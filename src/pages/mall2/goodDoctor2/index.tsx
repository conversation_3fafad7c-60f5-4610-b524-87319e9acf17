import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { useShare } from "@/stores/useShare";
import { showToast } from "@/utils/tools";
import {
  Button,
  Image,
  PageContainer,
  Text,
  Textarea,
  View,
} from "@tarojs/components";
import Taro, { useReachBottom, useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import dayjs from "dayjs";
import { useMemo } from "react";
import { Rate } from "@/components/Rate";
import { img_avatar } from "@/utils/images";
import RichContent from "@/components/RichContent";
import clsx from "clsx";
import { UnoIcon } from "@/components/UnoIcon";

const img_close = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241023/1298659849017233408.png`;
const img_avatar2 = `https://oss.gaomei168.com/file-release/20241119/1308468065374842880.png`;

const str2arr = (s = "") => s.split(",").filter((n) => n);
const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD") : "");

const rateList = [
  { size: 50, name: "总评分", key: "score" },
  { size: 36, name: "沟通", key: "communicateScore" },
  { size: 36, name: "技术", key: "technologyScore" },
  { size: 36, name: "审美", key: "estheticScore" },
];

const INIT = {
  score: 5,
  communicateScore: 5,
  technologyScore: 5,
  estheticScore: 5,
  content: "",
  anonymousTag: 0,
};

const useUpload = (props: { max: number }) => {
  const [state, setState] = useSetState({
    list: [] as any[],
  });

  const max = props.max ?? 1;

  const ex = useMemo(() => {
    const links = state.list
      .filter((n) => n.status === "success")
      .map((n) => n.url)
      .join(",");
    const loading = state.list.some((n) => n.status === "uploading");
    const showUpload = state.list.length < max;
    return { links, loading, showUpload };
  }, [state.list]);

  const upload = async () => {
    const count = max - state.list.length;
    if (count < 1) return;

    const res = await Taro.chooseMedia({
      mediaType: ["image"],
      sizeType: ["compressed"],
      count,
    });
    console.log(res);

    const list = res.tempFiles.map((n, idx) => ({
      uid: Date.now() + "-" + idx,
      path: n.tempFilePath,
      thumb: n.thumbTempFilePath,
      status: "uploading",
    }));

    setState((p) => ({ list: [...p.list, ...list] }));

    list.forEach(async (item) => {
      try {
        const res = await Taro.uploadFile({
          url: process.env.TARO_APP_UPLOAD ?? "",
          name: "file",
          filePath: item.path,
          header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
        });
        const obj = JSON.parse(res?.data);
        const url = obj?.data?.path ?? "";
        // await sleep(2e3);
        setState((p) => {
          const list = p.list.map((n) => {
            if (n.uid == item.uid) {
              return { ...n, url, status: "success" };
            }

            return n;
          });

          return { list };
        });
      } catch (err) {
        setState((p) => {
          const list = p.list.map((n) => {
            if (n.uid == item.uid) {
              return { ...n, status: "failed" };
            }

            return n;
          });

          return { list };
        });
        throw err;
      }
    });
  };

  const _delete = (uid: string) => {
    setState((p) => ({ list: p.list.filter((n) => n.uid != uid) }));
  };

  return [
    { ...state, ...ex },
    {
      upload,
      delete: _delete,
    },
  ] as const;
};

export default function Page() {
  const router = useRouter();
  const [fstate, fact] = useUpload({ max: 3 });

  const [state, setState] = useSetState({
    data: null as any,
    open: false,

    ...INIT,
  });

  useShare(() => {
    return {
      title: "搞美好医生 - " + state.data?.name,
      imageUrl: state.data?.coverImage,
      path: `/pages/mall2/goodDoctor2/index?id=${state.data?.id}`,
    };
  });

  useMount(() => {
    fetchDoc();
    listAct.getList();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  const fetchDoc = async () => {
    const id = router.params.id;
    const res = await CusApi.getDoctorInfo({ id });
    res._adeptInfo = str2arr(res?.adeptInfo);
    setState({ data: res });
  };

  const [list, listAct] = useList({
    api: CusApi.getDocComment,
    params: { doctorId: router.params.id },
  });

  const openCom = () => {
    const token = Taro.getStorageSync("token");

    if (!token) {
      return Taro.navigateTo({ url: `/pages/mall/login/index` });
    }

    setState({ ...INIT, open: true });
  };

  const closeCom = () => {
    setState({ open: false });
  };

  const submit = async () => {
    const data = {
      doctorId: router.params.id,
      score: state.score,
      communicateScore: state.communicateScore,
      technologyScore: state.technologyScore,
      estheticScore: state.estheticScore,
      content: state.content,
      images: fstate.links,
      anonymousTag: state.anonymousTag,
    };

    if (!data.content) return showToast("请填写留言内容");

    Taro.showLoading({ title: "提交中...", mask: true });
    await CusApi.addDocComment(data);
    Taro.showToast({ title: "提交成功", icon: "success" });
    listAct.reload();
    fetchDoc();
    setState({ open: false });
  };

  return (
    <View className="bg-#F6F6F6">
      <View className="pos-relative h-700 mt-30">
        <Image
          className="pos-absolute top-0 right-0 w-354 h-627"
          src="https://oss.gaomei168.com/file-release/20250312/1349331034557546496_712_1258.png"
        />

        <View className="pos-absolute top-30 left-15 w-425 h-633 bg-no-repeat bg-center bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250312/1349324012525092864_850_1266.png)]">
          <Image
            className="pos-absolute top-35 left-42 w-342 h-500 bg-#F6F6F6"
            mode="aspectFill"
            src={state.data?.coverImage}
          />
        </View>

        <View className="pos-absolute top-70 right-0 w-322 h-500 box-border flex items-center justify-center">
          <View
            className={clsx(
              "transition-all duration-500 delay-500 translate-x-1/1 opacity-0",
              !!state.data?.id && "!translate-x-0 !opacity-100 "
            )}
          >
            <View className="text-(36 #11100F) fw-bold">
              <Text>{state.data?.name}</Text>
              <Text className="italic fw-normal ml-10 text-26">
                {state.data?.alias}
              </Text>
            </View>
            <View className="text-(24 #11100F) fw-bold mt-20 mb-60">
              搞美严选医生
            </View>
            {state.data?._adeptInfo.map((n) => (
              <View key={n} className="text-(20 #707070) mt-15">
                {n}
              </View>
            ))}
            <View className="text-(20 #11100F) mt-40">
              执业编号：{state.data?.practiceNo}
            </View>
          </View>
        </View>
      </View>

      <View className="text-(36 #11100F) fw-bold mt-70 pl-50">医师资质</View>
      <View className="text-(20 #11100F) mt-15 pl-50">
        PHYSICIAN QUALIFICATIONS
      </View>
      <View className="px-50 py-40 text-(26/50 #716F70)">
        <RichContent html={state.data?.personalInformation} />
      </View>

      <View>
        <View className="text-(32 #11100F) fw-bold pb-40 px-35">诊后热评</View>
        <View className="">
          {list.list.map((item) => {
            const anon = !!item.anonymousTag;
            const nick = anon ? "****" : item.appUser?.name;
            const avatar = anon
              ? img_avatar2
              : item.appUser?.photo || img_avatar;

            return (
              <View className="px-35 pb-30" key={item.id}>
                <View className="flex py-20 items-center">
                  <Image
                    className="size-74 bg-#666 rounded-full mr-10"
                    mode="aspectFill"
                    src={avatar}
                  />
                  <View className="flex-1 min-w-0 flex flex-col justify-center">
                    <View className="flex items-center">
                      <View className="text-(26 #11100F)">{nick}</View>
                      {/* <View className="text-(16/22 #fff) h-22 px-5 rounded-3 bg-#D2AD7A ml-10">
                  搞美VIP
                </View> */}
                    </View>
                    <View className="text-(20 #787777) mt-5">
                      {fdate(item.createDate)}
                    </View>
                  </View>
                  <View>
                    <Rate value={item.score} size={22} gap={3} disabled />
                  </View>
                </View>
                <View className="text-(24 #11100F) pl-70 pr-50">
                  {item.content || "--"}
                </View>
                <View className="flex flex-wrap px-70 -ml-20 pt-10">
                  {str2arr(item.images).map((n, k) => (
                    <View
                      key={k}
                      className="pos-relative size-140 ml-20 mt-20 bg-#f5f5f5"
                      onClick={() => {
                        Taro.previewImage({
                          current: n,
                          urls: str2arr(item.images),
                        });
                      }}
                    >
                      <Image
                        className="pos-absolute top-0 left-0 size-full"
                        mode="aspectFill"
                        src={n}
                      />
                    </View>
                  ))}
                </View>
              </View>
            );
          })}
          <View className="text-(24 #999 center) py-30">
            {list.more ? "加载中..." : "没有更多数据了"}
          </View>
        </View>

        <View className="h-90 py-30"></View>

        <View className="z-50 pos-fixed left-0 bottom-0 w-full flex box-border px-60 bg-#fff py-25">
          <View className="pos-relative flex-1 h-90 rounded-full bg-#C9C9C9 text-(36/90 #fff center) mr-30 last:mr-0">
            <Button
              openType="share"
              className="pos-absolute top-0 left-0 size-full opacity-0"
            ></Button>
            转发分享
          </View>
          <View
            className="pos-relative flex-1 h-90 rounded-full bg-#B7684A text-(36/90 #fff center) mr-30 last:mr-0"
            onClick={openCom}
          >
            立即评价
          </View>
        </View>
      </View>

      <PageContainer
        show={state.open}
        onLeave={closeCom}
        customStyle="background: transparent;"
      >
        <View
          className="bg-#f4f4f4"
          style={{ borderRadius: "70rpx 70rpx 0 0" }}
        >
          <View className="flex items-center text-(37 #11100F) px-50 pt-40 pb-20">
            <View className="flex-1">写评价</View>
            <Image
              className="size-36"
              mode="aspectFit"
              src={img_close}
              onClick={closeCom}
            />
          </View>
          <View
            className="mx-20 bg-#fff px-35 pt-50 pb-50"
            style={{ borderRadius: "20rpx 20rpx 0 0" }}
          >
            {rateList.map((item) => {
              return (
                state.open && (
                  <View className="flex items-center py-8" key={item.key}>
                    <View className="text-(28 #11100F) w-150">{item.name}</View>
                    <View>
                      <Rate
                        value={state[item.key]}
                        size={item.size}
                        onChange={(e) => setState({ [item.key]: e } as any)}
                      />
                    </View>
                  </View>
                )
              );
            })}

            <View className="pos-relative pt-30 pb-50 h-150">
              <Textarea
                className="block size-full text-(24 #000)"
                placeholder="您的参与将决定搞美好医生的评分哦~"
                maxlength={200}
                value={state.content}
                onInput={(e) => setState({ content: e.detail.value })}
              />
              <View className="pos-absolute bottom-0 right-0 text-(22 #B2B2B2)">
                {state.content.length} / 200
              </View>
            </View>

            <View className="flex flex-wrap -ml-20 py-30">
              {fstate.list.map((item) => (
                <View
                  className="size-170 bg-#eee ml-20 pos-relative overflow-hidden"
                  key={item.uid}
                >
                  <Image
                    className="size-full block"
                    mode="aspectFill"
                    src={item.path || item.url}
                  />
                  <View
                    className={clsx(
                      "pos-absolute top-0 left-0 size-full bg-#000/80 flex flex-col items-center justify-center transition-all invisible opacity-0",
                      item.status == "uploading" &&
                        "uno-layer-z1-(visible opacity-100)"
                    )}
                  >
                    <UnoIcon className="i-icon-park-outline:loading-four size-48 text-#fff animate-spin" />
                    <View className="text-(26 #fff) mt-10">上传中...</View>
                  </View>
                  <View
                    className="pos-absolute top-0 right-0 bg-#000 size-36 flex items-center justify-center"
                    onClick={() => fact.delete(item.uid)}
                  >
                    <UnoIcon className="i-icon-park-outline:close-small size-30 text-#fff" />
                  </View>
                </View>
              ))}
              {fstate.showUpload && (
                <View
                  className="size-170 bg-#eee ml-20 pos-relative flex flex-col items-center justify-center"
                  onClick={fact.upload}
                >
                  <UnoIcon className="i-icon-park-outline:camera size-48 text-#666" />
                  <View className="text-(26 #666) mt-10">上传</View>
                </View>
              )}
            </View>

            <View className="flex">
              <View
                className="flex items-center"
                onClick={() =>
                  setState({ anonymousTag: Number(!state.anonymousTag) })
                }
              >
                {state.anonymousTag == 1 ? (
                  <UnoIcon className="i-icon-park-outline:check-one size-32 text-#999 mr-15" />
                ) : (
                  <UnoIcon className="i-icon-park-outline:round size-32 text-#999 mr-15" />
                )}

                <View className="text-(26 #666)">匿名评价</View>
              </View>
              <View
                className="w-407 h-90 bg-#AD7F6D rounded-18 text-(30/90 center #fefefe) ml-auto"
                onClick={submit}
              >
                确认发布
              </View>
            </View>
          </View>
        </View>
      </PageContainer>
    </View>
  );
}
