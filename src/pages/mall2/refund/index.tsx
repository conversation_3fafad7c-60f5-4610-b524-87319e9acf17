import { CusUpload } from "@/components/Custom/Upload";
import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { Image, Textarea, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useSetState } from "ahooks";
import clsx from "clsx";

const list = [
  { id: 1, name: "商品等信息错误" },
  { id: 2, name: "重复下单/误下单" },
  { id: 3, name: "不想要了" },
  { id: 9, name: "其它" },
];

export default () => {
  const router = useRouter();
  const [state, setState] = useSetState({
    cancelType: 0,
    cancelDesc: "",

    imgs: "",
  });

  const handleRefund = async () => {
    const data = {
      orderId: router.params.id,
      cancelType: state.cancelType,
      cancelDesc: state.cancelDesc,
      cancelImage: state.imgs,
    };

    if (!data.cancelType) return showToast("请选择退款原因");
    Taro.showLoading({ title: "处理中" });
    await CusApi.putOrderRefund(data);
    await showToast("操作成功");
    Taro.navigateBack();
    Taro.eventCenter.trigger("order.reload");
  };

  return (
    <View className="p-25">
      <View className="bg-#fff rounded-20 px-20 py-30">
        <View className="flex py-20">
          <View className="text-(30 #000) w-160 fw-bold">退款原因</View>
          <View className="flex-1 min-w-0">
            {list.map((item) => (
              <View
                key={item.id}
                className="flex items-center pb-20"
                onClick={() => setState({ cancelType: item.id })}
              >
                <Image
                  className="size-34 mr-20"
                  mode="aspectFit"
                  src={
                    state.cancelType === item.id
                      ? require("src/assets/ck-2.png")
                      : require("src/assets/ck-1.png")
                  }
                ></Image>
                <View
                  className={clsx(
                    "text-(30 #999)",
                    state.cancelType === item.id && "!text-#333 fw-bold"
                  )}
                >
                  {item.name}
                </View>
              </View>
            ))}
          </View>
        </View>
        <View className="flex py-20">
          <View className="text-(30 #000) w-160 fw-bold">图片描述</View>
          <View className="flex-1 min-w-0">
            <CusUpload max={4} onChange={(e) => setState({ imgs: e })} />
            {/* <Uploader
              url={process.env.TARO_APP_UPLOAD}
              multiple
              maxCount={4}
              value={state.imgs}
              onChange={(e) => setState({ imgs: [...e.fileList] })}
              onDelete={(_, f) => setState({ imgs: [...f] })}
            ></Uploader> */}
          </View>
        </View>
        <View className="flex py-20">
          <View className="text-(30 #000) w-160 fw-bold">退款备注</View>
          <View className="flex-1 min-w-0">
            <Textarea
              className="text-(30 #333) block w-full h-200"
              placeholder="请输入退款备注"
              value={state.cancelDesc}
              onInput={(e) => setState({ cancelDesc: e.detail.value })}
            ></Textarea>
          </View>
        </View>
      </View>

      <View
        className="h-80 rounded-full bg-#ad7f6d text-(28/80 #fff center) my-100"
        onClick={handleRefund}
      >
        确认退款
      </View>
    </View>
  );
};
