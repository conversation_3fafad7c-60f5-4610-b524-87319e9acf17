import CusApi from "@/services/CusApi";
import { Image, Swiper, SwiperItem, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { useMemo } from "react";

const img_bg = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241021/1297919378473095168.png`;
const img_bg2 = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241021/1297923195423952896.png`;
const img_arr1 = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241021/1297926118333747200.png`;
const img_arr2 = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241021/1297926118321164288.png`;

export default function CardCollect2() {
  const [state, setState] = useSetState({
    exlist: [] as any[],
    active: 0,
  });

  useMount(() => {
    fetchExList();
  });

  const crtItem = useMemo(() => {
    return state.exlist?.[state.active] || null;
  }, [state.exlist, state.active]);

  const fetchExList = async () => {
    const res = await CusApi.getMarketProduct({
      marketType: "20",
      pageSize: 9999,
    });
    const list = res?.list || [];
    setState({ exlist: list });
  };

  const handleExChange = async () => {
    Taro.showLoading({ title: "兑换中", mask: true });
    const item = state.exlist?.[state.active];
    const order = await CusApi.addMarketOrder({
      productList: [
        {
          marketId: item?.id,
          totalCount: 1,
        },
      ],
    });
    await CusApi.payOrderZero({ orderId: order?.id });
    Taro.hideLoading();
    const md = await Taro.showModal({
      title: "兑换成功",
      showCancel: false,
      confirmText: "返回",
    });

    if (md.confirm) {
      Taro.navigateBack();
      // Taro.eventCenter.trigger("card.collect.reload");
    }
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <Image className="w-full h-217 -mb-217" mode="scaleToFill" src={img_bg} />
      <View className="text-(80 #8E4E36 center) pt-90">恭喜你集齐四卡</View>
      <View className="text-(34 #8E4E36 center) pt-10 font-italic">
        可兑换【{crtItem?.name || "--"}】
      </View>

      <View className="h-700 pos-relative">
        <Image
          className="pos-absolute z-5 top-1/2 left-55 size-38"
          mode="aspectFit"
          src={img_arr1}
          onClick={() =>
            setState((p) => ({
              active: Math.abs(
                (p.active - 1 + p.exlist.length) % p.exlist.length
              ),
            }))
          }
        />
        <Image
          className="pos-absolute z-5 top-1/2 right-55 size-38"
          mode="aspectFit"
          src={img_arr2}
          onClick={() =>
            setState((p) => ({
              active: (p.active + 1) % p.exlist.length,
            }))
          }
        />
        <Swiper
          className="h-full"
          indicatorDots={false}
          circular
          current={state.active}
          onChange={(e) => setState({ active: e.detail.current })}
        >
          {state.exlist.map((item) => (
            <SwiperItem className="h-full" key={item.id}>
              <View className="h-full pos-relative flex flex-col justify-center items-center">
                <Image
                  className="pos-relative z-2 size-460 block mx-auto"
                  mode="aspectFit"
                  src={item.coverImage}
                />
                <View
                  className="w-573 h-223 bg-no-repeat bg-contain mx-auto -mt-120"
                  style={{ backgroundImage: `url(${img_bg2})` }}
                >
                  <View className="text-(34 #B7684A center) pt-140">
                    · {item.name} ·
                  </View>
                </View>
              </View>
            </SwiperItem>
          ))}
        </Swiper>
      </View>
      <View className="text-(20 #AEAEAE center)">滑动查看更多可兑换礼品</View>
      <View
        className="bg-#B7684A w-343 h-86 rounded-full text-(43/86 #fff center) mx-auto mt-35"
        onClick={handleExChange}
      >
        点击兑换
      </View>
      <View className="text-(20 #AEAEAE center) mt-35">
        *本活动最终解释权归搞美医疗所有
      </View>
    </View>
  );
}
