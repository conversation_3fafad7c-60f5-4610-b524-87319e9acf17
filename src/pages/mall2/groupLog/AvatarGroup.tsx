// import { UnoIcon } from "@/components/UnoIcon";
import { img_avatar } from "@/utils/images";
import { Image, View } from "@tarojs/components";

export const AvatarGroup = (props: { list?: any[] }) => {
  return (
    <View className="flex items-center justify-center gap-30">
      {props.list?.slice(0, 3).map((item, idx) => (
        <View className="pos-relative" style={{ zIndex: 1 + idx }} key={idx}>
          <Image
            className="block size-100 bg-#eee rounded-full"
            mode="aspectFill"
            src={item.photo || img_avatar}
          ></Image>
          {!idx && (
            <View className="pos-absolute bottom-0 left-1/2 -translate-x-1/2 bg-#8E4E36 rounded-14 px-15 py-5 ws-nowrap text-(20 #fff)">
              团长
            </View>
          )}
        </View>
      ))}

      {(props.list?.length || 0) > 3 && (
        <View className="text-(35 #999) px-10">…</View>
      )}

      {/* <View className="z-10 -ml-10 size-100 bg-#eee rounded-full box-border b-(2 dashed #8E4E36) flex items-center justify-center">
        <UnoIcon className="i-icon-park-outline:plus size-30 text-#8E4E36" />
      </View> */}
    </View>
  );
};
