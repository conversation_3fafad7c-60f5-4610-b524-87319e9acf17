import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { Image, ScrollView, View } from "@tarojs/components";
import { useMount, useSetState } from "ahooks";
import { GroupInfo } from "./GroupInfo";

const GroupState = [
  { id: 10, name: "拼团中" },
  { id: 20, name: "拼团成功" },
  { id: 30, name: "拼团失败" },
];

export default function Page() {
  const [state, setState] = useSetState({
    info: null as any,
    infoOpen: false,
  });

  const [list, listAct] = useList({
    api: CusApi.getGroup,
    params: {},
  });

  useMount(() => {
    listAct.reload();
  });

  return (
    <View className="h-100vh flex flex-col bg-#F7F4EF">
      <ScrollView
        scrollY
        className="flex-1 min-h-0"
        onScrollToLower={() => listAct.getNext()}
      >
        <View className="px-30 pt-30">
          {list.list.map((item) => (
            <View
              key={item.id}
              className="flex py-25 px-20 bg-#FDFCFA rounded-10 mt-30 first:mt-0"
            >
              <Image
                className="size-180 bg-#F6F3EE mr-30 rounded-10"
                mode="aspectFill"
                src={item.productCoverImage}
              />
              <View className="flex-1 min-w-0 flex flex-col pt-5">
                <View className="text-(30  #333) line-clamp-1">
                  {item.productName}
                </View>
                <View className="flex items-center mt-10">
                  <View className="text-(20 #666) pr-20">
                    {GroupState.find((n) => n.id == item.state)?.name}
                  </View>
                  {/* <View className="box-border size-40 bg-#eee rounded-full mr-5"></View>
                  <View className="box-border size-40 bg-#eee rounded-full mr-5"></View>
                  <View className="box-border size-40 bg-#eee rounded-full mr-5"></View>
                  <View className="box-border size-40 bg-#eee rounded-full mr-5"></View>
                  <View className="box-border size-40 bg-#eee rounded-full mr-5"></View>
                  <View className="box-border size-40 bg-#eee rounded-full mr-5 b-(2 dashed #ddd) flex items-center justify-center">
                    <UnoIcon className="i-icon-park-outline:plus size-20 text-#ddd" />
                  </View> */}
                </View>

                <View className="mt-a flex items-center">
                  <View className="flex-1 min-w-0 flex items-center">
                    <View className="text-(50 #000) fw-bold first-letter:(text-25 mr-5)">
                      &yen;{item.productPrice}
                    </View>
                    <View className="text-(20/25 #333) ml-8">
                      <View>拼团价</View>
                      <View className="line-through text-#666">
                        原价{item.product?.salePrice}
                      </View>
                    </View>
                  </View>
                  <View
                    className="text-(24 #fff) bg-#11100E rounded-full px-20 py-8"
                    onClick={() => setState({ info: item, infoOpen: true })}
                  >
                    查看详情
                  </View>
                </View>
              </View>
            </View>
          ))}

          <View className="py-50 text-(24 #999 center)">没有更多数据了</View>
        </View>
      </ScrollView>

      <GroupInfo
        open={state.infoOpen}
        onClose={() => setState({ infoOpen: false })}
        data={state.info}
      ></GroupInfo>
    </View>
  );
}
