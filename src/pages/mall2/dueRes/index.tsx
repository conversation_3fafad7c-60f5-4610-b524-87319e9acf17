import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { fdate } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import dayjs from "dayjs";

export default function Page() {
  const router = useRouter();
  const [state, setState] = useSetState({ data: null as any });

  useMount(() => {
    fetchDetail();
  });

  const fetchDetail = async () => {
    const id = router.params.id;
    const res = await CusApi.getDueInfo({ id });
    setState({ data: res });
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF px-30">
      <View className="pt-150">
        <View className="box-border px-45 w-660 mx-a rounded-20 overflow-hidden bg-#fff bg-[linear-gradient(0deg,rgba(226,199,171,0.7),rgba(255,255,255,0.7))]">
          <View className="flex items-center justify-center pt-60">
            <UnoIcon className="i-icon-park-outline:check-one size-60 text-#8E4E36 mr-30" />
            <View className="text-(56 #8E4E36)">预约成功</View>
          </View>

          <View className="flex px-15 pt-70 pb-20">
            <View className="w-155 text-(33 #636363)">预约门店</View>
            <View className="flex-1 min-w-0">
              <View className="text-(30 #1A1A1A) fw-bold">
                {state.data?.shopName}
              </View>
              <View className="mt-5 text-(24 #636363)">
                {state.data?.shop?.address}
              </View>
            </View>
          </View>

          <View className="b-b-(1 solid #ccc/30)"></View>

          <View className="flex px-15 pt-70 pb-20">
            <View className="w-155 text-(33 #636363)">预约项目</View>
            <View className="flex-1 min-w-0">
              <View className="text-(30 #1A1A1A) fw-bold">
                {state.data?.typeName}
              </View>
            </View>
          </View>

          <View className="b-b-(1 solid #ccc/30)"></View>

          <View className="flex px-15 pt-70 pb-20">
            <View className="w-155 text-(33 #636363)">预约日期</View>
            <View className="flex-1 min-w-0">
              <View className="text-(30 #1A1A1A) fw-bold">
                {fdate(state.data?.preTime, "YYYY-MM-DD HH:mm")}
              </View>
            </View>
          </View>

          <View className="b-b-(1 solid #ccc/30)"></View>

          <View className="flex px-15 pt-70 pb-20">
            <View className="w-155 text-(33 #636363)">预约备注</View>
            <View className="flex-1 min-w-0">
              <View className="text-(30 #1A1A1A) fw-bold">
                {state.data?.content}
              </View>
            </View>
          </View>

          <View className="b-b-(1 solid #ccc/30)"></View>

          <Image
            className="block w-330 h-88 mx-auto mt-35 mb-20"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20241114/1306644805427408896.png"
          />
        </View>
      </View>

      <View className="py-75">
        <View
          className="w-690 h-90 bg-#8E4E36 rounded-20 text-(38/90 center #fff) b-(1 solid #8E4E36) mt-15"
          onClick={() => {
            Taro.redirectTo({ url: `/pages/mall2/dueList/index` });
          }}
        >
          查看预约
        </View>
        <View
          className="w-690 h-90 bg-#F7F4EF rounded-20 text-(38/90 center #8E4E36) b-(1 solid #8E4E36) mt-15"
          onClick={() => {
            Taro.navigateBack();
          }}
        >
          完成
        </View>
      </View>
    </View>
  );
}
