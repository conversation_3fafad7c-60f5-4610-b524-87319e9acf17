import Watermark from "@/components/Watermark";
import CusApi from "@/services/CusApi";
import { formatDis, showToast } from "@/utils/tools";
import { Image, Swiper, SwiperItem, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useRequest, useSetState } from "ahooks";

const img_block = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241022/1298314441963343872.png`;

export default () => {
  const router = useRouter();
  const str2arr = (str) => (str || "").split(",").filter((n) => n);
  const [state, setState] = useSetState({
    shop: {} as any,
    advs: [] as any[],
  });

  useMount(() => {
    fetchShop();
  });

  const fetchShop = async () => {
    const id = router.params.id;
    const pos = Taro.getStorageSync("pos");
    const res = await CusApi.getShopInfo({
      id,
      latitude: pos?.latitude,
      longitude: pos?.longitude,
    });

    res.dis = formatDis(res.distance);
    setState({ shop: res, advs: str2arr(res.showImages) });
  };

  const docRes = useRequest(CusApi.getDoctor, {
    defaultParams: [{ shopId: router.params.id, pageSize: 9999 }],
    ready: !!router.params.id,
  });
  const docs = docRes.data?.list || [];

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="h-500 -mb-40">
        <Swiper className="size-full">
          {state.advs.map((item, idx) => (
            <SwiperItem
              className="size-full"
              key={idx}
              onClick={() => {
                Taro.previewImage({
                  current: item,
                  urls: state.advs,
                });
              }}
            >
              <Image
                className="block size-full"
                mode="aspectFill"
                src={item}
              ></Image>
            </SwiperItem>
          ))}
        </Swiper>
      </View>

      <View className="pos-relative px-25">
        <View className="bg-#fafafa rounded-20 p-30 mb-20">
          <View className="flex">
            <Image
              className="size-110 bg-#f5f5f5 rounded-10 overflow-hidden"
              mode="aspectFill"
              src={state.shop.logo}
            ></Image>
            <View className="flex-1 min-w-0 flex flex-col justify-between py-5 px-20">
              <View className="flex items-center">
                <View className="flex-1 min-w-0 text-(33 #000)">
                  {state.shop.name}
                </View>
                <View className="w-3 h-27 bg-#eee"></View>
                <Image
                  className="size-32 ml-30"
                  mode="aspectFit"
                  src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238077034488242176_size_4679.png"
                  onClick={(evt) => {
                    evt.stopPropagation();
                    Taro.makePhoneCall({
                      phoneNumber: state.shop.contactPhone,
                    });
                  }}
                ></Image>
              </View>
              <View className="flex items-center">
                {str2arr(state.shop.mainBusiness).map((item, idx) => (
                  <View
                    key={idx}
                    className="text-(24 #fff) bg-#ad7f6d rounded-10 px-15 py-5 mr-8"
                  >
                    {item}
                  </View>
                ))}
              </View>
            </View>
          </View>

          <View
            className="flex items-center pt-20"
            onClick={async (evt) => {
              evt.stopPropagation();
              Taro.openLocation({
                latitude: state.shop.latitude,
                longitude: state.shop.longitude,
                scale: 18,
                name: state.shop.name,
                address: state.shop.fullAddress,
                fail: () => showToast("打开地址失败"),
              });
            }}
          >
            <Image
              className="size-26 shrink-0"
              mode="aspectFit"
              src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238077259869167616_size_2330.png"
            ></Image>
            <View className="text-(26 #666) px-10">
              {state.shop.fullAddress}
            </View>
            <Image
              className="size-14 shrink-0"
              mode="aspectFit"
              src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238076800756457473_size_464.png"
            ></Image>
          </View>

          <View className="flex items-center pt-10">
            <Image
              className="size-26 shrink-0 invisible"
              mode="aspectFit"
              src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238077259869167616_size_2330.png"
            ></Image>
            <View className="text-(26 #666) px-10">
              距离当前位置：{state.shop.dis}
            </View>
          </View>
        </View>
      </View>

      <View className="px-25 -mt-20">
        <View className="flex items-center h-110">
          <View className="text-(34 #000) flex-1">医生团队</View>
          <View className="text-(25 #aaa)">专业 | 透明 | 品质</View>
        </View>

        <View className="px-20 grid cols-1 gap-50 pt-25">
          {docs.map((item) => (
            <View
              key={item.id}
              className="pos-relative bg-#fafafa rounded-40 pl-100 pr-220 pt-40 box-border min-h-245"
              // onClick={() => {
              //   Taro.preload(item);
              //   Taro.navigateTo({
              //     url: `/pages/mall2/doctorInfo/index?id=${item.id}&shopId=${router.params.id}`,
              //   });
              // }}
              onClick={() => {
                Taro.navigateTo({
                  url: `/pages/mall2/goodDoctor2/index?id=${item.id}`,
                });
              }}
            >
              <Image
                className="pos-absolute -top-33 -left-5 size-100 bg-#8C4D35 b-(1 solid #8C4D35) rounded-full overflow-hidden"
                mode="aspectFill"
                src={item.headPicture}
              />
              <View className="text-(42 #905139) fw-bold">{item.name}</View>
              <View className="text-(27 #343434) mt-10">{item.jobTitle}</View>
              <View className="flex flex-wrap -ml-15 pt-5 pb-20">
                {str2arr(item.adeptInfo).map((n, i) => (
                  <View
                    key={i}
                    className="h-30 rounded-10 b-(1 dashed #AD7F6D) px-10 text-(18/30 #1D1D1D center) mt-15 ml-15"
                  >
                    {n}
                  </View>
                ))}
              </View>

              <View className="pos-absolute top-38 right-53 w-142 h-154">
                <Image
                  className="pos-absolute top-0 left-0 size-full"
                  mode="scaleToFill"
                  src={img_block}
                />

                <View className="pos-absolute top-12 -left-38 flex flex-col text-(16 #666 center)">
                  <View>沟通</View>
                  <View>{item.communicateScore}</View>
                </View>
                <View className="pos-absolute top-12 -right-38 flex flex-col text-(16 #666 center)">
                  <View>技术</View>
                  <View>{item.technologyScore}</View>
                </View>
                <View
                  className="pos-absolute left-1/2 flex flex-col text-(16 #666 center) -translate-x-1/2"
                  style={{ top: "calc(100% + 12rpx)" }}
                >
                  <View>审美</View>
                  <View>{item.estheticScore}</View>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>

      <Watermark />

      <View className="h-200"></View>
    </View>
  );
};
