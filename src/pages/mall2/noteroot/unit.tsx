import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { img_avatar } from "@/utils/images";
import { Button, Image, ScrollView, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState, useUpdateEffect } from "ahooks";
import clsx from "clsx";
import dayjs from "dayjs";
import { useMemo } from "react";

export const NoteCate = (props: {
  type?: string;
  onChange?: (n: string) => any;
}) => {
  const [state, setState] = useSetState({
    open: false,
    cate: null as any,
    cates: [] as any[],
  });

  useMount(() => {
    fetchList();
  });

  const fetchList = async () => {
    const res = await CusApi.getNoteCates({ pageSize: 9999, type: props.type });
    const cates = res?.list || [];
    const cate = cates?.[0] || null;
    setState({ cates, cate });
  };

  useUpdateEffect(() => {
    props.onChange?.(state.cate?.id);
  }, [state.cate?.id]);

  return (
    <View className="h-70 overflow-hidden pos-relative bg-#fff">
      <ScrollView
        className="w-full h-130"
        scrollX
        scrollIntoView={"cate-" + state.cate?.id}
        scrollWithAnimation={true}
      >
        <View className="h-70 ws-nowrap text-0 pl-40">
          {state.cates.map((item) => (
            <View
              key={item.id}
              className="inline-flex h-full ws-normal pr-60 text-(28 #999) items-center pos-relative"
              onClick={() => setState({ cate: item })}
            >
              <View
                className="pos-absolute top-0 -left-200 size-0"
                id={`cate-` + item.id}
              ></View>
              <View
                className={clsx({
                  "text-#8E4E36": state.cate?.id == item.id,
                })}
              >
                {item.name}
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
      <View className="pos-absolute top-0 right-0 w-80 h-full pointer-events-none bg-[linear-gradient(90deg,rgba(255,255,255,0),rgba(255,255,255,1))]"></View>
    </View>
  );
};

export const NavBar = (props: { data: any; share?: boolean }) => {
  const [state, setState] = useSetState({
    userVip: null as any,
  });

  const unit = useMemo(() => {
    const win = Taro.getWindowInfo();
    const sh = win.statusBarHeight || 0;
    const rect = Taro.getMenuButtonBoundingClientRect();
    const nh = rect.height + (rect.top - sh) * 2;
    const pr = win.windowWidth - rect.left;
    return { sh, nh, pr };
  }, []);

  useMount(() => {
    fetchUserVip();
  });

  const fetchUserVip = async () => {
    // 判断本地是否有token，只有在有token的情况下才请求用户VIP信息
    const token = Taro.getStorageSync("token");
    if (token) {
      try {
        const userVip = await CusApi.getUserInfo();
        setState({ userVip });
      } catch (error) {
        console.log("获取用户VIP信息失败", error);
      }
    }
  };

  const fdate = (n: string | number | Date | null | undefined): string =>
    n ? dayjs(n).format("YYYY-MM-DD") : "--";

  return (
    <View className="bg-#fff">
      <View className="" style={{ height: unit.sh }}></View>
      <View
        className="flex items-center"
        style={{ height: unit.nh, marginRight: unit.pr }}
      >
        <View
          className="px-20"
          onClick={() => {
            Taro.navigateBack({
              fail: () => Taro.reLaunch({ url: `/pages/tabs/mall/index` }),
            });
          }}
        >
          <UnoIcon className="i-icon-park-outline:left size-40 c-#333" />
        </View>
        <View
          className="flex-1 flex items-center"
          onClick={() => {
            // 只有当用户不是NomalUser时才允许跳转
            if (state.userVip && state.userVip.vipLevelCode !== "NomalUser") {
              Taro.navigateTo({
                url: `/pages/user/user/index?id=${props.data?.appUserId}`,
              });
            }
            // 如果是普通用户，点击不做任何操作
          }}
        >
          <Image
            // style={{ height: unit.nh, width: unit.nh }}
            className="rounded-full overflow-hidden bg-#666 size-65"
            mode="aspectFill"
            src={props.data?.appUser?.photo || img_avatar}
          />
          <View className="flex-1 min-w-0 px-15">
            <View className="text-(28 #11100F) line-clamp-1 fw-bold">
              {props.data?.appUser?.name}
            </View>
            <View className="text-(22 #5e5e5e) mt-5">
              发布于: {fdate(props.data?.createDate)}
            </View>
          </View>
        </View>
        {props.share !== false && (
          <View className="px-20 pos-relative">
            <UnoIcon className="i-icon-park-outline:share size-40 c-#333" />
            <Button
              className="pos-absolute top-0 left-0 size-full opacity-0"
              openType="share"
            />
          </View>
        )}
      </View>
      <View className="h-20"></View>
    </View>
  );
};
