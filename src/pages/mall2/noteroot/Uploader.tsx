import { UnoIcon } from "@/components/UnoIcon";
import { Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useSetState } from "ahooks";
import clsx from "clsx";
import { useEffect, useMemo } from "react";

export const useUpload = (props: { max: number; type?: "image" | "video" }) => {
  const [state, setState] = useSetState({
    list: [] as any[],
  });

  const max = props.max ?? 1;
  const type = props.type ?? "image";

  const ex = useMemo(() => {
    const links = state.list
      .filter((n) => n.status === "success")
      .map((n) => n.url)
      .join(",");
    const loading = state.list.some((n) => n.status === "uploading");
    const showUpload = state.list.length < max;
    return { links, loading, showUpload };
  }, [state.list]);

  const upload = async () => {
    const count = max - state.list.length;
    if (count < 1) return;

    const res = await Taro.chooseMedia({
      mediaType: [type],
      sizeType: ["compressed"],
      count,
    });
    // console.log(res);

    const list = res.tempFiles.map((n, idx) => ({
      uid: Date.now() + "-" + idx,
      path: n.tempFilePath,
      thumb: n.thumbTempFilePath || n.tempFilePath,
      status: "uploading",
    }));

    setState((p) => ({ list: [...p.list, ...list] }));

    list.forEach(async (item) => {
      try {
        const res = await Taro.uploadFile({
          url: process.env.TARO_APP_UPLOAD ?? "",
          name: "file",
          filePath: item.path,
          header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
        });
        const obj = JSON.parse(res?.data);
        const url = obj?.data?.path ?? "";
        // await sleep(2e3);
        setState((p) => {
          const list = p.list.map((n) => {
            if (n.uid == item.uid) {
              return { ...n, url, status: "success" };
            }

            return n;
          });

          return { list };
        });
      } catch (err) {
        setState((p) => {
          const list = p.list.map((n) => {
            if (n.uid == item.uid) {
              return { ...n, status: "failed" };
            }

            return n;
          });

          return { list };
        });
        throw err;
      }
    });
  };

  const _delete = (uid: string) => {
    setState((p) => ({ list: p.list.filter((n) => n.uid != uid) }));
  };

  return [
    { ...state, ...ex },
    {
      upload,
      delete: _delete,
    },
  ] as const;
};

export const UploadImage = (props: {
  max: number;
  onChange?: (url: string) => any;
  className?: string;
  holder?: string;
}) => {
  const [fstate, fact] = useUpload({ max: props.max });

  useEffect(() => {
    props.onChange?.(fstate.links);
  }, [fstate.links]);

  return (
    <View className={clsx("grid cols-3 gap-20", props.className)}>
      {fstate.list.map((item) => (
        <View className="pos-relative pt-1/1 bg-#eee" key={item.uid}>
          <View className="pos-absolute top-0 left-0 size-full">
            <Image
              className="size-full block"
              mode="aspectFill"
              src={item.thumb || item.url}
            />

            <View
              className={clsx(
                "pos-absolute top-0 left-0 size-full bg-#000/80 flex flex-col items-center justify-center transition-all invisible opacity-0",
                item.status == "uploading" &&
                  "uno-layer-z1-(visible opacity-100)"
              )}
            >
              <UnoIcon className="i-icon-park-outline:loading-four size-48 text-#fff animate-spin" />
              <View className="text-(26 #fff) mt-10">上传中...</View>
            </View>

            <View
              className="pos-absolute top-0 right-0 bg-#000 size-36 flex items-center justify-center"
              onClick={() => fact.delete(item.uid)}
            >
              <UnoIcon className="i-icon-park-outline:close-small size-30 text-#fff" />
            </View>
          </View>
        </View>
      ))}
      {fstate.showUpload && (
        <View className="pos-relative pt-1/1 bg-#eee" onClick={fact.upload}>
          <View className="pos-absolute top-0 left-0 size-full">
            <View className="size-full flex flex-col items-center justify-center">
              <UnoIcon className="i-icon-park-outline:pic size-48 text-#666" />
              <View className="text-(22 #666) mt-5">
                {props.holder ?? "上传图片"}
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export const UploadVideo = (props: {
  max: number;
  onChange?: (url: string) => any;
  className?: string;
  holder?: string;
}) => {
  const [fstate, fact] = useUpload({ max: props.max, type: "video" });

  useEffect(() => {
    props.onChange?.(fstate.links);
  }, [fstate.links]);

  return (
    <View className={clsx("grid cols-3 gap-20", props.className)}>
      {fstate.list.map((item) => (
        <View className="pos-relative pt-1/1 bg-#eee" key={item.uid}>
          <View className="pos-absolute top-0 left-0 size-full">
            <Image
              className="size-full block"
              mode="aspectFill"
              src={item.thumb || item.url}
            />

            <View
              className={clsx(
                "pos-absolute top-0 left-0 size-full bg-#000/80 flex flex-col items-center justify-center transition-all invisible opacity-0",
                item.status == "uploading" &&
                  "uno-layer-z1-(visible opacity-100)"
              )}
            >
              <UnoIcon className="i-icon-park-outline:loading-four size-48 text-#fff animate-spin" />
              <View className="text-(26 #fff) mt-10">上传中...</View>
            </View>

            <View
              className="pos-absolute top-0 right-0 bg-#000 size-36 flex items-center justify-center"
              onClick={() => fact.delete(item.uid)}
            >
              <UnoIcon className="i-icon-park-outline:close-small size-30 text-#fff" />
            </View>
          </View>
        </View>
      ))}
      {fstate.showUpload && (
        <View className="pos-relative pt-1/1 bg-#eee" onClick={fact.upload}>
          <View className="pos-absolute top-0 left-0 size-full">
            <View className="size-full flex flex-col items-center justify-center">
              <UnoIcon className="i-icon-park-outline:video-two size-48 text-#666" />
              <View className="text-(22 #666) mt-5">
                {props.holder ?? "上传视频"}
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};
