import { Image, ScrollView, Text, Textarea, View } from "@tarojs/components";
import { useMount, useSetState } from "ahooks";
import { forwardRef, useEffect, useImperativeHandle } from "react";
import Taro from "@tarojs/taro";
import { useList } from "@/stores/useList";
import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { img_avatar } from "@/utils/images";
import dayjs from "dayjs";
import { UnoIcon } from "@/components/UnoIcon";
import { PopUp } from "@/components/Custom/PopUp2";

export const NoteComments = forwardRef(
  (props: { aid: string; num?: any }, ref) => {
    const [state, setState] = useSetState({
      ropen: false,
      rfor: null as any,

      subs: {} as any,
    });

    const [list, listAct] = useList({
      api: CusApi.getNComment,
      params: { articleId: props.aid, layer: 1 },
    });

    useImperativeHandle(ref, () => {
      return {
        openReply,
        closeReply,
        getNext: listAct.getNext,
      };
    });

    useMount(() => {
      listAct.reload();
    });

    // useReachBottom(() => {
    //   listAct.getNext();
    // });

    const openSub = async (item: any) => {
      setState((p) => ({
        subs: { ...p.subs, [item.id]: { ...p.subs?.[item.id], loading: true } },
      }));
      const data = {
        articleId: props.aid,
        layer: 2,
        topCommentsId: item.id,
        pageSize: 9999,
      };

      try {
        const res = await CusApi.getNComment(data);
        const list = res?.list || [];
        setState((p) => ({
          subs: { ...p.subs, [item.id]: { loading: false, list: [...list] } },
        }));
      } catch (err) {
        setState((p) => ({
          subs: { ...p.subs, [item.id]: { loading: false, list: null } },
        }));
        throw err;
      }
    };

    const onReplyOk = (res) => {
      listAct.reload();

      const item = res.reply;
      if (!item) return;

      let tid = item.topCommentsId || item.id;
      if (!tid) return;

      openSub({ id: tid });
    };

    const openReply = (rfor: any = null) => {
      setState({ rfor, ropen: true });
    };

    const closeReply = () => {
      setState({ ropen: false });
    };

    const fdate = (n) => (n ? dayjs(n).format("YYYY-MM-DD") : "--");

    return (
      <View className="b-t-(1 solid #ccc/30)">
        <View className="text-(26 #666) pt-20">共 {props.num} 条评论</View>
        {list.list.map((item) => {
          const sub = state.subs?.[item.id]?.list;
          const subs = sub || item.childList;
          const subLoading = state.subs?.[item.id]?.loading;
          const showOpen = !sub && item.childCommentsNum > 1;
          const showSub = !!item.childList?.length;

          return (
            <View key={item.id} className="flex pt-35">
              <Image
                className="size-65 bg-#666 rounded-full"
                mode="aspectFill"
                src={item?.appUser?.photo || img_avatar}
              />
              <View className="flex-1 min-w-0 ml-15 b-b-(1 solid #ccc/30) pb-10">
                <View className="flex items-center h-74">
                  <View className="flex-1 min-w-0 text-(26 #11100F) fw-bold">
                    <View className="line-clamp-1">{item?.appUser?.name}</View>
                  </View>
                  <View className="text-(20 #787777) ml-15">
                    {fdate(item.createDate)}
                  </View>
                </View>

                <View className="text-(24/38 #11100F) py-15">
                  {item.content || "--"}
                </View>

                {showSub && (
                  <View className="bg-#f5f5f5 px-25 overflow-hidden py-10">
                    {subs?.map((subitem) => {
                      return (
                        <View
                          className="text-(24/36 #787878) my-6"
                          key={subitem.id}
                          onClick={() => openReply(subitem)}
                        >
                          <Text className="text-(26 #8E4E36)">
                            {subitem?.appUser?.name}
                          </Text>

                          <Text className="mx-3">回复</Text>
                          <Text className="text-(26 #8E4E36)">
                            {subitem?.replyCommentsUser?.name}
                          </Text>

                          <Text className="text-(26 #8E4E36)">：</Text>
                          <Text>{subitem?.content}</Text>
                        </View>
                      );
                    })}
                  </View>
                )}

                <View className="flex items-center h-70 pl-20">
                  <View className="flex-1">
                    {showOpen && (
                      <>
                        {subLoading ? (
                          <View
                            className="text-(24 #999)"
                            onClick={() => openSub(item)}
                          >
                            加载中...
                          </View>
                        ) : (
                          <View
                            className="text-(24 #8E4E36)"
                            onClick={() => openSub(item)}
                          >
                            展开{item.childCommentsNum || 0}条回复
                          </View>
                        )}
                      </>
                    )}
                  </View>

                  <View
                    className="flex items-center px-10"
                    onClick={(e) => {
                      e.stopPropagation();
                      openReply(item);
                    }}
                  >
                    <UnoIcon className="i-icon-park-outline:message-one size-30 c-#5f5f5f" />
                    <View className="text-(20 #5F5F5F) ml-3">
                      {item.childCommentsNum || 0}
                    </View>
                  </View>
                  {/* <View className="flex items-center px-10">
              <UnoIcon className="i-icon-park-outline:like size-30 c-#5f5f5f" />
              <View className="text-(20 #5F5F5F) ml-3">123</View>
            </View> */}
                </View>
              </View>
            </View>
          );
        })}

        <View className="text-(24 #999 center) py-30">
          {list.more ? "加载中..." : "没有更多评论了"}
        </View>

        <CommentReply
          aid={props.aid}
          rfor={state.rfor}
          open={state.ropen}
          onClose={closeReply}
          onOK={onReplyOk}
        />
      </View>
    );
  }
);

export const CommentReply = (props: {
  aid: string;
  rfor?: any;
  open?: boolean;
  onClose?: () => any;
  onOK?: (res?: any) => any;
}) => {
  const [state, setState] = useSetState({
    kh: 0,

    anonymousTag: 0,
    content: "",

    focus: false,
  });

  useEffect(() => {
    const func = (res) => setState({ kh: res.height });
    Taro.onKeyboardHeightChange(func);

    return () => {
      Taro.offKeyboardHeightChange(func);
    };
  }, []);

  useEffect(() => {
    if (props.open) {
      setState({ anonymousTag: 0, content: "" });
      setTimeout(() => setState({ focus: true }), 100);
    } else {
      setState({ focus: false });
    }
  }, [props.open]);

  const onReply = async () => {
    const data = {
      articleId: props.aid,
      replyCommentsId: props.rfor?.id || "",
      content: state.content,
      anonymousTag: state.anonymousTag,
    };

    if (!data.content) return showToast("请输入评论内容");
    Taro.showLoading({ title: "评论中...", mask: true });
    const res = await CusApi.addNComment(data);
    // Taro.hideLoading();
    showToast("评论成功");
    props.onClose?.();
    props.onOK?.({ ...res, reply: props.rfor });
  };

  return (
    <PopUp open={props.open} onClose={() => props.onClose?.()}>
      <View
        className="bg-#F4F4F4 p-25 pt-35"
        style={{ borderRadius: "20rpx 20rpx 0 0" }}
      >
        <ScrollView scrollY className="max-h-120">
          <Textarea
            className="block w-full h-120 min-h-120 text-(26/40 #000)"
            autoHeight
            adjustPosition={false}
            placeholder={
              props.rfor
                ? `回复@${props.rfor?.appUser?.name || "--"}：`
                : "请输入评论"
            }
            maxlength={200}
            focus={state.focus}
            // autoFocus
            value={state.content}
            onInput={(e) => setState({ content: e.detail.value })}
          />
        </ScrollView>
        <View className="flex items-center py-25">
          <View
            className="flex-1 flex items-center"
            onClick={() =>
              setState({ anonymousTag: Number(!state.anonymousTag) })
            }
          >
            {state.anonymousTag ? (
              <UnoIcon className="i-icon-park-outline:check-one size-32 text-#999 mr-15" />
            ) : (
              <UnoIcon className="i-icon-park-outline:round size-32 text-#999 mr-15" />
            )}
            <View className="text-(26 #666)">匿名</View>
          </View>
          <View
            className="h-60 w-160 rounded-10 bg-#AD7F6D text-(26/60 center #fff)"
            onClick={onReply}
          >
            发布
          </View>
        </View>
      </View>
      <View style={{ height: state.kh }} className=""></View>
    </PopUp>
  );
};
