import { Swiper, SwiperItem, View } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useSetState } from "ahooks";
import clsx from "clsx";
import { Notes } from "./Notes";
import { Reply } from "./Reply";
// import { Wiki } from "./Wiki";
import { useMemo } from "react";

const tabs = [
  { name: "最新活动", key: "20", comp: <Notes key="20" type="20" /> },
  { name: "评价", key: "30", comp: <Reply key="30" /> },
  // { name: "术后须知", key: "40", comp: <Wiki key="40" /> },
  { name: "哈哈日常", key: "10", comp: <Notes key="10" type="10" /> },
];

export default function NoteRoot() {
  const router = useRouter();
  const [state, setState] = useSetState({
    tabKey: router.params.key || "20",
  });

  const crt = useMemo(() => {
    return tabs.findIndex((n) => n.key == state.tabKey);
  }, [state.tabKey]);

  return (
    <View className="flex flex-col h-100vh overflow-hidden">
      <View className="flex bg-#fff h-100 pos-relative">
        {tabs.map((item) => (
          <View
            key={item.key}
            className={clsx(
              "flex-1 flex items-center justify-center transition-300 text-(28 #010101) fw-bold",
              state.tabKey == item.key && "!text-(#8E4E36)"
            )}
            onClick={() => setState({ tabKey: item.key })}
          >
            <View className="line-clamp-1">{item.name}</View>
          </View>
        ))}
        <View
          className="pos-absolute bottom-20 left-0 flex justify-center transition-300"
          style={{
            width: 100 / tabs.length + "%",
            transform: `translateX(${tabs.findIndex(
              (n) => n.key == state.tabKey
            )}00%)`,
          }}
        >
          <View className="w-26 h-6 bg-#AD7F6D"></View>
        </View>
      </View>

      <View className="flex-1 min-h-0">
        <Swiper
          className="size-full"
          current={crt}
          onChange={(e) => {
            const idx = e.detail.current;
            const key = tabs[idx].key;
            setState({ tabKey: key });
          }}
        >
          {tabs.map((item) => (
            <SwiperItem className="size-full" key={item.key}>
              <View className="size-full flex flex-col">{item.comp}</View>
            </SwiperItem>
          ))}
        </Swiper>
      </View>

      {/* {tabs.find((n) => n.key == state.tabKey)?.comp} */}
    </View>
  );
}
