import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { Image, ScrollView, View } from "@tarojs/components";
import { useSetState, useUpdateEffect } from "ahooks";
import { NoteCate } from "./unit";
import { UnoIcon } from "@/components/UnoIcon";
import Taro from "@tarojs/taro";

const img_bg = `https://oss.gaomei168.com/file-release/20241028/1300503697838927872.png`;

export const Wiki = () => {
  const type = "40";
  const [state, setState] = useSetState({
    loading: false,
    cate: "",
  });

  const [list, listAct] = useList({
    api: CusApi.getNote,
    params: { type, categoryId: state.cate },
  });

  useUpdateEffect(() => {
    listAct.reload();
  }, [state.cate]);

  return (
    <>
      <NoteCate type={type} onChange={(n) => setState({ cate: n })} />

      <ScrollView
        className="flex-1 min-h-0"
        scrollY
        onScrollToLower={() => listAct.getNext()}
        refresherEnabled
        refresherTriggered={state.loading}
        refresherBackground="#f5f5f5"
        onRefresherRefresh={async () => {
          setState({ loading: true });
          await listAct.reload().finally(() => setState({ loading: false }));
        }}
      >
        <View className="grid cols-2 gap-25 p-25">
          {list.list.map((item) => (
            <View
              className="h-208 bg-#fff rounded-20 pos-relative"
              key={item.id}
              onClick={() => {
                Taro.navigateTo({
                  url: `/pages/mall2/noteinfo40/index?id=${item.id}`,
                });
              }}
            >
              <Image
                className="pos-absolute top-32 left-45 w-130 h-30"
                mode="aspectFit"
                src={img_bg}
              />
              <UnoIcon className="pos-absolute top-30 right-20 i-icon-park-outline:right size-30 c-#666" />
              <View className="pos-absolute left-0 bottom-40 px-45">
                <View className="text-(26 #8E4E36) line-clamp-2">
                  {item.title}
                </View>
                <View className="text-(18 #666) mt-5">{item.categoryName}</View>
              </View>
            </View>
          ))}
        </View>
        <View className="py-30 text-(24 #999 center)">
          {list.more ? "加载中..." : "没有更多数据了"}
        </View>
      </ScrollView>
    </>
  );
};
