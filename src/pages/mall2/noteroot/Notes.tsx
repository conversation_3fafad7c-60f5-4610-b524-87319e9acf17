import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { img_avatar } from "@/utils/images";
import { str2arr } from "@/utils/tools";
import { Image, ScrollView, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useSetState } from "ahooks";
import { PostNote10 } from "./post";
import { NoteCate } from "./unit";
import { useEffect } from "react";

export const Notes = (props: { type?: string }) => {
  const [state, setState] = useSetState({
    loading: false,
    openPost: false,
    cate: "",
  });

  const [list, listAct] = useList({
    api: CusApi.getNote,
    params: { type: props.type, categoryId: state.cate },
  });

  useEffect(() => {
    listAct.reload();
  }, [state.cate]);

  // useMount(() => {
  //   listAct.reload();
  // });

  return (
    <>
      {/* {props.type == "10" && ( */}
      <NoteCate type={props.type} onChange={(n) => setState({ cate: n })} />
      {/* )} */}

      <ScrollView
        className="flex-1 min-h-0"
        scrollY
        onScrollToLower={() => listAct.getNext()}
        refresherEnabled
        refresherTriggered={state.loading}
        refresherBackground="#f5f5f5"
        onRefresherRefresh={async () => {
          setState({ loading: true });
          await listAct.reload().finally(() => setState({ loading: false }));
        }}
      >
        <View className="grid cols-2 gap-10 pt-10">
          {list.list.map((item) => {
            const imgs = str2arr(item.images);
            let cover = item.model == 1 ? imgs?.[0] : item.videoCoverImage;
            cover = cover + "?x-oss-process=image/resize,w_750,limit_0";

            return (
              <View
                key={item.id}
                className="bg-#fff"
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/mall2/noteinfo/index?id=${item.id}`,
                  });
                }}
              >
                <Image
                  className="block w-full h-493 bg-#fff"
                  mode="aspectFit"
                  src={cover}
                />
                <View className="px-25 pt-20">
                  <View className="h-88 text-(30/44 #282828) line-clamp-2">
                    {item.title}
                  </View>
                  <View className="flex items-center py-20">
                    <Image
                      className="size-37 bg-#666 rounded-full overflow-hidden"
                      mode="aspectFill"
                      src={item?.appUser?.photo || img_avatar}
                    />
                    <View className="text-(20 #5B5B5B) line-clamp-1 flex-1 min-w-0 fw-bold mx-10">
                      {item?.appUser?.name}
                    </View>
                    {item.likesTag ? (
                      <UnoIcon className="i-icon-park-solid:like size-30 c-#8E4E36" />
                    ) : (
                      <UnoIcon className="i-icon-park-outline:like size-30 c-#333" />
                    )}
                    {/* <UnoIcon className="i-icon-park-outline:like size-30 text-#5f5f5f" /> */}
                    <View className="text-(23 #5f5f5f) ml-5">
                      {item.likesNum}
                    </View>
                  </View>
                </View>
              </View>
            );
          })}
        </View>
        <View className="text-(24 #999 center) py-30">
          {list.more ? "加载中..." : "没有更多数据了"}
        </View>
      </ScrollView>

      {props.type == "10" && (
        <View
          className="z-99 pos-fixed bottom-100 right-25 size-80 bg-#AD7F6D rounded-full flex flex-col items-center justify-center"
          onClick={() => setState({ openPost: true })}
        >
          <UnoIcon className="i-icon-park-outline:file-editing-one size-50 text-#fff mb-5" />
          {/* <View className="text-(22 #fff)">发布日常</View> */}
        </View>
      )}

      <PostNote10
        open={state.openPost}
        onClose={() => setState({ openPost: false })}
        onOk={() => listAct.reload()}
      />
    </>
  );
};
