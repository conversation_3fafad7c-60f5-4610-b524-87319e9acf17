import { PopUp } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import { Input, ScrollView, Textarea, View } from "@tarojs/components";
import { UploadImage, UploadVideo } from "./Uploader";
import { useMount, useSetState } from "ahooks";
import { useEffect } from "react";
import clsx from "clsx";
import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { Rate } from "@/components/Rate";
import Taro from "@tarojs/taro";

const tabs = [
  { id: 1, name: "图片" },
  { id: 2, name: "视频" },
];

const INIT_STATE = {
  mode: 1,
  title: "",
  cont: "",
  imgs: "",
  video: "",
  cover: "",
  anon: false,
};

export const PostNote10 = (props: {
  open?: boolean;
  onClose?: () => any;
  onOk?: () => any;
}) => {
  const [state, setState] = useSetState({ key: 1, ...INIT_STATE });
  const [state2, setState2] = useSetState({ loading: false });

  const maxlen = 500;

  useEffect(() => {
    if (props.open) {
      setState({ ...INIT_STATE, key: state.key + 1 });
    }
  }, [props.open]);

  const submit = async () => {
    if (state2.loading) return;
    setState2({ loading: true });

    try {
      const data = {
        type: 10,
        title: state.title,
        content: state.cont,
        // evaluationStars: 0,
        model: state.mode,
        images: state.imgs,
        videoCoverImage: state.cover,
        videoUrl: state.video,
        anonymousTag: Number(state.anon),
        // saveStyle: 0,
      };

      if (!data.title) return showToast("请填写标题");
      if (!data.content) return showToast("请填写内容");
      if (data.model == 1 && !data.images) return showToast("请上传图片");
      if (data.model == 2 && !data.videoUrl) return showToast("请上传视频");

      await CusApi.addNote(data);
      props.onClose?.();
      props.onOk?.();

      Taro.showModal({
        title: "发布成功",
        content: "请等待管理员审核",
        showCancel: false,
      });
    } finally {
      setState2({ loading: false });
    }
  };

  return (
    <PopUp open={props.open} onClose={() => props.onClose?.()}>
      <View
        className="bg-#F4F4F4 px-20 pt-20"
        style={{ borderRadius: "70rpx 70rpx 0 0" }}
      >
        <View className="flex items-center py-20 px-30">
          <View className="flex-1 min-w-0 text-(37 #11100F)">发布日常</View>
          <View onClick={() => props.onClose?.()}>
            <UnoIcon className="i-icon-park-outline:close-one size-36 c-#333" />
          </View>
        </View>
        <View
          className="min-h-100 bg-#fff px-35 pt-30"
          style={{ borderRadius: "20rpx 20rpx 0 0" }}
        >
          <View className="flex b-(1 solid #AD7F6D) rounded-10 mb-20 overflow-hidden">
            {tabs.map((item) => (
              <View
                key={item.id}
                className={clsx(
                  "flex-1 h-60 text-(22/60 center #666)",
                  state.mode == item.id && "!bg-#AD7F6D !text-#fff"
                )}
                onClick={() => setState({ mode: item.id })}
              >
                {item.name}
              </View>
            ))}
          </View>

          {state.mode == 1 && (
            <UploadImage
              key={state.key}
              max={3}
              onChange={(s) => setState({ imgs: s })}
            />
          )}

          {state.mode == 2 && (
            <View className="grid cols-3 gap-20">
              <UploadVideo
                key={state.key}
                max={1}
                className="!cols-1"
                onChange={(s) => setState({ video: s })}
              />
              <UploadImage
                key={state.key}
                max={1}
                className="!cols-1"
                holder="视频封面"
                onChange={(s) => setState({ cover: s })}
              />
            </View>
          )}

          <Input
            className="h-100 text-(38/100 #333)  b-b-(1 solid #eee)"
            placeholder="点击输入标题"
            value={state.title}
            onInput={(e) => setState({ title: e.detail.value })}
          />
          <ScrollView scrollY className="h-200 mt-30">
            <Textarea
              placeholder="点击输入正文内容~"
              className="block w-full text-(26/40 #333) h-200 min-h-200"
              value={state.cont}
              onInput={(e) => setState({ cont: e.detail.value })}
              maxlength={maxlen}
            />
          </ScrollView>
          <View className="flex justify-end text-(22 #b2b2b2) py-10">
            {state.cont.length} / {maxlen}
          </View>

          <View className="flex items-center py-30">
            {/* <View
              className="flex items-center"
              onClick={() => setState({ anon: !state.anon })}
            >
              {state.anon ? (
                <UnoIcon className="i-icon-park-outline:check-one size-36 c-#AD7F6D" />
              ) : (
                <UnoIcon className="i-icon-park-outline:round size-36 c-#c9c9c9" />
              )}
              <View className="text-(26 #666) ml-15">匿名发布</View>
            </View> */}
            <View
              className="ml-auto w-408 h-90 bg-#AD7F6D rounded-18 text-(30/90 center #fff)"
              onClick={submit}
            >
              发布日常
            </View>
          </View>
        </View>
      </View>
    </PopUp>
  );
};

const INIT_STATE_30 = {
  cont: "",
  imgs: "",
  anon: false,
  rate: 5,
  cate: "",
};

export const PostNote30 = (props: {
  open?: boolean;
  onClose?: () => any;
  onOk?: () => {};
}) => {
  const [state, setState] = useSetState({ ...INIT_STATE_30 });
  const [state2, setState2] = useSetState({
    loading: false,
    cates: [] as any[],
  });

  const maxlen = 500;

  useMount(() => {
    fetchCates();
  });

  useEffect(() => {
    if (props.open) {
      setState({ ...INIT_STATE_30 });
    }
  }, [props.open]);

  const fetchCates = async () => {
    const res = await CusApi.getNoteCates({ type: 30, pageSize: 9999 });
    setState2({ cates: res?.list || [] });
  };

  const submit = async () => {
    if (state2.loading) return;
    setState2({ loading: true });

    try {
      const data = {
        type: 30,
        content: state.cont,
        evaluationStars: state.rate,
        model: 1,
        images: state.imgs,
        anonymousTag: Number(state.anon),
        categoryId: state.cate,
      };

      if (!data.categoryId) return showToast("请选择评价分类");
      if (!data.content) return showToast("请填写内容");
      // if (!data.images) return showToast("请上传图片");

      await CusApi.addNote(data);

      props.onClose?.();
      props.onOk?.();

      Taro.showModal({
        title: "发布成功",
        content: "请等待管理员审核",
        showCancel: false,
      });
    } finally {
      setState2({ loading: false });
    }
  };

  return (
    <PopUp open={props.open} onClose={() => props.onClose?.()}>
      <View
        className="bg-#F4F4F4 px-20 pt-20"
        style={{ borderRadius: "70rpx 70rpx 0 0" }}
      >
        <View className="flex items-center py-20 px-30">
          <View className="flex-1 min-w-0 text-(37 #11100F)">发布评价</View>
          <View onClick={() => props.onClose?.()}>
            <UnoIcon className="i-icon-park-outline:close-one size-36 c-#333" />
          </View>
        </View>
        <View
          className="min-h-100 bg-#fff px-35 pt-30"
          style={{ borderRadius: "20rpx 20rpx 0 0" }}
        >
          <View>
            <View className="text-(20 #787777)">选择评价类型</View>
            <View className="grid cols-4 gap-20 pt-20 pb-30">
              {state2.cates.map((item) => (
                <View
                  key={item.id}
                  className={clsx(
                    "h-54 rounded-10 bg-#f1f1f1 text-(22/54 center #333) b-(1 solid #f1f1f1)",
                    item.id == state.cate && "!b-#AD7F6D !text-#8E4E36"
                  )}
                  onClick={() => setState({ cate: item.id })}
                >
                  {item.name}
                </View>
              ))}
            </View>
          </View>

          <View className="flex items-center py-0">
            <View className="text-(28 #11100F) fw-bold mr-20">总体</View>
            <Rate
              value={state.rate}
              color1="#8E4E36"
              size={36}
              gap={15}
              onChange={(e) => setState({ rate: e })}
            />
          </View>
          <ScrollView scrollY className="h-200 mt-30">
            <Textarea
              placeholder="写出您的感受，可以帮助其他小伙伴哦~"
              className="block w-full text-(26/40 #333) h-200 min-h-200"
              value={state.cont}
              onInput={(e) => setState({ cont: e.detail.value })}
              maxlength={maxlen}
            />
          </ScrollView>

          <View className="flex justify-end text-(22 #b2b2b2) py-10">
            {state.cont.length} / {maxlen}
          </View>

          <UploadImage max={3} onChange={(s) => setState({ imgs: s })} />

          <View className="flex items-center py-30">
            <View
              className="flex items-center"
              onClick={() => setState({ anon: !state.anon })}
            >
              {state.anon ? (
                <UnoIcon className="i-icon-park-outline:check-one size-36 c-#AD7F6D" />
              ) : (
                <UnoIcon className="i-icon-park-outline:round size-36 c-#c9c9c9" />
              )}
              <View className="text-(26 #666) ml-15">匿名发布</View>
            </View>
            <View
              className="ml-auto w-408 h-90 bg-#AD7F6D rounded-18 text-(30/90 center #fff)"
              onClick={submit}
            >
              发布评价
            </View>
          </View>
        </View>
      </View>
    </PopUp>
  );
};
