import { Rate } from "@/components/Rate";
import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { img_avatar } from "@/utils/images";
import { str2arr } from "@/utils/tools";
import { Image, ScrollView, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useSetState } from "ahooks";
import dayjs from "dayjs";
import { NoteCate } from "./unit";
import { useEffect } from "react";
import { PostNote30 } from "./post";

const fdate = (n) => (n ? dayjs(n).format("YYYY-MM-DD") : "--");

export const Reply = () => {
  const [state, setState] = useSetState({
    loading: false,
    cate: "",
    openPost: false,
  });

  const [list, listAct] = useList({
    api: CusApi.getNote,
    params: { type: 30, categoryId: state.cate },
  });

  useEffect(() => {
    listAct.reload();
  }, [state.cate]);

  return (
    <>
      <NoteCate type="30" onChange={(n) => setState({ cate: n })} />

      <ScrollView
        className="flex-1 min-h-0"
        scrollY
        onScrollToLower={() => listAct.getNext()}
        refresherEnabled
        refresherTriggered={state.loading}
        refresherBackground="#f5f5f5"
        onRefresherRefresh={async () => {
          setState({ loading: true });
          await listAct.reload().finally(() => setState({ loading: false }));
        }}
      >
        <View className="grid cols-1 gap-25 px-25 py-25">
          {list.list.map((item) => {
            const imgs = str2arr(item.images).slice(0, 3);

            return (
              <View
                className="bg-#fff rounded-10"
                key={item.id}
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/mall2/noteinfo30/index?id=${item.id}`,
                  });
                }}
              >
                <View className="flex items-center px-25 py-20">
                  <Image
                    className="size-74 bg-#666 rounded-full"
                    mode="aspectFill"
                    src={item.appUser?.photo || img_avatar}
                  />
                  <View className="flex-1 min-w-0 px-10 flex flex-col">
                    <View className="line-clamp-1 text-(25 #11100F) fw-bold">
                      {item.appUser?.name}
                    </View>
                    <View className="text-0 mt-5">
                      <Rate
                        value={item.evaluationStars}
                        disabled
                        color1="#8E4E36"
                        gap={4}
                        size={22}
                      />
                    </View>
                  </View>
                  <View className="text-(20 #787777)">
                    {fdate(item.createDate)}
                  </View>
                </View>
                <View className="px-50 text-(24/38 #11100F)">
                  {item.content ?? "--"}
                </View>
                {!!imgs.length && (
                  <View className="grid cols-3 px-50 gap-10 pt-20">
                    {imgs.map((url, idx) => (
                      <View className="pt-1/1 pos-relative" key={idx}>
                        <Image
                          className="pos-absolute top-0 left-0 size-full bg-#666 rounded-10"
                          mode="aspectFill"
                          src={url}
                        />
                      </View>
                    ))}
                  </View>
                )}
                <View className="py-25 px-50 flex items-center">
                  <View className="w-300 h-50 rounded-full bg-#f5f5f5 text-(23/50 #666 center)">
                    让我来评评理
                  </View>
                  <View className="flex-1 min-w-0 flex justify-end">
                    <View className="flex items-center ml-20">
                      {item.likesTag ? (
                        <UnoIcon className="i-icon-park-solid:like size-30 c-#8E4E36" />
                      ) : (
                        <UnoIcon className="i-icon-park-outline:like size-30 c-#333" />
                      )}
                      <View className="text-(18 #333) ml-5">
                        {item.likesNum}
                      </View>
                    </View>

                    <View className="flex items-center ml-20">
                      <UnoIcon className="i-icon-park-outline:message-one size-30 c-#333" />
                      <View className="text-(18 #333) ml-5">
                        {item.commentsNum}
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            );
          })}
        </View>
        <View className="py-30 text-(24 #999 center)">
          {list.more ? "加载中..." : "没有更多数据了"}
        </View>
      </ScrollView>

      <View
        className="z-99 pos-fixed bottom-100 right-25 size-80 bg-#AD7F6D rounded-full flex flex-col items-center justify-center"
        onClick={() => setState({ openPost: true })}
      >
        <UnoIcon className="i-icon-park-outline:file-editing-one size-50 text-#fff mb-5" />
        {/* <View className="text-(22 #fff)">发布评价</View> */}
      </View>

      <PostNote30
        open={state.openPost}
        onClose={() => setState({ openPost: false })}
        onOk={() => listAct.reload()}
      />
    </>
  );
};
