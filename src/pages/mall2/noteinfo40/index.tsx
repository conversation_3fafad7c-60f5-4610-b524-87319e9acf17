import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { useShare } from "@/stores/useShare";
import { str2arr } from "@/utils/tools";
import { Button, Image, ScrollView, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { useMemo } from "react";

export const NavBar = (props: { data: any }) => {
  const unit = useMemo(() => {
    const win = Taro.getWindowInfo();
    const sh = win.statusBarHeight || 0;
    const rect = Taro.getMenuButtonBoundingClientRect();
    const nh = rect.height + (rect.top - sh) * 2;
    const pr = win.windowWidth - rect.left;
    return { sh, nh, pr };
  }, []);

  useShare(async () => {
    return {
      title: props.data?.title,
      imageUrl: props.data?._cover,
      path: `/pages/mall2/noteinfo40/index?id=${props.data?.id}`,
    };
  });

  return (
    <View className="bg-#F7F4EF">
      <View className="" style={{ height: unit.sh }}></View>
      <View
        className="flex items-center"
        style={{ height: unit.nh, marginRight: unit.pr }}
      >
        <View
          className="px-20"
          onClick={() => {
            Taro.navigateBack({
              fail: () => Taro.reLaunch({ url: `/pages/tabs/mall/index` }),
            });
          }}
        >
          <UnoIcon className="i-icon-park-outline:left size-40 c-#333" />
        </View>

        <View className="flex-1 min-w-0 px-15">
          <View className="text-(34 #11100F) line-clamp-1 fw-bold">
            {props.data?.title}
          </View>
        </View>
        <View className="px-20 pos-relative">
          <UnoIcon className="i-icon-park-outline:share size-40 c-#333" />
          <Button
            className="pos-absolute top-0 left-0 size-full opacity-0"
            openType="share"
          />
        </View>
      </View>
      {/* <View className="h-20"></View> */}
    </View>
  );
};

export default function NoteInfo() {
  const router = useRouter();
  const [state, setState] = useSetState({
    data: null as any,
  });

  useMount(() => {
    fetchInfo();
  });

  const fetchInfo = async () => {
    const id = router.params.id;
    const res = await CusApi.getNoteInfo({ id });
    const imgs = str2arr(res?.images);
    const cover = res?.model == 1 ? imgs?.[0] : res?.videoCoverImage;
    res._imgs = imgs;
    res._cover = cover;

    setState({ data: res });
  };

  return (
    <View className="h-100vh bg-#F7F4EF flex flex-col">
      <NavBar data={state.data} />
      <ScrollView scrollY className="flex-1 min-h-0">
        <View className="grid cols-1 gap-10">
          {state.data?._imgs?.map((u, i) => (
            <Image
              key={i}
              className="block w-full h-auto"
              mode="widthFix"
              src={u}
              showMenuByLongpress
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
}
