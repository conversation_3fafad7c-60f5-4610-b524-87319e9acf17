import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { str2arr } from "@/utils/tools";
import { Image, ScrollView, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { useRef } from "react";
import { NavBar } from "../noteroot/unit";
import { NoteComments } from "../noteroot/Comments";
import { Rate } from "@/components/Rate";
import { useShare } from "@/stores/useShare";

export default function NoteInfo() {
  const router = useRouter();
  const comRef = useRef<any>(null);

  const [state, setState] = useSetState({
    id: router.params.id || "",
    data: null as any,
  });

  useMount(() => {
    fetchInfo();
  });

  useShare(() => {
    return {
      title: state.data?.title,
      imageUrl: state.data?._cover,
      path: `/pages/mall2/noteinfo30/index?id=${state.data?.id}`,
    };
  });

  const fetchInfo = async () => {
    const id = router.params.id;
    const res = await CusApi.getNoteInfo({ id });
    const imgs = str2arr(res?.images);
    const cover = res?.model == 1 ? imgs?.[0] : res?.videoCoverImage;
    res._imgs = imgs;
    res._cover = cover;

    setState({ data: res });
  };

  const toggleLike = async () => {
    if (!state.data?.id) return;
    const data = { source: 1, articleId: state.id, type: 20 };
    if (state.data?.likesTag) {
      await CusApi.delNoteAct(data);
    } else {
      await CusApi.addNoteAct(data);
    }
    fetchInfo();
  };

  return (
    <View className="h-100vh flex flex-col">
      <NavBar data={state.data} />

      <ScrollView
        scrollY
        className="flex-1 min-h-0"
        onScrollToLower={() => comRef.current.getNext()}
      >
        <View className="px-35 bg-#fff pb-50">
          <View className="flex items-center py-25">
            <View className="text-(28 #5D5D5D)">总体：</View>
            <Rate
              value={state.data?.evaluationStars}
              disabled
              size={36}
              gap={8}
              color1="#8E4E36"
            />
          </View>

          <View className="text-(30/48 #11100F) ws-pre-wrap">
            {state.data?.content ?? "--"}
          </View>

          <View className="grid cols-3 gap-10 pt-50">
            {state.data?._imgs?.map((url, idx) => (
              <View
                className="pos-relative pt-1/1"
                key={idx}
                onClick={() => {
                  Taro.previewImage({
                    current: url,
                    urls: state.data?._imgs || [],
                  });
                }}
              >
                <Image
                  className="pos-absolute top-0 left-0 size-full rounded-10 bg-#666"
                  mode="aspectFill"
                  src={url}
                />
              </View>
            ))}
          </View>
        </View>

        <View className="px-35 bg-#fff">
          <NoteComments ref={comRef} aid={state.id} />
        </View>
      </ScrollView>

      <View
        className="h-130 bg-#fff flex items-center box-border px-25"
        style={{ boxShadow: "0rpx 3rpx 7rpx 0rpx rgba(0,0,0,0.48)" }}
      >
        <View
          className="w-310 h-66 bg-#F5F5F5 rounded-full flex items-center justify-center"
          onClick={() => comRef.current?.openReply()}
        >
          <UnoIcon className="i-icon-park-outline:file-editing-one size-36 c-#666" />
          <View className="ml-15 text-(31 #666)">发布评论</View>
        </View>
        <View className="flex-1 min-w-0 flex px-50">
          <View
            className="flex-1 flex items-center justify-center"
            onClick={() => comRef.current?.openReply()}
          >
            <UnoIcon className="i-icon-park-outline:message-one size-40 c-#333" />
            <View className="text-(33 #5F5F5F) ml-10">
              {state.data?.commentsNum || 0}
            </View>
          </View>
          <View
            className="flex-1 flex items-center justify-center"
            onClick={() => toggleLike()}
          >
            {state.data?.likesTag ? (
              <UnoIcon className="i-icon-park-solid:like size-40 c-#8E4E36" />
            ) : (
              <UnoIcon className="i-icon-park-outline:like size-40 c-#333" />
            )}
            <View className="text-(33 #5F5F5F) ml-10">
              {state.data?.likesNum || 0}
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}
