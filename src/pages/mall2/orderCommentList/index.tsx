import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { Rate } from "@taroify/core";
import { Image, View } from "@tarojs/components";
import Taro, { useReachBottom, useRouter } from "@tarojs/taro";
import { useMount } from "ahooks";
import dayjs from "dayjs";

export default () => {
  const router = useRouter();
  const str2arr = (s) => (s || "").split(",").filter((n) => n);

  const [list, listAct] = useList({
    api: CusApi.getOrderComment,
    params: { productId: router.params.id },
  });

  console.log(list.list);

  useMount(() => {
    listAct.reload();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  return (
    <View className="py-20">
      <View className="bg-#fff px-40">
        {list.list.map((item) => {
          const imgs = str2arr(item.evaluationImage);

          return (
            <View className="b-t-(1 solid #eee) first:(b-0)" key={item.id}>
              <View className="flex items-center py-30">
                <Image
                  className="size-56 mr-15 bg-#f5f5f5 rounded-full overflow-hidden"
                  mode="aspectFill"
                  src={item.appUser?.photo}
                ></Image>
                <View className="flex-1 min-w-0">
                  <View className="text-(24 #000)">{item.appUser?.name}</View>
                  <View className="text-(22 #aaa) mt-4">
                    {dayjs(item.evaluationDate).format("YYYY-MM-DD")}
                  </View>
                </View>
                <View>
                  <Rate
                    value={item.evaluationScore}
                    readonly
                    style={
                      {
                        "--rate-icon-size": "25rpx",
                        "--rate-icon-gutter": "8rpx",
                        "--rate-icon-full-color": "#8E4E36",
                      } as any
                    }
                  />
                </View>
              </View>
              {!!item.evaluationDesc && (
                <View className="text-(28 #000) pb-25">
                  {item.evaluationDesc}
                </View>
              )}
              {!!imgs.length && (
                <View className="flex pb-25">
                  {imgs.map((src, idx) => (
                    <Image
                      key={idx}
                      className="size-100 bg-#f5f5f5 rounded-10 overflow-hidden mr-20"
                      mode="aspectFill"
                      src={src}
                      onClick={() => {
                        Taro.previewImage({
                          current: src,
                          urls: imgs,
                        });
                      }}
                    />
                  ))}
                </View>
              )}
            </View>
          );
        })}
      </View>

      <View className="py-30 text-(22 #aaa center)">
        {list.more ? "加载中..." : "没有更多数据了"}
      </View>
    </View>
  );
};
