import Watermark from "@/components/Watermark";
import CusApi from "@/services/CusApi";
import { fetchShopsByLocation, showToast } from "@/utils/tools";
import { Image, Swiper, SwiperItem, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useRequest, useSetState } from "ahooks";

export default () => {
  const str2arr = (str) => (str || "").split(",").filter((n) => n);
  const [state, setState] = useSetState({
    shops: [] as any[],
  });

  useMount(() => {
    fetchShop();
  });

  const getPos = async () => {
    try {
      const pos = await Taro.getLocation({ type: "gcj02" });
      Taro.setStorageSync("pos", pos);
    } catch (e) {}
  };

  const fetchShop = async () => {
    await getPos();
    const res = await fetchShopsByLocation();
    setState({ shops: res });
  };

  const advRes = useRequest(CusApi.getAdv, {
    defaultParams: [{ showPosition: 2 }],
  });
  const advs: any[] = advRes.data?.list || [];

  return (
    <View className="">
      <View className="h-530 -mb-40">
        <Swiper className="size-full">
          {advs.map((item) => (
            <SwiperItem
              className="size-full"
              key={item.id}
              onClick={() => {
                Taro.previewImage({
                  current: item.image,
                  urls: advs.map((n) => n.image),
                });
              }}
            >
              <Image
                className="block size-full"
                mode="aspectFill"
                src={item.image}
              ></Image>
            </SwiperItem>
          ))}
        </Swiper>
      </View>

      <View className="pos-relative px-25">
        {state.shops.map((item) => (
          <View
            className="bg-#fff rounded-20 p-30 mb-20"
            key={item.id}
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/mall2/shopInfo/index?id=${item.id}`,
              });
            }}
          >
            <View className="flex">
              <Image
                className="size-110 bg-#fafafa rounded-10 overflow-hidden"
                mode="aspectFill"
                src={item.logo}
              ></Image>
              <View className="flex-1 min-w-0 flex flex-col justify-between py-5 px-20">
                <View className="flex items-center">
                  <View className="flex-1 min-w-0 text-(33 #000)">
                    {item.name}
                  </View>
                  <View className="w-3 h-27 bg-#eee"></View>
                  <Image
                    className="size-32 ml-30"
                    mode="aspectFit"
                    src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238077034488242176_size_4679.png"
                    onClick={(evt) => {
                      evt.stopPropagation();
                      Taro.makePhoneCall({ phoneNumber: item.contactPhone });
                    }}
                  ></Image>
                </View>
                <View className="flex items-center">
                  {str2arr(item.mainBusiness).map((item, idx) => (
                    <View
                      key={idx}
                      className="text-(24 #fff) bg-#ad7f6d rounded-10 px-15 py-5 mr-8"
                    >
                      {item}
                    </View>
                  ))}
                </View>
              </View>
            </View>

            <View
              className="flex items-center pt-20"
              onClick={async (evt) => {
                evt.stopPropagation();
                Taro.openLocation({
                  latitude: item.latitude,
                  longitude: item.longitude,
                  scale: 18,
                  name: item.name,
                  address: item.fullAddress,
                  fail: () => showToast("打开地址失败"),
                });
              }}
            >
              <Image
                className="size-26 shrink-0"
                mode="aspectFit"
                src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238077259869167616_size_2330.png"
              ></Image>
              <View className="text-(26 #666) px-10">{item.fullAddress}</View>
              <Image
                className="size-14 shrink-0"
                mode="aspectFit"
                src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238076800756457473_size_464.png"
              ></Image>
            </View>

            <View className="flex items-center pt-10">
              <Image
                className="size-26 shrink-0 invisible"
                mode="aspectFit"
                src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238077259869167616_size_2330.png"
              ></Image>
              <View className="text-(26 #666) px-10">
                距离当前位置：{item._dis}
              </View>
            </View>
          </View>
        ))}
      </View>

      <Watermark />
    </View>
  );
};
