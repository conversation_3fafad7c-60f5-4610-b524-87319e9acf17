import { UnoIcon } from "@/components/UnoIcon";
import Cus<PERSON>pi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { useShare } from "@/stores/useShare";
import { Tabs } from "@/components/Custom/Tabs";
import { Image, Text, View } from "@tarojs/components";
import Taro, { useReachBottom } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";
import { useEffect } from "react";

const sorts = [
  { name: "综合排序", key: "1" },
  { name: "积分升序", key: "2" },
  { name: "积分倒序", key: "3" },
];

export default function MMall() {
  const [state, setState] = useSetState({
    user2: null as any,
    tabs: [] as any[],
    tabKey: "" as any,
    sort: "1",
    sortShow: false,
  });

  // const type = useMemo(() => {
  //   return state.tabs[state.tabKey]?.id || "";
  // }, [state.tabKey, state.tabs]);

  const [list, listAct] = useList({
    api: CusApi.getMarketProduct,
    params: { marketType: 10, categoryId: state.tabKey, sortType: state.sort },
  });

  useShare(() => {
    return {
      title: "搞美「M币商城」",
      path: `/pages/mall2/mmall/index`,
    };
  });

  useEffect(() => {
    listAct.reload();
  }, [state.sort, state.tabKey]);

  useMount(() => {
    fetchUser();
    getTypes();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  const fetchUser = async () => {
    const user2 = await CusApi.getUserInfo(
      {},
      { _noAuth: true, _toast: false }
    );
    setState({ user2 });
  };

  const getTypes = async () => {
    const res = await CusApi.getMarketTypes({ marketType: 10 });
    let list: any[] = res?.list || [];
    list = list.map((n) => ({ id: n.id, name: n.name }));
    list.unshift({ id: "", name: "全部商品" });
    setState({ tabs: list });
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="pl-65 pr-40 pt-35 pb-35">
        <View className="flex items-center">
          <View className="flex-1 text-(56 #8E4E36) fw-500">
            搞美「M币商城」
          </View>
        </View>
        <View className="flex items-center">
          <View className="text-(24 #11100F) mt-15">医生品牌 匠心交付</View>
          <View
            className="ml-a text-24 text-#8E4E36"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/gmbDraw/index` });
            }}
          >
            M币提现&gt;
          </View>
        </View>
      </View>

      <View className="pos-relative mx-35 h-114 bg-#E8D9C5 rounded-10 mb-30">
        <Image
          className="w-293 h-77 pos-absolute top-20 right-0"
          mode="aspectFit"
          src="https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241012/1294712858860064768.png"
        />
        <View className="pos-absolute top-0 left-0 size-full flex items-center px-40 box-border">
          <View className="text-(29 #1A1A1A) flex-1">当前可用M币:</View>
          <View className="text-(50 #8E4E36)">{state.user2?.coin ?? "--"}</View>
          <Image
            className="size-35 ml-10"
            mode="aspectFit"
            src="https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241012/1294712858834898944.png"
          />
        </View>
      </View>

      <View className="flex items-center pos-relative px-40">
        <View className="flex-1 min-w-0">
          <Tabs
            tabs={state.tabs}
            value={state.tabKey}
            onChange={(e) => setState({ tabKey: e })}
            scroll={state.tabs.length > 3}
          />
        </View>

        <View
          className="ml-40 flex items-center"
          onClick={() => setState({ sortShow: true })}
        >
          <View className="text-(28 #666)">排序</View>
          <UnoIcon className="i-icon-park-outline:down size-20 text-#666 ml-5" />
        </View>

        <View>
          {state.sortShow && (
            <>
              <View
                className="z-50 pos-fixed top-0 left-0 size-full bg-#000/60"
                catchMove
                onClick={() => setState({ sortShow: false })}
              ></View>
              <View
                className="z-50 pos-absolute top-1/1 right-25 w-288 bg-#fff rounded-5"
                catchMove
              >
                {sorts.map((item) => (
                  <View
                    key={item.key}
                    className={clsx(
                      "h-80 mx-44 b-t-(1 solid #eee) text-(26/80 center #232323) first:b-0",
                      state.sort == item.key && "!text-#92533C"
                    )}
                    onClick={() =>
                      setState({ sort: item.key, sortShow: false })
                    }
                  >
                    {item.name}
                  </View>
                ))}
              </View>
            </>
          )}
        </View>
      </View>

      <View className="py-20">
        <View className="grid cols-2 px-40 gap-40">
          {list.list.map((item) => (
            <View
              className="rounded-24 overflow-hidden bg-#fff"
              key={item.id}
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/mall2/minfo/index?id=${item.id}`,
                })
              }
            >
              <Image
                className="h-315 w-full bg-#aaa"
                mode="aspectFill"
                src={item.coverImage}
              />
              <View className="h-145 px-15">
                <View className="h-80 text-(32/80 #11100F) line-clamp-1">
                  {item.name}
                </View>
                <View className="flex items-center">
                  <View className="flex-1 min-w-0 mr-20">
                    <Text className="text-(28 #11100F)">{item.saleCoin}</Text>
                    <Text className="text-(20 #11100F)"> M币</Text>
                  </View>
                  <View className="text-(20/36 center #fff) bg-#DEA266 rounded-full w-82 h-36">
                    兑换
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>

        <View className="text-(22 #999 center) py-30">
          {list.more ? "加载中..." : "没有更多数据了"}
        </View>
      </View>
    </View>
  );
}
