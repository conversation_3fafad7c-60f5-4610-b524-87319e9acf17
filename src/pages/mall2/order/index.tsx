import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { Image, Input, ScrollView, Text, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState, useUpdateEffect } from "ahooks";
import clsx from "clsx";
import dayjs from "dayjs";

export default () => {
  const router = useRouter();
  const [state, setState] = useSetState({
    group: {} as any,

    coupon: {} as any,
    save: {} as any,
    total: {} as any,
    score: false,
    open: false,
    addr: Taro.getStorageSync("addr") as any,
    remark: "",
  });

  useMount(() => {
    initGoods();
  });

  useUpdateEffect(() => {
    updateTotal();
  }, [state.group, state.score, state.coupon?.id]);

  const initGoods = async () => {
    const cid = router.params.cart;
    let group: any = null;

    if (cid) {
      const res = await CusApi.getCart();
      group = res?.find((n) => n.id === cid) || {};
      group.productList = (group.productList || []).filter(
        (n) => n.selectState
      );
    } else {
      group = Taro.getCurrentInstance().preloadData;
    }

    setState({ group });
    console.log("group", group);

    const list = group.productList.map((item) => ({
      productId: item.productId,
      totalCount: item.cartCount,
      shopId: item.shopId,
      // "serviceFee": item.serviceFee,
      serviceUserId: item.serviceUserId,
      productSpecificationId: item.productSpecificationId,
      // "discount": 0,
      // "lastMoney": 0
    }));

    const save = await CusApi.ckSave({ model: group.model, productList: list });
    setState({ save });
    console.log("save", save);
  };

  const updateTotal = async () => {
    const res = await CusApi.ckTotal({
      model: state.group.model,
      couponId: state.coupon?.id,
      useIntegralTag: state.score ? 1 : 0,
      productList: state.group.productList.map((item) => ({
        productId: item.productId,
        totalCount: item.cartCount,
        shopId: item.shopId,
        serviceUserId: item.serviceUserId,
        productSpecificationId: item.productSpecificationId,
      })),
    });
    console.log("total", res);
    setState({ total: res });
  };

  const choseAddr = async () => {
    const res = await Taro.chooseAddress();
    setState({ addr: res });
    Taro.setStorageSync("addr", res);
  };

  const openCoupon = async () => {
    if (state.save.couponList?.length) {
      setState({ open: true });
    }
  };

  const pickCoupon = async (item) => {
    setState({ open: false, coupon: item });
  };

  const handlePay = async () => {
    try {
      await Taro.requestSubscribeMessage({
        tmplIds: [
          "2TF7S1V76kGdssG8hdAe7K2PuZku5OAX19itR3p7Ya4", // 服务评价提醒
          "amOtUV-Hj4_JWer2HV5WWh6F2sG_bpmwPNdjoM3o89Y", // 订单退款成功提醒
          "Yjazv274K85LZepgNAAH6CNAj9E_gj2GZJ6ai3eUBnY", // 订单支付成功提醒
        ],
      } as any);
    } catch (e) {}

    if (state.group.model == 2 && !state.addr) {
      return Taro.showToast({ title: "请选择地址", icon: "none" });
    }

    const goInfoPage = () =>
      Taro.redirectTo({ url: `/pages/mall2/orderInfo/index?id=${order.id}` });

    Taro.showLoading({ title: "加载中..." });
    const order = await CusApi.createOrder({
      model: state.group?.model,
      couponId: state.coupon?.id,
      useIntegralTag: state.score ? 1 : 0,
      productList: state.group.productList.map((item) => ({
        productId: item.productId,
        totalCount: item.cartCount,
        shopId: item.shopId,
        serviceUserId: item.serviceUserId,
        productSpecificationId: item.productSpecificationId,
      })),
      orderRemark: state.remark,
      // shoppingCartIds: state.group.productList.map((n) => n.id).join(","),
      contactName: state.addr?.userName,
      contactMobile: state.addr?.telNumber,
      contactAddress: `${state.addr.provinceName} ${state.addr.cityName} ${state.addr.countyName} ${state.addr.detailInfo}`,
    });

    // 0元购
    if (!order.totalMoney) {
      await CusApi.payOrderZero({ orderId: order.id });
      Taro.hideLoading();
      await showToast("下单成功");
      goInfoPage();
    }

    // 微信支付
    else {
      const res = await CusApi.preOrder({
        wxAppId: process.env.TARO_APP_ID,
        orderId: order.id,
      });
      Taro.hideLoading();

      Taro.requestPayment({
        timeStamp: res.timeStamp,
        nonceStr: res.nonceStr,
        package: res.packgeStr,
        signType: res.signType,
        paySign: res.paySign,
        success: async () => {
          await showToast("下单成功");
          goInfoPage();
        },
        fail: async () => {
          await showToast("支付失败");
          goInfoPage();
        },
      });
    }

    // Taro.showModal({
    //   title: "支付成功",
    //   confirmText: "查看订单",
    //   cancelText: "返回",
    //   success: (res) => {
    //     if (res.cancel) {
    //       Taro.navigateBack();
    //     } else {
    //       if (res.confirm)
    //         Taro.redirectTo({
    //           url: `/pages/user/orderInfo/index?id=${order.id}`,
    //         });
    //     }
    //   },
    // });
  };

  return (
    <View className="py-40 px-20">
      {state.group?.model == 2 && (
        <View
          className="bg-#fff rounded-20 px-25 mb-30 py-30 flex items-center"
          onClick={choseAddr}
        >
          <Image
            mode="aspectFill"
            className="size-32 mr-20"
            src="https://vip.gaomei168.com/file-release/file/100/2024/0506/16/1237081777718009857_size_1966.png"
          ></Image>
          {state.addr ? (
            <View className="flex-1 min-w-0">
              <View className="text-(31 #000)">
                {state.addr.provinceName} {state.addr.cityName}{" "}
                {state.addr.countyName} {state.addr.detailInfo}
              </View>
              <View className="text-(24 #aaa) mt-5">
                {state.addr.userName} {state.addr.telNumber}
              </View>
            </View>
          ) : (
            <View className="flex-1 min-w-0">
              <View className="text-(31 #000)">请选择地址</View>
              <View className="text-(24 #aaa) mt-5">--</View>
            </View>
          )}
        </View>
      )}

      <View className="bg-#fff rounded-20 px-25">
        <View className="flex items-center py-20 b-b-(1 solid #eee)">
          <Image
            mode="aspectFill"
            className="size-31 mr-15"
            src={require("src/assets/shop.png")}
          ></Image>
          <View className="flex-1 min-w-0 text-(30 #000) fw-bold mt-5 line-clamp-1">
            {state.group?.shopName}
          </View>
        </View>

        <View className="px-15 py-10">
          {state.group.productList?.map((item) => (
            <View className="flex py-10 items-center" key={item.id}>
              <Image
                className="size-140 mr-25 rounded-10 overflow-hidden"
                mode="aspectFill"
                src={item.productCoverImage}
              ></Image>
              <View className="flex-1 min-w-0 h-140 flex flex-col justify-between box-border py-10">
                <View className="text-(28 #000) line-clamp-1">
                  {item.productName}
                </View>

                <View className="text-(22 #ad7f6d) mt-10">
                  {item.productSpecificationName}
                </View>

                <View className="flex items-center mt-10">
                  <View className="text-(28 #000) flex-1">
                    &yen;{item.cartPrice}
                  </View>
                  <View className="text-(24 #000)">x{item.cartCount}</View>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>

      <View className="bg-#fff rounded-20 px-25 mt-25">
        <View className="px-15 py-30">
          <View className="flex py-15 items-center">
            <View className="flex-1 text-(28 #000)">商品总价</View>
            <View className="text-(26 #ad7f6d)">
              &yen;{state.total.totalMoney}
            </View>
          </View>

          {!!state.total.totalOutpatientFee && (
            <View className="flex py-15 items-center">
              <View className="flex-1 text-(28 #000)">门诊费总额</View>
              <View className="text-(26 #ad7f6d)">
                &yen;{state.total.totalOutpatientFee}
              </View>
            </View>
          )}

          {!!state.total.totalServiceFee && (
            <View className="flex py-15 items-center">
              <View className="flex-1 text-(28 #000)">服务费总额</View>
              <View className="text-(26 #ad7f6d)">
                &yen;{state.total.totalServiceFee}
              </View>
            </View>
          )}

          <View className="flex py-15 items-center" onClick={openCoupon}>
            <View className="flex-1 text-(28 #000)">优惠券</View>
            {state.coupon?.id ? (
              <View className="text-(26 #ad7f6d)">
                - &yen;{state.total.couponConvertMoney}
              </View>
            ) : state.save.couponList?.length ? (
              <View className="text-(22 #ad7f6d)">
                可用 {state.save.couponList?.length} 张
              </View>
            ) : (
              <View className="text-(22 #ad7f6d)">暂无可用</View>
            )}
            <Image
              className="size-20"
              mode="aspectFit"
              src={require("src/assets/arr-p.png")}
            ></Image>
          </View>

          {!!state.save.maxUseCoin && (
            <View
              className="flex py-15 items-center"
              onClick={() => setState({ score: !state.score })}
            >
              <View className="flex-1 text-(28 #000)">
                M币抵扣
                <Text className="text-(22 #aaa) ml-10">
                  (总共{state.save.userCoin}，本单可用
                  {state.save.maxUseCoin})
                </Text>
              </View>
              <View className="text-(26 #ad7f6d)">
                - &yen;{state.total.integralConvertMoney}
              </View>
              <Image
                className="size-30 ml-10"
                mode="aspectFit"
                src={
                  state.score
                    ? require("src/assets/ck-2.png")
                    : require("src/assets/ck-1.png")
                }
              ></Image>
            </View>
          )}

          <View className="flex py-15 items-center">
            <View className="flex-1 text-(28 #000)">
              会员抵扣
              <Text className="text-(22 #aaa) ml-10">
                ({state.save.userDiscount})
              </Text>
            </View>
            <View className="text-(26 #ad7f6d)">
              - &yen;{state.total.discountPreferentialMoney}
            </View>
          </View>
        </View>
      </View>

      <View className="bg-#fff rounded-20 p-30 my-30 flex items-center">
        <Input
          placeholder="请输入订单备注"
          className="text-(26 #333) flex-1"
          value={state.remark}
          onInput={(e) => setState({ remark: e.detail.value })}
        ></Input>
      </View>

      <View>
        <View
          className={clsx(
            "z-1000 pos-fixed top-0 left-0 size-full bg-#000/80 transition-all duration-300",
            !state.open && "uno-layer-z1-(invisible opacity-0)"
          )}
          onClick={() => setState({ open: false })}
        ></View>
        <View
          className={clsx(
            "z-1000 pos-fixed left-0 bottom-0 w-full bg-#fff rounded-t-20 transition-all duration-300",
            !state.open && "uno-layer-z1-(invisible opacity-0 translate-y-100%)"
          )}
        >
          <View className="px-30 b-b-(1 solid #f5f5f5) h-100 flex items-center">
            <View className="text-(32/100 #000) fw-bold flex-1">
              选择优惠券
            </View>
            <View
              className="text-(22 #aaa) py-30"
              onClick={() => pickCoupon(null)}
            >
              取消用券
            </View>
          </View>
          <ScrollView scrollY className="h-60vh bg-#f5f5f5">
            <View className="px-30">
              {state.save.couponList?.map((item) => {
                return (
                  <View
                    className="pos-relative h-180 bg-#fff rounded-20 mt-30 flex overflow-hidden"
                    onClick={() => pickCoupon(item)}
                  >
                    <View className="pos-relative w-210 flex flex-col items-center justify-center">
                      <View className="pos-absolute top-0 right-0 size-30 rounded-full bg-#f5f5f5 translate-x-1/2 -translate-y-1/2"></View>
                      <View className="pos-absolute bottom-0 right-0 size-30 rounded-full bg-#f5f5f5 translate-x-1/2 translate-y-1/2"></View>

                      {item.type == 10 && (
                        <View className="text-(40 #ad7f6d)">
                          <Text className="text-1.3em fw-bold mr-8">
                            {item.preferentialMoney}
                          </Text>
                          元
                        </View>
                      )}

                      {item.type == 20 && (
                        <View className="text-(40 #ad7f6d)">
                          <Text className="text-1.3em fw-bold mr-8">
                            {item.discount}
                          </Text>
                          折
                        </View>
                      )}

                      <View className="text-(22 #000) mt-15">
                        满{item.minConsumeMoney}元可用
                      </View>
                    </View>
                    <View className="my-30 b-r-(2 dashed #eee)"></View>
                    <View className="flex-1 min-w-0 px-40 flex flex-col justify-center">
                      <View className="text-(36 #000) line-clamp-1">
                        {item.name}
                      </View>
                      <View className="text-(22 #aaa) mt-15">
                        有效期至
                        {dayjs(item.effectiveEndDate).format(
                          "YYYY-MM-DD HH:mm:ss"
                        )}
                      </View>
                    </View>
                  </View>
                );
              })}
            </View>
            <View className="py-30 text-(22 #999 center)">
              没有更多优惠券啦
            </View>
          </ScrollView>
        </View>
      </View>

      <>
        <View className="h-150"></View>
        <View className="z-500 pos-fixed left-0 bottom-0 w-full h-150 bg-#fff flex items-center box-border px-40">
          <View className="flex-1 min-w-0">
            <View className="text-(33 #000)">
              合计：&yen;<Text className="text-40">{state.total.payMoney}</Text>
            </View>
            <View className="text-(26 #ad7f6d) mt-10">
              共优惠：{state.total.preferentialMoney}
            </View>
          </View>
          <View
            className="w-264 h-80 rounded-full bg-#ad7f6d text-(29/80 #fff center)"
            onClick={handlePay}
          >
            去支付
          </View>
        </View>
      </>
    </View>
  );
};
