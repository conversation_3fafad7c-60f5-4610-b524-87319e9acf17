import CusApi from "@/services/CusApi";
import { fdate, str2arr } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";

export default function Page() {
  const router = useRouter();
  const [state, setState] = useSetState({ data: null as any });

  useMount(() => {
    fetchData();
  });

  const fetchData = async () => {
    const res = await CusApi.getFeedInfo({ id: router.params.id });
    res._imgs = str2arr(res?.image);
    setState({ data: res });
  };

  const handleDel = async () => {
    const md = await Taro.showModal({ title: "确定要删除条建议吗？" });
    if (!md.confirm) return;
    await CusApi.delFeed({ ids: router.params.id }, { _loading: "删除中..." });
    Taro.eventCenter.trigger("mailbox.del");
    Taro.navigateBack();
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="px-30 pt-100">
        <View className="bg-#fff rounded-10 px-25 py-50">
          <View className="text-(28/50 #1A1A1A)">{state.data?.mobile}</View>

          <View className="text-(28/50 #636363) py-20">
            {state.data?.content}
          </View>

          <View className="grid cols-3 gap-20 py-50">
            {state.data?._imgs?.map((item) => (
              <View
                className="pos-relative pt-1/1"
                key={item}
                onClick={() => {
                  Taro.previewImage({
                    current: item,
                    urls: state.data?._imgs || [],
                  });
                }}
              >
                <View className="pos-absolute top-0 left-0 size-full bg-#666 rounded-10 overflow-hidden">
                  <Image
                    className="block size-full"
                    mode="aspectFill"
                    src={item}
                  />
                </View>
              </View>
            ))}
          </View>

          <View className="pb-200">
            <View>
              <View className="inline-block text-(26 #636363) w-160">
                提交时间
              </View>
              <View className="inline-block text-(29 #11100F)">
                {fdate(state.data?.createDate, "YYYY-MM-DD HH:mm:ss")}
              </View>
            </View>
            {!!state.data?.readDate && (
              <View>
                <View className="inline-block text-(26 #636363) w-160">
                  结束时间
                </View>
                <View className="inline-block text-(29 #11100F)">
                  {fdate(state.data?.readDate, "YYYY-MM-DD HH:mm:ss")}
                </View>
              </View>
            )}
          </View>

          <View className="flex justify-end">
            <View
              className="ml-20 w-174 h-62 rounded-16 bg-#EFEFEF text-(30/62 #5A5A5A center) b-(1 solid #EFEFEF)"
              onClick={handleDel}
            >
              删除
            </View>
            {!state.data?.state ? (
              <View className="ml-20 w-174 h-62 rounded-16 bg-#EFEFEF text-(30/62 #5A5A5A center) b-(1 solid #EFEFEF) uno-layer-z1-(b-#8E4E36 text-#fff bg-#8E4E36)">
                待查看
              </View>
            ) : (
              <View className="ml-20 w-174 h-62 rounded-16 bg-#EFEFEF text-(30/62 #5A5A5A center) b-(1 solid #EFEFEF) uno-layer-z1-(b-#8E4E36 text-#8E4E36 bg-#F7F4EF)">
                已收到
              </View>
            )}
          </View>
        </View>

        <Image
          className="block w-403 h-107 mx-a mt-60"
          mode="aspectFit"
          src="https://oss.gaomei168.com/file-release/20241116/1307361119141900288.png"
        />

        <View className="text-(18 #666666 center) pt-18 pb-100">
          医生品牌 匠心交付
        </View>
      </View>
    </View>
  );
}
