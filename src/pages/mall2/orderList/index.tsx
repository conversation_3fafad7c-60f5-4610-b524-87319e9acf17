import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { useShare } from "@/stores/useShare";
import { img_share } from "@/utils/images";
import { Button, Image, Text, View } from "@tarojs/components";
import Taro, { useReachBottom, useRouter } from "@tarojs/taro";
import { useSetState } from "ahooks";
import clsx from "clsx";
import { useEffect } from "react";
import * as oact from "src/utils/orderAct";

export default () => {
  const router = useRouter();
  const [state, setState] = useSetState({
    tabId: +(router.params.id || 0),
  });

  useShare((evt: any) => {
    // console.log(evt);
    const data = evt?.target?.dataset?.share;
    const name = data?.groupData?.productName;
    const cover = data?.groupData?.productCoverImage || img_share;
    const id = data?.groupData?.marketProductId;
    const gid = data?.groupData?.id;

    return {
      title: `[拼团] ${name}`,
      imageUrl: cover,
      path: `/pages/mall2/groupInfo/index?id=${id}&gid=${gid}`,
    };
  });

  const [list, listAct] = useList({
    api: CusApi.getOrderList,
    params: oact.OrderItem.find((n) => n.id === state.tabId)?.params,
    formater: (n) => oact.patchOrder(n),
  });

  useEffect(() => {
    Taro.eventCenter.on("order.reload", listAct.reload);
    return () => {
      Taro.eventCenter.off("order.reload");
    };
  }, []);

  useEffect(() => {
    listAct.reload();
  }, [state.tabId]);

  useReachBottom(() => {
    listAct.getNext();
  });

  return (
    <View>
      <View className="z-999 pos-sticky top-0 flex bg-#fff transition-all duration-300">
        {oact.OrderItem.map((item) => (
          <View
            className={clsx(
              "flex-1 h-70 text-(24/70 #000 center)",
              state.tabId === item.id && "!text-(#ad7f6d)"
            )}
            onClick={() => setState({ tabId: item.id })}
          >
            {item.name}
          </View>
        ))}
        <View
          className="pos-absolute left-0 bottom-0 w-0 h-4 transition-all duration-300"
          style={{
            width: 100 / oact.OrderItem.length + "%",
            transform: `translateX(${state.tabId * 100}%)`,
          }}
        >
          <View className="h-4 w-46 bg-#ad7f6d mx-auto"></View>
        </View>
      </View>

      <View className="p-25">
        {list.list.map((item) => {
          return (
            <View
              className="bg-#fff rounded-20 mb-20 px-25"
              key={item.id}
              onClick={() => {
                Taro.navigateTo({
                  url: `/pages/mall2/orderInfo/index?id=${item.id}`,
                });
              }}
            >
              <View className="h-80 flex items-center b-b-(1 solid #eee)">
                <Image
                  className="size-32 mr-10"
                  mode="aspectFit"
                  src="https://vip.gaomei168.com/file-release/file/100/2024/0507/11/1237365086738030592_size_2076.png"
                ></Image>
                <View className="text-(24 #000)">{item.shopName}</View>
                <View className="text-(28 #ad7f6d) ml-auto">
                  {item._stateName}
                  {/* {oact.StateMap.find((n) => n.id === item.orderState)?.name} */}
                </View>
              </View>
              <View className="py-10">
                {item.mallOrderItemList?.map((goods) => (
                  <View className="flex py-10" key={goods.id}>
                    <Image
                      className="size-140 bg-#f5f5f5 rounded-10 overflow-hidden mr-25"
                      mode="aspectFill"
                      src={goods.productCoverImage}
                    ></Image>
                    <View className="flex-1 min-w-0  flex flex-col justify-between py-5">
                      <View className="text-(28 #000) line-clamp-1">
                        {goods.productName}
                      </View>
                      {/* {goods.productModel == 12 && (
                        <View className="text-(22 #ad7f6d)">
                          已选:{goods.serviceUserName} &yen;{goods.serviceFee}
                          /次
                        </View>
                      )} */}
                      {/* {[1, 2, 12].includes(goods.productModel) && (
                        <View className="text-(22 #ad7f6d) mt-10">
                          门诊费: &yen;{goods.outpatientFee} &nbsp;&nbsp;&nbsp;
                          {!!goods.serviceUserId && (
                            <>
                              {goods.serviceUserName}{" "}
                              {!!goods.serviceFee && (
                                <>: &yen;{goods.serviceFee || "--"}</>
                              )}
                            </>
                          )}
                        </View>
                      )} */}
                      <View className="text-(28 #000) flex">
                        <View>&yen;{goods.productPrice}</View>
                        <View className="ml-auto text-(24 #aaa)">
                          x{goods.totalCount}
                        </View>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
              <View className="b-t-(1 solid #eee) pt-20">
                <View className="text-(28 #000 right)">
                  共计：&yen;<Text className="text-35">{item.payMoney}</Text>
                </View>
                <View
                  className="flex items-center justify-end py-30"
                  onClick={(e) => e.stopPropagation()}
                >
                  {item._comment && (
                    <View
                      className="w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25"
                      onClick={() => oact.handleComment(item)}
                    >
                      去评论
                    </View>
                  )}

                  {/* {item._refundCancel && (
                    <View
                      className="w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25"
                      onClick={() =>
                        oact.handleRefundCancel(item, listAct.reload)
                      }
                    >
                      取消退款
                    </View>
                  )}

                  {item._refund && (
                    <View
                      className="w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25"
                      onClick={() => oact.handleRefund(item)}
                    >
                      申请退款
                    </View>
                  )} */}

                  {item._toMall && (
                    <View
                      className="w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25"
                      onClick={() => {
                        Taro.switchTab({ url: `/pages/tabs/cate/index` });
                      }}
                    >
                      去使用
                    </View>
                  )}

                  {item._toReservation && (
                    <View
                      className="w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25"
                      onClick={() => {
                        Taro.navigateTo({ url: `/pages/mall2/due/index` });
                      }}
                    >
                      立即预约
                    </View>
                  )}

                  {item._hx && (
                    <View
                      className="w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25"
                      onClick={() => {
                        Taro.navigateTo({
                          url: `/pages/mall2/orderInfo/index?id=${item.id}`,
                        });
                      }}
                    >
                      核销码
                    </View>
                  )}

                  {item._confirm && (
                    <View
                      className="w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25 !text-#fff !bg-#ad7f6d"
                      onClick={() => oact.handleConfirm(item, listAct.reload)}
                    >
                      确认收货
                    </View>
                  )}

                  {item._close && (
                    <View
                      className="w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25"
                      onClick={() => oact.handleClose(item, listAct.reload)}
                    >
                      取消订单
                    </View>
                  )}

                  {item._pay && (
                    <View
                      className="w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25 !text-#fff !bg-#ad7f6d"
                      onClick={() => oact.handlePay(item, listAct.reload)}
                    >
                      去付款
                    </View>
                  )}

                  {item._shareGroup && (
                    <View className="pos-relative w-178 h-68 rounded-full text-(28/68 #ad7f6d center) b-(1 solid #ad7f6d) ml-25 !text-#fff !bg-#ad7f6d">
                      <Button
                        // onClick={() => setState({ share: item })}
                        openType="share"
                        className="pos-absolute top-0 left-0 size-full opacity-0"
                        data-share={item}
                      />
                      邀请拼团
                    </View>
                  )}
                </View>
              </View>
            </View>
          );
        })}

        <View className="text-(24 #aaa center) pb-30">
          {list.more ? "加载中..." : "没有更多数据了"}
        </View>
      </View>
    </View>
  );
};
