import { Modal } from "@/components/Custom/PopUp2";
import RichContent from "@/components/RichContent";
import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { useShare } from "@/stores/useShare";
import { showToast } from "@/utils/tools";
import { Button, Image, Text, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";

export default function Minfo() {
  const router = useRouter();
  const [state, setState] = useSetState({
    data: {} as any,
    // rule: {} as any,

    open: false,
  });

  useMount(() => {
    fetchData();
    // fetchRule();
  });

  useShare(() => {
    return {
      title: state.data.name,
      path: `/pages/mall2/minfo/index?id=${router.params.id}`,
      imageUrl: state.data.coverImage,
    };
  });

  const fetchData = async () => {
    const id = router.params.id;
    const res = await CusApi.getMarketProductInfo({ id });
    setState({ data: res });
  };

  // const fetchRule = async () => {
  //   const res = await CusApi.getMarketProductRule();
  //   setState({ rule: res });
  // };

  const submit = async () => {
    Taro.showLoading({ title: "兑换中...", mask: true });

    const data = {
      productList: [{ marketId: router.params.id, totalCount: 1 }],
    };
    const order = await CusApi.addMarketOrder(data);
    await CusApi.payOrderZero({ orderId: order.id });
    Taro.hideLoading();
    setState({ open: false });
    showToast("兑换成功");
    // Taro.showModal({ title: "兑换成功", showCancel: false });
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="h-750 bg-#aaa">
        <Image
          className="block size-full"
          mode="aspectFill"
          src={state.data.coverImage || ""}
        />
      </View>
      <View className="bg-#fff mb-15 px-55">
        <View className="text-#823C1E py-30">
          <Text className="text-44 fw-bold">{state.data.saleCoin ?? "--"}</Text>
          <Text className="text-35 ml-3">M币</Text>
        </View>
        <View className="text-(33 #11100F) pb-30">
          {state.data.name || "--"}
        </View>
      </View>
      <View className="h-94 px-55 bg-#fff text-(26/94 #11100F)">商品详情</View>
      <View className="px-55 py-35">
        <RichContent html={state.data.content} />
      </View>
      <View className="h-130"></View>
      <View className="z-50 pos-fixed left-0 bottom-0 w-full">
        <View className="h-130 bg-#fff flex items-center pr-25">
          <View className="pos-relative flex flex-col items-center px-55">
            <UnoIcon className="i-icon-park-outline:share size-34 text-#343434" />
            <View className="text-(20 #343434) mt-5">分享</View>
            <Button
              openType="share"
              className="pos-absolute top-0 left-0 size-full opacity-0"
            ></Button>
          </View>
          <View
            className="ml-a w-410 h-80 rounded-full bg-#8E4E36 text-(30/80 #fff center)"
            onClick={() => setState({ open: true })}
          >
            确认兑换
          </View>
        </View>
      </View>

      <Modal open={state.open} onClose={() => setState({ open: false })}>
        <View className="flex flex-col items-center">
          <View className="text-(40 #fff) mb-25">确认兑换</View>
          <View
            className="box-border p-50 w-488 min-h-420 bg-#fff rounded-30 flex flex-col items-center justify-center"
            style={{
              backgroundImage: `linear-gradient(0deg, rgba(226,199,171,0.7), rgba(255,255,255,0.7))`,
            }}
          >
            <View className="text-(32 #666)">
              消耗{state.data.saleCoin}M币兑换
            </View>

            <View className="text-(60 #8E4E36 center) pt-50">
              {state.data.name}
            </View>
          </View>
          <View className="flex mt-40">
            <View
              className="mr-42 w-180 h-74 rounded-full bg-#7C7C7C text-(36/74 center #fff)"
              onClick={() => setState({ open: false })}
            >
              再想想
            </View>
            <View
              className="w-234 h-74 rounded-full bg-#B7684A text-(36/74 center #fff)"
              onClick={submit}
            >
              确认兑换
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
