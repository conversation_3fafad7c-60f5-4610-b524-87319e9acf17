import { PopUp } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { img_avatar } from "@/utils/images";
import { Image, Text, View } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { WeCountDown } from "./CountDown";

const GroupState = [
  { id: 10, name: "拼团中" },
  { id: 20, name: "拼团成功" },
  { id: 30, name: "拼团失败" },
];

export const GroupInfo = (props: {
  open?: boolean;
  onClose?: any;
  data?: any;
  onJoin?: any;
}) => {
  const router = useRouter();
  const [state, setState] = useSetState({
    open: false,
    data: null as any,
  });

  useMount(() => {
    fetchGroup();
  });

  const onClose = () => {
    setState({ open: false });
  };

  const fetchGroup = async () => {
    const id = router.params.gid || "";
    if (!id) return;
    const res = await CusApi.getGroupInfo({ id });
    setState({ data: res, open: true });
  };

  const data = state.data;

  const onJoin = async () => {
    props.onJoin?.(state.data);
  };

  return (
    <PopUp open={state.open} onClose={onClose}>
      <View className="bg-#F7F4EF">
        <View className="pt-80 px-100">
          <View className="text-(36 #333) fw-bold">{data?.productName}</View>
        </View>
        <View className="flex items-center py-50">
          <View className="flex-1 min-w-0 flex flex-col items-center px-70 text-#8D451F ">
            <View className="flex flex-col items-center ml-a">
              <View className="text-20 mb-5">拼团价</View>
              <View className="text-50 fw-bold first-letter:(text-30 mr-8)">
                &yen;{data?.productPrice}
              </View>
            </View>
          </View>

          <View className="w-4 h-70 rounded-full bg-#ddd"></View>

          <View className="flex-1 min-w-0 flex flex-col items-center px-70 text-#333 ">
            <View className="flex flex-col items-center mr-a">
              <View className="text-20 mb-5">平台价</View>
              <View className="text-50 fw-bold first-letter:(text-30 mr-8)">
                &yen;{data?.product?.salePrice}
              </View>
            </View>
          </View>
        </View>

        <View className="flex items-center px-50">
          <View className="flex-1">
            <View className="text-(24 #333)">
              <Text className="text-(24 #999) fw-bold mr-20">
                {GroupState.find((n) => n.id == data?.state)?.name}
              </Text>
              已有{data?.joinNum}人参与拼团({data?.joinNum}/{data?.groupNum})
            </View>
            {/* {data?.state == 10 && (
              <View className="text-(24 #999) mt-8">
                剩余拼团时间{" "}
                <CountDown
                  className="!inline-block !text-(24 #999)"
                  endTime={data?.endDate}
                />
              </View>
            )} */}
          </View>
          <View className="text-(20 #8D451F) b-(1 solid #8D451F) rounded-full py-8 px-15">
            {data?.groupNum}人团
          </View>
        </View>

        <View className="flex flex-wrap justify-center py-50 px-100">
          {data?.userList?.map((item, idx) => {
            return (
              <View className="pos-relative size-100 m-15" key={item.id}>
                <Image
                  className="block size-full bg-#eee rounded-full"
                  mode="aspectFill"
                  src={item.photo || img_avatar}
                />
                {!idx && (
                  <View className="pos-absolute bottom-0 left-1/2 -translate-x-1/2 bg-#8E4E36 rounded-14 px-15 py-5 ws-nowrap text-(20 #fff)">
                    团长
                  </View>
                )}
              </View>
            );
          })}

          {data?.state == 10 && (
            <View className="pos-relative size-100 m-15">
              <View className="size-full bg-#eee rounded-full box-border b-(2 dashed #8E4E36) flex items-center justify-center">
                <UnoIcon className="i-icon-park-outline:plus size-30 text-#8E4E36" />
              </View>
              {/* <Button
                className="pos-absolute top-0 left-0 size-full opacity-0"
                openType="share"
              ></Button> */}
            </View>
          )}
        </View>

        <View>
          <View className="text-(26 center #333) mt-8 pb-20">
            剩余拼团时间{" "}
            <WeCountDown className="!inline-flex" end={data?.endDate} />
          </View>
        </View>

        {data?.state == 10 && (
          <>
            {/* {need > 0 ? (
              <View className="text-(26 #666 center) pb-80">
                仅需邀请
                <Text className="text-#8E4E36">{need}人</Text>
                ，即可完成
              </View>
            ) : (
              <View className="text-(26 #666 center) pb-80">邀请更多好友</View>
            )} */}

            <View className="pb-50 px-50" onClick={() => onJoin()}>
              <View className="pos-relative h-80 bg-#8E4E36 b-(1 solid #8E4E36) text-(30/80 #fff center) rounded-10 my-10">
                立即加入
              </View>
            </View>
          </>
        )}

        {data?.state == 20 && (
          <>
            <View>
              <View className="h-100 bg-#B5B5B5 text-(36/100 #fff center)">
                当次团购已结束
              </View>
            </View>
          </>
        )}

        {data?.state == 30 && (
          <View>
            <View className="h-100 bg-#B5B5B5 text-(36/100 #fff center)">
              当次团购已结束
            </View>
          </View>
        )}
      </View>
    </PopUp>
  );
};
