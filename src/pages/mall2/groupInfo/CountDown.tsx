import { View } from "@tarojs/components";
import { useCountDown } from "ahooks";
import { useMemo } from "react";

const pad = (n = 0) => (n < 10 ? "0" + n : n + "");

export const WeCountDown = (props: { end?: number; className?: string }) => {
  const [cd] = useCountDown({
    targetDate: props.end,
  });

  const cdf = useMemo(() => {
    const ss = pad(Math.floor((cd / 1000) % 60));
    const mm = pad(Math.floor((cd / 1000 / 60) % 60));
    const HH = pad(Math.floor((cd / 1000 / 60 / 60) % 24));
    const D = Math.floor(cd / 1000 / 60 / 60 / 24);

    return { D, HH, mm, ss } as any;
  }, [cd]);

  return (
    <View
      className={
        "cd flex items-center text-(24 #111) fw-bold " + props.className
      }
    >
      {cdf.D != 0 && (
        <>
          <View className="cd-i size-36 bg-#111 flex items-center justify-center rounded-6 text-#fff">
            {cdf.D}
          </View>
          <View className="cd-g px-5">天</View>
        </>
      )}
      <View className="cd-i size-36 bg-#111 flex items-center justify-center rounded-6 text-#fff">
        {cdf.HH}
      </View>
      <View className="cd-g px-5">:</View>
      <View className="cd-i size-36 bg-#111 flex items-center justify-center rounded-6 text-#fff">
        {cdf.mm}
      </View>
      <View className="cd-g px-5">:</View>
      <View className="cd-i size-36 bg-#111 flex items-center justify-center rounded-6 text-#fff">
        {cdf.ss}
      </View>
    </View>
  );
};
