import RichContent from "@/components/RichContent";
import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { openConcat, showToast } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { SkuPick } from "./SkuPick";
import { GroupInfo } from "./GroupInfo";
import { WeCountDown } from "./CountDown";
import { useShare } from "@/stores/useShare";
import { CanvasShare } from "./CanvasShare";
import { GroupList } from "./GroupList";
import { Bubble } from "./Bubble";
import { usePageStat } from "@/stores/usePageStat";

export default function Page() {
  const router = useRouter();
  const [state, setState] = useSetState({
    data: null as any,

    groupId: null as any,
    // noGroup: false,

    skuOpen: false,

    shareOpen: false,
  });

  usePageStat({
    id: state.data?.id,
    title: state.data?.name,
  });

  useMount(() => {
    fetchData();
  });

  useShare(() => {
    return {
      title: `[拼团] ` + state.data?.name,
      imageUrl: state.data?.coverImage,
      path: `/pages/mall2/groupInfo/index?id=${router.params.id}`,
    };
  });

  const fetchData = async () => {
    const id = router.params.id;
    const res = await CusApi.getMarketProductInfo({ id });
    setState({ data: res });
  };

  const makeGroup = () => {
    setState({ skuOpen: true, groupId: "" });
  };

  // const makeNoGroup = () => {
  //   setState({ skuOpen: true, groupId: "", noGroup: true });
  // };

  const joinGroup = async (data: any) => {
    setState({ skuOpen: true, groupId: data?.id });
  };

  const joinFirstGroup = async () => {
    const res = await CusApi.getGrouping({ marketProductId: router.params.id });
    const data = res?.list?.[0];
    if (!data) return showToast("未获取到开团信息");
    joinGroup(data);
  };

  // const makeAdminGroup = async () => {
  //   const m = await Taro.showModal({ title: "确定发起开团？" });
  //   if (!m.confirm) return;
  //   await CusApi.makeAdminGroup({
  //     marketProductId: router.params.id,
  //   });
  //   showToast("发起成功");
  //   fetchData();
  // };

  // const joinAdminGroup = async () => {
  //   const res = await CusApi.getGrouping({ marketProductId: router.params.id });
  //   const data = res?.list?.[0];
  //   if (!data) return showToast("未获取到开团信息");
  //   joinGroup(data);
  // };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="pos-relative">
        <Image
          className="block w-full h-750 bg-#eee"
          mode="aspectFill"
          src={state.data?.coverImage}
        />
        {!!state.data?.id && <Bubble></Bubble>}
      </View>
      <View className="px-30">
        <View className="bg-#fff rounded-20 mt-20 p-30">
          <View className="flex items-center">
            <View className="flex items-center">
              <View className="text-(66 #000) fw-bold first-letter:(text-25 mr-5)">
                &yen;{state.data?.salePrice}
              </View>
              <View className="ml-10">
                <View className="text-(24 #333)">拼团价</View>
                <View className="text-(24 #999) line-through">
                  原价{state.data?.product?.salePrice}
                </View>
              </View>
            </View>
            {state.data?.saleTimeLimit != 0 && (
              <View className="ml-a flex flex-col">
                <View className="ml-a text-(24 #444 right) flex items-center">
                  <UnoIcon className="i-icon-park-outline:alarm-clock size-30 text-#444 mr-10" />
                  限时拼团
                </View>
                <View className="text-(22 #666 right) flex items-center mt-8">
                  <View>仅剩</View>
                  <WeCountDown className="ml-8" end={state.data?.saleEndDate} />
                </View>
              </View>
            )}
          </View>

          <View className="text-(34 #000) pb-30 pt-20 leading-normal">
            {state.data?.name}
          </View>
        </View>

        <GroupList total={state.data?.soldCount} onJoin={joinGroup} />
      </View>

      <View className="text-(26 #111 center) py-40">项目详情</View>
      <View className="">
        <RichContent html={state.data?.content} />
      </View>

      <View className="h-160"></View>
      <View className="pos-fixed left-0 bottom-0 z-50 h-160 w-full bg-#fff box-border flex items-center">
        <View
          className="flex flex-col items-center justify-center px-30"
          onClick={() => openConcat()}
        >
          <Image
            className="size-50"
            mode="scaleToFill"
            src="https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241019/1297147475462459392.png"
          ></Image>
          {/* <UnoIcon className="i-icon-park-outline:message-one size-50 text-#333" /> */}
          <View className="text-(22 #333) mt-5">立即咨询</View>
        </View>

        <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #8E4E36)">
          {state.data?.openGroupStyle == 10 && (
            <>
              <View
                className="flex-1 w-200 text-(30/80 #fff center) bg-#8E4E36"
                onClick={makeGroup}
              >
                发起拼团
              </View>
            </>
          )}
          {state.data?.openGroupStyle == 20 && (
            <>
              <View
                className="flex-1 w-200 text-(30/80 #fff center) bg-#8E4E36"
                onClick={joinFirstGroup}
              >
                加入拼团
              </View>
            </>
          )}
        </View>

        {/* <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #8E4E36)">
          {state.data?.openGroupStyle != 20 ? (
            <>
              <View
                className="flex-1 w-200 text-(30/80 #8E4E36 center) bg-#fff"
                onClick={makeNoGroup}
              >
                直接购买
              </View>
              <View
                className="flex-1 w-200 text-(30/80 #fff center) bg-#8E4E36"
                onClick={makeGroup}
              >
                发起拼团
              </View>
            </>
          ) : (
            <>
              {state.admin?.internalGroup == 1 ? (
                <View
                  className="flex-1 w-200 text-(30/80 #fff center) bg-#8E4E36"
                  onClick={makeAdminGroup}
                >
                  内部开团
                </View>
              ) : (
                <View
                  className="flex-1 w-200 text-(30/80 #fff center) bg-#8E4E36"
                  onClick={joinAdminGroup}
                >
                  加入拼团
                </View>
              )}
            </>
          )}
        </View> */}
      </View>

      <GroupInfo onJoin={(data) => joinGroup(data)}></GroupInfo>

      {state.data?.id && (
        <SkuPick
          open={state.skuOpen}
          onClose={() => setState({ skuOpen: false })}
          data={state.data}
          // noGroup={state.noGroup}
          groupId={state.groupId}
        ></SkuPick>
      )}

      <View
        className="z-40 pos-fixed right-30 bottom-200 bg-#8E4E36 rounded-full flex flex-col items-center justify-center size-80"
        onClick={() => setState({ shareOpen: true })}
      >
        <UnoIcon className="i-icon-park-outline:share size-30 text-#fff" />
        <View className="text-(20 #fff) mt-4">分享</View>
      </View>

      {!!state.data?.id && (
        <CanvasShare
          data={state.data}
          open={state.shareOpen}
          onOK={() => setState({ shareOpen: false })}
        />
      )}
    </View>
  );
}
