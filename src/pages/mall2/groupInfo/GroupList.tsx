import CusApi from "@/services/CusApi";
import { img_avatar } from "@/utils/images";
import {
  Image,
  ScrollView,
  Swiper,
  SwiperItem,
  View,
} from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { WeCountDown } from "./CountDown";
import { UnoIcon } from "@/components/UnoIcon";
import { Modal } from "@/components/Custom/PopUp2";

export const GroupList = (props: { total: number; onJoin: any }) => {
  const router = useRouter();
  const [state, setState] = useSetState({
    list: [] as any[],
    open: false,
  });

  useMount(async () => {
    const res = await CusApi.getGrouping({
      marketProductId: router.params.id,
      pageSize: 9999,
    });

    let list: any[] = res?.list || [];

    list.forEach((item) => {
      let users = item.userList || [];

      if (!users.length) {
        users = [{ id: "-1", name: "***" }];
      }

      users = users.map((u) => ({ ...u, photo: u.photo || img_avatar }));
      users = users.reverse().slice(0, 2);

      item._users = users;
      item._names = users.map((n) => n.name).join(",");
      // item._lastName = users[users.length - 1].name;
      item._num = Math.max(0, item.groupNum - item.joinNum);
    });

    // const total = list.reduce((p, c) => p + c.joinNum, 0);

    setState({ list });
  });

  const onJoin = (item) => {
    setState({ open: false });
    props.onJoin(item);
  };

  if (!state.list.length) return null;

  return (
    <>
      <View className="px-30 bg-#fff rounded-20 mt-20 bg-[linear-gradient(0deg,rgba(226,199,171,0.7),rgba(255,255,255,0.7))]">
        <View
          className="flex items-center py-20"
          onClick={() => setState({ open: true })}
        >
          <View className="text-(22 #111)">
            {props.total || 0}人正在拼团，参与立即拼成
          </View>
          <UnoIcon className="ml-a i-icon-park-outline:right size-30 text-#666" />
        </View>
        <Swiper
          vertical
          displayMultipleItems={2}
          autoplay
          interval={5000}
          circular
          className="h-200"
        >
          {state.list.map((item) => (
            <SwiperItem className="h-100">
              <View className="flex items-center h-100" key={item.id}>
                <View className="flex items-center">
                  {item._users.map((user) => (
                    <Image
                      key={user.id}
                      className="size-60 rounded-full bg-#ddd -ml-25 first:ml-0"
                      src={user.photo}
                    />
                  ))}
                </View>
                <View className="flex-1 mx-10 text-(26 #333) line-clamp-1">
                  {item._names}
                </View>
                <View className="text-(22 #666) px-15">
                  <WeCountDown
                    className="inline-flex  mr-5"
                    end={item.endDate}
                  />
                  后结束
                </View>
                <View
                  className="text-(22 #fff) px-15 py-8 rounded-full bg-#8E4E36 pos-relative"
                  onClick={() => onJoin(item)}
                >
                  {!!item._num && (
                    <View
                      className="bg-#F7F4E5 text-(16 #8E4E36) pos-absolute bottom-1/1 right-0 py-5 px-10 translate-y-6"
                      style={{ borderRadius: "100rpx 100rpx 100rpx 0" }}
                    >
                      还差{item._num}人
                    </View>
                  )}
                  直接拼成
                </View>
              </View>
            </SwiperItem>
          ))}
        </Swiper>
      </View>

      <Modal open={state.open} onClose={() => setState({ open: false })}>
        <View className="w-660 rounded-30 bg-#fff bg-[linear-gradient(0deg,rgba(226,199,171,0.7),rgba(255,255,255,0.7))]">
          <View className="text-(30 #000 center) py-30">可参与的拼单</View>
          <ScrollView scrollY className="max-h-600">
            <View className="px-30 pb-40">
              {state.list.map((item) => (
                <View className="flex items-center h-100" key={item.id}>
                  <View className="flex items-center">
                    {item._users.map((user) => (
                      <Image
                        key={user.id}
                        className="size-60 rounded-full bg-#ddd -ml-25 first:ml-0"
                        src={user.photo}
                      />
                    ))}
                  </View>
                  <View className="flex-1 mx-10 text-(26 #333) line-clamp-1">
                    {item._names}
                  </View>
                  <View className="text-(22 #666) px-15">
                    <WeCountDown
                      className="inline-flex  mr-5"
                      end={item.endDate}
                    />
                    后结束
                  </View>
                  <View
                    className="text-(22 #fff) px-15 py-8 rounded-full bg-#8E4E36 pos-relative"
                    onClick={() => onJoin(item)}
                  >
                    {!!item._num && (
                      <View
                        className="bg-#F7F4E5 text-(16 #8E4E36) pos-absolute bottom-1/1 right-0 py-5 px-10 translate-y-6"
                        style={{ borderRadius: "100rpx 100rpx 100rpx 0" }}
                      >
                        还差{item._num}人
                      </View>
                    )}
                    直接拼成
                  </View>
                </View>
              ))}
            </View>
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};
