import { Modal } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import { Button, Image, View } from "@tarojs/components";
import { WeCountDown } from "../groupInfo/CountDown";
import Taro from "@tarojs/taro";
import { useEffect } from "react";
import { useSetState } from "ahooks";
import CusApi from "@/services/CusApi";
import { img_share } from "@/utils/images";
import { useShare } from "@/stores/useShare";

export const ResModal = (props: { open?: boolean; oid: any }) => {
  const [state, setState] = useSetState({
    data: null as any,
  });

  useEffect(() => {
    if (props.open && props.oid) {
      fetchData();
    }
  }, [props.open, props.oid]);

  useShare(() => {
    const data = state.data;
    const name = data?.groupData?.productName;
    const cover = data?.groupData?.productCoverImage || img_share;
    const id = data?.groupData?.marketProductId;
    const gid = data?.groupData?.id;

    return {
      title: `[拼团] ${name}`,
      imageUrl: cover,
      path: `/pages/mall2/groupInfo/index?id=${id}&gid=${gid}`,
    };
  });

  const fetchData = async () => {
    const res = await CusApi.getOrderInfo({ id: props.oid });
    setState({ data: res });
  };

  const group = state.data?.groupData;
  const dif = Math.max(0, group?.groupNum - group?.joinNum) || 0;
  const isGroupFinish = dif <= 0;

  return (
    <Modal open={props.open}>
      <View className="w-600 bg-#8E4E36 overflow-hidden rounded-20">
        <View className="py-30 flex items-center justify-center">
          <UnoIcon className="i-icon-park-outline:check-one size-30 text-#fff mr-10" />
          <View className="text-(30 #fff)">拼团成功</View>
        </View>
        <View
          className="rounded-20 bg-#fff px-30 py-40"
          style={{ borderRadius: "20rpx 20rpx 0 0" }}
        >
          {isGroupFinish ? (
            <View className="text-(30 #333 center) pb-20">当前已成团</View>
          ) : (
            <>
              <View className="text-(30 #333 center) pb-20">
                还差 {dif}人 即可拼成
              </View>
              <View className="text-(26 #666 center)">
                时间仅剩{" "}
                <WeCountDown className="!inline-flex" end={group?.endDate} />
              </View>
            </>
          )}

          <View className="py-30 flex items-center justify-center">
            {group?.userList?.slice(0, 3).map((user) => (
              <Image
                key={user.id}
                mode="aspectFill"
                className="size-80 m-10 rounded-full bg-#eee"
                src={user.photo}
              />
            ))}
            {!isGroupFinish && (
              <View className="box-border b-(1 dashed #8E4E36) size-80 m-10 rounded-full bg-#eee flex items-center justify-center">
                <UnoIcon className="i-icon-park-outline:plus size-26 text-#8E4E36" />
              </View>
            )}
          </View>

          <View className="flex items-center gap-20">
            {!isGroupFinish && (
              <View className="pos-relative flex-[2] ml-20 h-80 b-(1 solid #8D451F) rounded-full bg-#8E4E36 text-(30/80 #fff center)">
                <Button
                  className="pos-absolute top-0 left-0 size-full opacity-0"
                  openType="share"
                ></Button>
                邀请好友参团
              </View>
            )}
            <View
              className="flex-1 h-80 b-(1 solid #8D451F) rounded-full bg-#fff text-(30/80 #8D451F center)"
              onClick={() => {
                Taro.navigateBack({ delta: 2 });
              }}
            >
              返回
            </View>
          </View>
          <View className="text-(24 #999 center) mt-20">
            未成团订单原路退款
          </View>
        </View>
      </View>
    </Modal>
  );
};
