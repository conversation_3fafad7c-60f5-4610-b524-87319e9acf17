import { UnoIcon } from "@/components/UnoIcon";
import { View } from "@tarojs/components";
import clsx from "clsx";

export const CusRadio = (props: {
  active?: boolean;
  disabled?: boolean;
  onClick?: any;
  className?: string;
  children?: any;
}) => {
  // const hasDis = props.disabled !== undefined;

  return (
    <View
      className={clsx(
        "pos-relative box-border b-(1 solid #f5f5f5) bg-#f5f5f5 rounded-10 text-(25 #666) flex items-center justify-center",
        // hasDis && "uno-layer-z1-(!b-#F7F4EF !bg-#F7F4EF !text-#955A44)",
        props.active && "!bg-#F7F4EF !b-#8E4E36 !text-#955A44",
        props.className
      )}
      onClick={!props.disabled && props.onClick}
    >
      {props.children}
      {props.active && (
        <>
          <View className="pos-absolute top-0 right-0 b-l-(40 solid transparent) b-t-(40 solid #8E4E36)"></View>
          <UnoIcon className="pos-absolute top-0 right-0 i-icon-park-outline:check-small size-20 fw-bold text-#fff" />
        </>
      )}
    </View>
  );
};

export const CusRadio2 = (props: {
  active?: boolean;
  disabled?: boolean;
  onClick?: any;
  className?: string;
  children?: any;
}) => {
  return (
    <View
      className={clsx(
        "pos-relative box-border b-(1 solid #F7F4EF) bg-#F7F4EF rounded-10 text-(25 #955A44) flex items-center justify-center",
        !!props.disabled && "uno-layer-z1-(!b-#F5F5F5 !bg-#F5F5F5 !text-#666)",
        props.active && "!bg-#F7F4EF !b-#8E4E36 !text-#955A44",
        props.className
      )}
      onClick={!props.disabled && props.onClick}
    >
      {props.children}
      {props.active && (
        <>
          <View className="pos-absolute top-0 right-0 b-l-(40 solid transparent) b-t-(40 solid #8E4E36)"></View>
          <UnoIcon className="pos-absolute top-0 right-0 i-icon-park-outline:check-small size-20 fw-bold text-#fff" />
        </>
      )}
    </View>
  );
};
