import CusApi from "@/services/CusApi";
import { fetchShopsByLocation, showToast, str2arr } from "@/utils/tools";
import { Image, ScrollView, Text, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState, useUpdateEffect } from "ahooks";
import clsx from "clsx";

const shadow = {
  boxShadow: `-2rpx 2rpx 5rpx 0px rgba(0,0,0,.2), inset -2rpx 2rpx 5rpx 0 #fff`,
};

export default function Page() {
  const [state, setState] = useSetState({
    shops: [] as any[],
    shop: null as any,
    doctors: [] as any[],
  });

  useMount(() => {
    fetchShops();
  });

  useUpdateEffect(() => {
    fetchDoctors();
  }, [state.shop?.id]);

  const fetchShops = async () => {
    const shops = await fetchShopsByLocation({
      shopState: "10,20,80",
    });
    setState({ shops, shop: shops?.[0] });
  };

  const fetchDoctors = async () => {
    const id = state.shop?.id;
    // if (!id) return setState({ doctors: [] });
    const res = await CusApi.getDoctor({ shopId: id, pageSize: 9999 });
    const list = res?.list || [];
    const doctors = list?.map((item) => ({
      ...item,
      _adeptInfo: str2arr(item.adeptInfo),
    }));
    setState({ doctors });
  };

  return (
    <View className="min-h-100vh bg-#F6F6F6">
      <Image
        className="block w-full h-280 bg-#eee"
        mode="aspectFill"
        src="https://oss.gaomei168.com/file-release/20250311/1348970137467490304_750_279.png"
      />

      <View className="h-190 flex flex-col items-center justify-center">
        <View className="text-(24 #333)">一家医生</View>
        <View className="text-(24 #333) mt-5">联合创办的医美诊所</View>
        <View className="text-(12 #666) mt-10">a doctor co-founded</View>
        <View className="text-(12 #666)">medical aesthetic clinic</View>
      </View>

      <View className="h-430 overflow-hidden">
        <ScrollView scrollX className="h-500">
          <View className="ws-nowrap fz-0">
            {state.shops.map((item) => (
              <View
                key={item.id}
                className={clsx(
                  "ws-normal inline-block w-256 h-420 mr-20 rounded-20 overflow-hidden first:ml-55 last:mr-55 box-border p-1",
                  item.id == state.shop?.id && "!p-0 b-(1 solid #8E4E36)"
                )}
                style={shadow}
                onClick={() => {
                  if (item.shopState == 80) return;
                  setState({ shop: item });
                }}
              >
                <View className="pos-relative h-210 bg-#282828">
                  <Image
                    className="block size-full"
                    mode="aspectFill"
                    src={item.logo}
                  />
                  {item.shopState == 80 && (
                    <View className="pos-absolute top-0 left-0 size-full bg-#000/60 text-(34 #fff/90) font-italic flex flex-col items-center justify-center">
                      <View>敬 请</View>
                      <View>期 待</View>
                    </View>
                  )}
                </View>
                <View className="text-(26 #11100F center) mt-40 fw-bold">
                  {item.shortName}
                </View>
                <View className="text-(14 #8D8C8A center) mt-5">
                  Clinic Location
                </View>
                <View className="text-(18/22 #1E1C1D center) mx-35 mt-30">
                  {item.fullAddress}
                </View>
              </View>
            ))}

            {/* <View
              className="ws-normal inline-block w-256 h-420 mr-20 rounded-20 overflow-hidden first:ml-55 last:mr-55 box-border"
              style={shadow}
            >
              <View className="h-210 bg-#282828">
                <Image
                  className="block size-full"
                  mode="aspectFill"
                  src="https://oss.gaomei168.com/file-release/20250311/1348965050372329472_259_213.png"
                />
              </View>
              <View className="text-(26 #11100F center) mt-40 fw-bold">
                重庆礼嘉店
              </View>
              <View className="text-(14 #8D8C8A center) mt-5">
                Clinic Location
              </View>
              <View className="text-(18/22 #1E1C1D center) mx-35 mt-30">
                重庆市渝北区礼嘉
              </View>
            </View> */}
          </View>
        </ScrollView>
      </View>

      <View
        className="flex items-center px-35"
        onClick={() => {
          Taro.openLocation({
            latitude: state.shop?.latitude,
            longitude: state.shop?.longitude,
            scale: 18,
            name: state.shop?.name,
            address: state.shop?.fullAddress,
            fail: () => showToast("打开地址失败"),
          });
        }}
      >
        <View className="flex-1 min-w-0 py-45">
          <View className="text-(30 #333) line-clamp-1">
            {state.shop?.name}
          </View>
          <View className="text-(26 #666) mt-8 line-clamp-1">
            {state.shop?.fullAddress}
          </View>
        </View>
        <Image
          className="size-42"
          mode="aspectFit"
          src="https://oss.gaomei168.com/file-release/20250311/1348971489685934080_200_200.png"
        />
      </View>

      <View className="flex items-center px-35 pb-40">
        <View className="text-(32 #8F431F) pr-10 fw-bold">营业中</View>
        <View className="flex-1 text-(28 #666)">10:00~18:00 (周日休息)</View>
        <View
          className="size-66 rounded-full bg-#eee overflow-hidden flex items-center justify-center ml-40"
          onClick={() => {
            Taro.makePhoneCall({ phoneNumber: state.shop?.contactPhone });
          }}
        >
          <Image
            className="size-38"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250311/1348973383355469825_200_200.png"
          />
        </View>
        <View
          className="size-66 rounded-full bg-#eee overflow-hidden flex items-center justify-center ml-40"
          onClick={() => {
            Taro.navigateTo({ url: `/pages/mall2/due/index` });
          }}
        >
          <Image
            className="size-38"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250311/1348973383355469824_200_200.png"
          />
        </View>
      </View>

      <View className="mx-35 bg-#FAFAFA rounded-30 " style={shadow}>
        <View className="text-(32 #333) py-30 px-25 fw-bold">近期出诊医生</View>
        {state.doctors.map((item) => (
          <View
            className="px-60"
            key={item.id}
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/mall2/goodDoctor2/index?id=${item.id}`,
              });
            }}
          >
            <View className="flex py-30">
              <Image
                className="w-170 h-218 bg-#BFBFBF mr-90"
                mode="aspectFill"
                src={item.coverImage}
              />
              <View className="flex-1 min-w-0">
                <View className="text-(26 #11100F) fw-bold mt-20">
                  <Text>{item.name}</Text>
                  <Text className="fw-normal italic ml-10">{item.alias}</Text>
                </View>
                <View className="text-(28 #11100F) fw-bold mt-20">
                  搞美严选医生
                </View>
                <View className="flex flex-wrap gap-20 b-t-(1 solid #F8F4F1) pt-5 mt-15">
                  {item._adeptInfo?.map((sub) => (
                    <View key={sub} className="text-(20 #666)">
                      {sub}
                    </View>
                  ))}
                </View>
              </View>
            </View>
            <View className="b-t-(1 solid #F8F4F1) text-(22 #666) py-25">
              执业编号：{item.practiceNo}
            </View>
          </View>
        ))}
      </View>

      <View className="mx-35 bg-#FAFAFA rounded-30 mt-60" style={shadow}>
        <View className="text-(32 #333) py-30 px-25 fw-bold">服务设施</View>
        <View className="flex pb-45 pt-20">
          <View className="flex-1 flex flex-col items-center justify-center">
            <Image
              className="size-60"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250311/1349024243708071936_200_200.png"
            />
            <View className="text-(24 #333) mt-10">茶歇小食</View>
          </View>
          <View className="flex-1 flex flex-col items-center justify-center">
            <Image
              className="size-60"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250311/1349024243682906112_200_200.png"
            />
            <View className="text-(24 #333) mt-10">免费卸妆</View>
          </View>
          <View className="flex-1 flex flex-col items-center justify-center">
            <Image
              className="size-60"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250311/1349024243687100416_200_200.png"
            />
            <View className="text-(24 #333) mt-10">术后护理</View>
          </View>
          <View className="flex-1 flex flex-col items-center justify-center">
            <Image
              className="size-60"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250311/1349024243699683328_200_200.png"
            />
            <View className="text-(24 #333) mt-10">边界感服务</View>
          </View>
        </View>
      </View>

      <View className="px-60">
        <View className="text-(32 #333 center) pb-40 pt-60">·品牌承诺·</View>
        <View className="grid cols-3 gap-30">
          <View className="h-250 rounded-6 bg-#eee flex flex-col items-center justify-center">
            <View className="">
              <Text className="text-(28 #8F431F) italic">01</Text>
              <Text className="text-(28 #333) ml-5">·</Text>
            </View>
            <View className="text-(26 #333) fw-bold mt-10">无隐形消费</View>
            <View className="text-(20 #666) mt-35">无AB价格表</View>
            <View className="text-(20 #666) mt-5">价格完全透明</View>
          </View>

          <View className="h-250 rounded-6 bg-#eee flex flex-col items-center justify-center">
            <View className="">
              <Text className="text-(28 #8F431F) italic">02</Text>
              <Text className="text-(28 #333) ml-5">·</Text>
            </View>
            <View className="text-(26 #333) fw-bold mt-10">正品保证</View>
            <View className="text-(20 #666) mt-35">资质齐全</View>
            <View className="text-(20 #666) mt-5">360度随便扫码验</View>
          </View>

          <View className="h-250 rounded-6 bg-#eee flex flex-col items-center justify-center">
            <View className="">
              <Text className="text-(28 #8F431F) italic">03</Text>
              <Text className="text-(28 #333) ml-5">·</Text>
            </View>
            <View className="text-(26 #333) fw-bold mt-10">服务体验</View>
            <View className="text-(20 #666) mt-35">您需要什么</View>
            <View className="text-(20 #666) mt-5">我们尽可能提供什么</View>
          </View>

          <View className="h-250 rounded-6 bg-#eee flex flex-col items-center justify-center">
            <View className="">
              <Text className="text-(28 #8F431F) italic">04</Text>
              <Text className="text-(28 #333) ml-5">·</Text>
            </View>
            <View className="text-(26 #333) fw-bold mt-10">流程公开可视</View>
            <View className="text-(20 #666) mt-35">流程透明</View>
            <View className="text-(20 #666) mt-5">拒绝套路</View>
          </View>

          <View className="h-250 rounded-6 bg-#eee flex flex-col items-center justify-center">
            <View className="">
              <Text className="text-(28 #8F431F) italic">05</Text>
              <Text className="text-(28 #333) ml-5">·</Text>
            </View>
            <View className="text-(26 #333) fw-bold mt-10">医生主导</View>
            <View className="text-(20 #666) mt-35">医生掌握话语权</View>
            <View className="text-(20 #666) mt-5">不存在“销售业绩”</View>
          </View>

          <View className="h-250 rounded-6 bg-#eee flex flex-col items-center justify-center">
            <View className="">
              <Text className="text-(28 #8F431F) italic">06</Text>
              <Text className="text-(28 #333) ml-5">·</Text>
            </View>
            <View className="text-(26 #333) fw-bold mt-10">个性化定制</View>
            <View className="text-(20 #666) mt-35">定制专属美学</View>
            <View className="text-(20 #666) mt-5">拒绝流水线式医美</View>
          </View>
        </View>
      </View>

      <View className="h-100"></View>
    </View>
  );
}
