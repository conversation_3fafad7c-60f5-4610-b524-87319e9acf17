import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { useShare } from "@/stores/useShare";
import { showToast } from "@/utils/tools";
import { Text, View } from "@tarojs/components";
import Taro, { useReachBottom } from "@tarojs/taro";
import { useMount } from "ahooks";
import dayjs from "dayjs";

const StateMap = [
  { id: 10, name: "待确认" },
  { id: 20, name: "待到店" },
  { id: 30, name: "已到店" },
  { id: 40, name: "已超时" },
  { id: 99, name: "已取消" },
];

export default () => {
  const [list, listAct] = useList({
    api: CusApi.getMyTreat,
  });

  useShare(() => {
    return {
      title: "我的项目",
      path: `/pages/mall2/projectList/index`,
    };
  });

  useMount(() => {
    listAct.reload();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  const fdate = (n: any) => {
    return n ? dayjs(n).format("YYYY-MM-DD") : "";
  };

  return (
    <View className="px-30">
      {list.list.map((item) => (
        <View
          className="pos-relative bg-#fff rounded-20 my-30 p-35"
          key={item.id}
        >
          <View className="flex">
            <View className="text-(40 #000) flex-1 min-w-0 line-clamp-0">
              {item.productName}
            </View>
          </View>
          <View className="pt-15">
            <View className="text-(27 #666) py-13">
              <Text>项目次数：总数{item.includeNum}次</Text>
              <Text className="text-(#ad7f6d) ml-15">剩余{item.lessNum}次</Text>
            </View>
            <View className="text-(27 #666) py-13">
              到期时间：
              <>
                {item.useEffectiveStyle == 0 && "永久"}
                {item.useEffectiveStyle == 20 && (
                  <>
                    <Text>{fdate(item.useEffectiveStartDate)}</Text>
                    <Text> 至 </Text>
                    <Text>{fdate(item.useEffectiveEndDate)}</Text>
                  </>
                )}
              </>
            </View>
          </View>
        </View>
      ))}

      <View className="py-30 text-(25 #aaa center)">
        {list.more ? "加载中..." : "没有更多数据了"}
      </View>
    </View>
  );
};
