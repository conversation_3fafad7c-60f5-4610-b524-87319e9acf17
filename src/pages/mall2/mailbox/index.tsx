import { Image, Textarea, View } from "@tarojs/components";
import { UnoIcon } from "@/components/UnoIcon";
import { useSetState } from "ahooks";
import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import Taro from "@tarojs/taro";
import { UploadImage } from "./Uploader";
import { CusNoticeBar } from "@/components/Custom/NoticeBar";

export default function Page() {
  const [state, setState] = useSetState({
    content: "",
    image: "",
    anon: false,
  });

  const onSubmit = async () => {
    const data = {
      content: state.content,
      image: state.image,
      anonymousTag: Number(state.anon),
    };
    if (!data.content) return showToast("请填写建议");
    Taro.showLoading({ title: "提交中..." });
    await CusApi.postFeed(data);
    Taro.hideLoading();

    const md = await Taro.showModal({
      title: "提交成功",
      content: "您的建议已发给系统管理员",
      // cancelText: "我的建议",
      showCancel: false,
    });

    if (md.confirm) {
      Taro.redirectTo({ url: `/pages/mall2/mailboxList/index` });
    }
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="z-50 pos-sticky top-0 py-25 px-45">
        <CusNoticeBar>
          非常感谢您的反馈，我们工作人员将尽快对您的建议进行响应。
        </CusNoticeBar>
      </View>

      <View className="px-30">
        <View className="bg-#fff rounded-10 px-25 py-50">
          <Textarea
            className="text-(30 #333) block w-full h-600"
            placeholder="请放心写下您的建议"
            value={state.content}
            onInput={(e) => setState({ content: e.detail.value })}
          />
          <View className="h-50"></View>
          <UploadImage max={3} onChange={(s) => setState({ image: s })} />
        </View>

        <Image
          className="block w-403 h-107 mx-a my-60"
          mode="aspectFit"
          src="https://oss.gaomei168.com/file-release/20241116/1307361119141900288.png"
        />

        <View className="h-160"></View>
        <View className="z-50 pos-fixed left-0 bottom-0 w-full box-border px-30 bg-#F7F4EF">
          <View className="flex items-center h-160">
            <View
              className="flex flex-col items-center justify-center px-30"
              onClick={() =>
                Taro.navigateTo({ url: `/pages/mall2/mailboxList/index` })
              }
            >
              <UnoIcon className="i-icon-park-outline:mail-edit size-46 text-#343434" />
              <View className="text-(20 #343434)">我的建议</View>
            </View>

            <View
              className="flex items-center pr-30 pl-30"
              onClick={() => setState({ anon: !state.anon })}
            >
              {state.anon ? (
                <UnoIcon className="i-icon-park-outline:check-one size-36 c-#AD7F6D" />
              ) : (
                <UnoIcon className="i-icon-park-outline:round size-36 c-#c9c9c9" />
              )}
              <View className="text-(26 #666) ml-15">匿名发布</View>
            </View>

            <View
              className="ml-a flex-1 min-w-0 h-92 bg-#8E4E36 rounded-20 text-(30/92 center #fff center)"
              onClick={onSubmit}
            >
              提交
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}
