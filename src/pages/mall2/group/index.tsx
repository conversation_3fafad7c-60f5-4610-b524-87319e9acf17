import { PopUp } from "@/components/Custom/PopUp2";
import { Tabs } from "@/components/Custom/Tabs";
import RichContent from "@/components/RichContent";
import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { useShare } from "@/stores/useShare";
import { Image, ScrollView, Text, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { useEffect } from "react";

export default function Page() {
  const [state, setState] = useSetState({
    rule: null as any,
    ruleOpen: false,

    tabs: [] as any[],
    tabKey: "" as any,
  });

  const [list, listAct] = useList({
    api: CusApi.getMarketProduct,
    params: { marketType: 30, categoryId: state.tabKey },
  });

  useMount(() => {
    getTypes();
    fetchRole();
  });

  useShare(() => {
    return {
      title: "限时团购",
      path: `/pages/mall2/group/index`,
    };
  });

  useEffect(() => {
    listAct.reload();
  }, [state.tabKey]);

  const fetchRole = async () => {
    const res = await CusApi.getMarketProductRule();
    setState({ rule: res });
  };

  const getTypes = async () => {
    const res = await CusApi.getMarketTypes({ marketType: 30 });
    let list: any[] = res?.list || [];
    list = list.map((n) => ({ id: n.id, name: n.name }));
    list.unshift({ id: "", name: "全部" });
    setState({ tabs: list });
    // console.log(res);
  };

  return (
    <View className="h-100vh flex flex-col bg-#F7F4EF">
      <View className="bg-#DED2C6 flex px-30 items-end py-30">
        <View className="flex-1">
          <View className="text-(60 #333)">限时团购</View>
          <View className="text-(24 #333) mt-10">参与拼单 立即成团</View>
        </View>
        <View className="flex text-(30 #333)">
          <View
            className="pl-30"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall2/groupLog/index` });
            }}
          >
            参团记录
          </View>
          <View className="pl-30" onClick={() => setState({ ruleOpen: true })}>
            规则
          </View>
        </View>
      </View>

      <Tabs
        tabs={state.tabs}
        value={state.tabKey}
        onChange={(e) => setState({ tabKey: e })}
        scroll={state.tabs.length > 4}
      />

      {/* <View className="flex h-100 pos-relative">
        {state.tabs.map((item) => (
          <View
            key={item.id}
            className={clsx(
              "flex-1 flex items-center justify-center transition-300 text-(28 #010101) fw-bold",
              state.tabKey == item.id && "!text-(#8E4E36)"
            )}
            onClick={() => setState({ tabKey: item.id })}
          >
            <View className="line-clamp-1">{item.name}</View>
          </View>
        ))}
        <View
          className="pos-absolute bottom-20 left-0 flex justify-center transition-300"
          style={{
            width: 100 / state.tabs.length + "%",
            transform: `translateX(${state.tabs.findIndex(
              (n) => n.id == state.tabKey
            )}00%)`,
          }}
        >
          <View className="w-26 h-6 bg-#AD7F6D"></View>
        </View>
      </View> */}

      <ScrollView
        scrollY
        className="flex-1 min-h-0"
        onScrollToLower={() => listAct.getNext()}
      >
        <View className="px-30">
          {list.list.map((item) => (
            <View
              key={item.id}
              className="flex py-30"
              onClick={() => {
                Taro.navigateTo({
                  url: `/pages/mall2/groupInfo/index?id=${item.id}`,
                });
              }}
            >
              <Image
                className="size-180 bg-#F6F3EE mr-30"
                src={item.coverImage}
              />
              <View className="flex-1 min-w-0 flex flex-col">
                <View className="text-(34  #333) line-clamp-1">
                  {item.name}
                </View>
                <View className="text-(24 #333) mt-15">
                  仅需
                  <Text className="text-#C48053">{item.groupNum}人</Text>
                  拼成
                </View>

                <View className="mt-a flex items-center">
                  <View className="flex-1 min-w-0 text-(20 #333)">
                    <Text>拼团价</Text>
                    <Text className="text-#000 fw-bold mx-5">&yen;</Text>
                    <Text className="text-(40 #000) fw-bold">
                      {item.salePrice}
                    </Text>
                    <Text className="ml-15 text-(#999) line-through">
                      原价{item.product?.salePrice}元
                    </Text>
                  </View>
                  <View className="text-(30 #fff) bg-#AE7F6D rounded-full px-25 py-8">
                    立即拼团
                  </View>
                </View>
              </View>
            </View>
          ))}

          <View className="py-50 text-(24 #999 center)">
            {list.more ? "加载中..." : "没有更多数据了"}
          </View>
        </View>
      </ScrollView>

      <PopUp
        open={state.ruleOpen}
        onClose={() => setState({ ruleOpen: false })}
      >
        <View
          className="px-30 bg-#fff bg-[linear-gradient(0deg,rgba(226,199,171,0.7),rgba(255,255,255,0.7))]"
          style={{ borderRadius: "40rpx 40rpx 0 0" }}
        >
          <View className="flex items-center py-40">
            <View className="flex-1 text-(34 #333) fw-bold">活动规则</View>
            <View onClick={() => setState({ ruleOpen: false })}>
              <UnoIcon className="i-icon-park-outline:close size-40 text-#333" />
            </View>
          </View>
          <View className="pb-30">
            <RichContent html={state.rule?.groupMallRuleContent} />
          </View>
        </View>
      </PopUp>
    </View>
  );
}
