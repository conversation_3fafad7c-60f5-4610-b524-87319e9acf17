import { UnoIcon } from "@/components/UnoIcon";
import { makeApi } from "@/utils/request";
import { Input, View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useSetState } from "ahooks";
import clsx from "clsx";
import { useMemo } from "react";

const getSearch = makeApi("get", `/api/platformSearchKeyword/query_list`);

export default function Page() {
  const [state, setState] = useSetState({
    keyword: "",
    search: [] as any[],
    list: [] as any[],
    listOpen: false,
    hots: [] as any[],
  });

  useDidShow(() => {
    getHots();

    const search = (Taro.getStorageSync("search") || []) as any[];
    setState({ search });
  });

  const onChange = async (keyword = "") => {
    if (!keyword) {
      setState({ keyword: "", list: [], listOpen: false });
      return;
    }

    const res = await getSearch({ source: 10, searchKey: keyword });
    const list = res?.list || [];
    setState({ list, listOpen: true });
  };

  const onClear = () => {
    setState({ keyword: "", listOpen: false });
  };

  const clearSearch = () => {
    const search = [];
    setState({ search, list: [], listOpen: false });
    Taro.setStorage({ key: "search", data: search });
  };

  const onSearch = async (keyword = "") => {
    if (!keyword) return;
    let search = [...state.search];
    search = search.filter((n) => n != keyword);
    search.unshift(keyword);
    search = search.slice(0, 10);
    setState({ search });
    Taro.setStorage({ key: "search", data: search });
    Taro.navigateTo({ url: `/pages/mall2/goodsList/index?keyword=${keyword}` });
  };

  const getHots = async () => {
    const res = await getSearch({ source: 10, recommendTag: 1 });
    const list = res?.list || [];
    setState({ hots: list });
  };

  const showList = useMemo(() => {
    return state.listOpen && !!state.keyword;
  }, [state.keyword, state.listOpen]);

  return (
    <View className="min-h-100vh bg-#fff">
      <View className="px-20 pos-sticky top-0 bg-#fff z-30">
        <View className="flex rounded-10 b-(1 solid #8E4E36) p-6">
          <View className="w-50 flex items-center justify-center">
            <UnoIcon className="i-icon-park-outline:search text-(30 #999)" />
          </View>
          <View className="flex-1">
            <Input
              className="size-full text-(26 #333)"
              placeholder="请输入关键字"
              confirmType="search"
              value={state.keyword}
              onInput={(e) => {
                const val = e.detail.value;
                setState({ keyword: val });
                onChange(val);
              }}
              onConfirm={(e) => onSearch(e.detail.value)}
            />
          </View>
          <View
            className={clsx(
              "w-60 flex items-center justify-center invisible",
              state.keyword ? "!visible" : ""
            )}
            onClick={onClear}
          >
            <UnoIcon className="i-icon-park-outline:close text-(30 #333)" />
          </View>
          <View
            className="h-60 rounded-10 bg-#8E4E36 flex items-center px-30 text-(26 #fff)"
            onClick={() => onSearch(state.keyword)}
          >
            搜索
          </View>
        </View>
      </View>

      {!showList && (
        <View className="px-20">
          {!!state.search?.length && (
            <>
              <View className="flex items-center pt-30 pb-20">
                <View className="text-(30 #333) fw-bold">搜索历史</View>
                <View onClick={clearSearch}>
                  <UnoIcon className="i-icon-park-outline:delete size-30 text-#999 ml-20" />
                </View>
              </View>

              <View className="overflow-hidden -mb-20">
                {state.search.map((item) => (
                  <View
                    key={item}
                    className="float-left mr-20 mb-20 max-w-1/1 rounded-full px-30 py-10 bg-#eee text-(24 #666)"
                    onClick={() => onSearch(item)}
                  >
                    <View className="line-clamp-1">{item}</View>
                  </View>
                ))}
              </View>
            </>
          )}

          <View className="flex items-center pt-30 pb-20">
            <View className="text-(30 #333) fw-bold">搜索热词</View>
            {/* <UnoIcon className="i-icon-park-outline:delete size-30 text-#999 ml-20" /> */}
          </View>

          <View className="overflow-hidden -mb-20">
            {state.hots.map((item) => (
              <View
                key={item.key}
                className="float-left mr-20 mb-20 max-w-1/1 rounded-full px-30 py-10 bg-#eee text-(24 #666)"
                onClick={() => onSearch(item.searchKey)}
              >
                <View className="line-clamp-1">{item.searchKey}</View>
              </View>
            ))}
          </View>
        </View>
      )}

      {showList && (
        <View className="">
          {state.list.map((item) => (
            <View
              key={item.id}
              className="py-25 flex items-center b-b-(1 solid #eee) px-30"
              onClick={() => onSearch(item.searchKey)}
            >
              <View className="w-50 flex items-center justify-center">
                <UnoIcon className="i-icon-park-outline:search text-(30 #999)" />
              </View>
              <View className="flex-1 min-w-0 text-(24 #333)">
                <View className="line-clamp-1">{item.searchKey}</View>
              </View>
              <UnoIcon className="i-icon-park-outline:arrow-left-up text-(30 #999)" />
            </View>
          ))}
        </View>
      )}
    </View>
  );
}
