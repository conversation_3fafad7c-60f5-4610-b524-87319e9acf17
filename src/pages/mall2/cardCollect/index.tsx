import CusApi from "@/services/CusApi";
import { img_arr, img_avatar, img_share } from "@/utils/images";
import { Button, Image, Text, View } from "@tarojs/components";
import { useMount, useSetState } from "ahooks";
import Style from "./index.module.scss";

import RichContent from "@/components/RichContent";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { CusPopup } from "@/components/Custom/PopUp";
import { CusModal } from "@/components/Custom/Modal";
import clsx from "clsx";
import { useShare } from "@/stores/useShare";
import { showToast, sleep } from "@/utils/tools";
import { CusNoticeBar } from "@/components/Custom/NoticeBar";

const img_jk_bg = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241010/1293935350996799488.png`;
const img_jk_s1 = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241011/1294304964658335744.png`;
const img_jk_s2 = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241011/1294304964704473088.png`;
const img_jk_s3 = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241011/1294304964670918656.png`;
const img_jk_back = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241018/1296776186784649216.png`;

export default function CardCollect() {
  const router = useRouter();
  const [state, setState] = useSetState({
    user: null as any,
    active: null as any,

    mine: [] as any[],
    list: [] as any[],
    exlist: [] as any[],
    canExchange: false,

    ranks: [] as any[],
    rule: null as any,

    resObj: null as any,

    rankOpen: false,
    ruleOpen: false,
    numOpen: false,
    resOpen: false,
    exOpen1: false,

    giftOpen: false,
    giftData: null as any,

    giftOpen2: false,
    giftData2: null as any,

    loading: false,
  });

  useShare(async (res: any) => {
    const sdata = res?.target?.dataset?.share;

    // 分享卡
    if (sdata) {
      Taro.showLoading({ title: "加载中..." });
      const res2 = await CusApi.doJKGive({ cardTypeId: sdata.cardTypeId });
      Taro.hideLoading();
      fetchMine();

      return {
        title: `好友为你分享1张[${sdata.cardName}]`,
        imageUrl: sdata.cardImage,
        path: `/pages/mall2/cardCollect/index?shareCard=${res2?.transferId}`,
      };
    }

    // 分享次数
    else {
      Taro.showLoading({ title: "加载中..." });
      await CusApi.doJKShare();
      Taro.hideLoading();
      fetchUser();

      return {
        title: "搞美医疗数字诊所",
        imageUrl: img_share,
        path: `/pages/tabs/mall/index`,
      };
    }
  });

  useMount(() => {
    getGift();
  });

  useDidShow(() => {
    init();
  });

  const init = () => {
    fetchList();
    fetchMine();
    fetchRank();
    fetchRule();
    fetchUser();
    fetchExList();
  };

  const fetchMine = async () => {
    const res = await CusApi.getJkStat();
    const list = res?.list || [];
    const canExchange = list.length && list.every((n) => n.totalNum > 0);
    setState({ mine: list, canExchange });
  };

  const fetchList = async () => {
    const res = await CusApi.getJKlist();
    setState({ list: res?.list || [] });
  };

  const fetchRank = async () => {
    const res = await CusApi.getJkRank();
    let ranks = res?.list || [];
    setState({ ranks });
  };

  const fetchRule = async () => {
    const res = await CusApi.getJkRule();
    setState({ rule: res });
  };

  const fetchUser = async () => {
    const res = await CusApi.getUserInfo();
    setState({ user: res });
  };

  const fetchExList = async () => {
    const res = await CusApi.getMarketProduct({
      marketType: "20",
      pageSize: 9999,
    });
    const list = res?.list || [];
    setState({ exlist: list });
  };

  const onCardTap = async (idx: number) => {
    if (state.loading) return;
    try {
      setState({ loading: true });
      const res = await CusApi.getJkStart();
      fetchUser();
      const list = state.list.sort(() => Math.random() - 0.5);
      const _idx = list.findIndex((n) => n.projectId == res?.projectId);
      if (_idx < 0 || _idx > 7) return showToast("idx错误,请重试");
      const tmp = list[idx];
      list[idx] = list[_idx];
      list[_idx] = tmp;
      setState({ resObj: res, list, active: idx });
      await sleep(400 + 1000 + 500);
      setState({ resOpen: true, loading: false });
    } finally {
      setState({ loading: false });
    }
  };

  const onResClose = () => {
    setState({ active: null, resOpen: false });
    fetchMine();
    fetchRank();
  };

  const openExchange = () => {
    if (!state.canExchange) {
      setState({ exOpen1: true });
    } else {
      // setState({ exOpen2: true });
      Taro.navigateTo({ url: `/pages/mall2/cardCollect2/index` });
    }
  };

  const openGiftGive = (item: any) => {
    setState({ giftData: item, giftOpen: true });
  };

  const getGift = async () => {
    const cid = router.params.shareCard;
    if (!cid) return;
    const res = await CusApi.doJKGet({ transferId: cid });
    fetchMine();
    setState({ giftData2: res, giftOpen2: true });
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="pl-65 pr-40 pt-30 pb-70">
        <View className="flex items-center">
          <View className="flex-1 text-(56 #8E4E36) fw-500">
            搞美「集卡送礼」
          </View>
          <View
            className="text-(20 #2F2F2F) b-(1 solid #333333) rounded-full py-8 px-20 flex items-center"
            onClick={() => setState({ ruleOpen: true })}
          >
            <View>游戏规则</View>
            <Image className="size-14 ml-10" mode="aspectFit" src={img_arr} />
          </View>
        </View>
        <View className="text-(24 #11100F) mt-15">
          集齐四张搞美卡即可兑换项目
        </View>
      </View>

      <View
        className="pos-relative w-727 h-905 mx-auto bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${img_jk_bg})`,
          backgroundSize: "100% 100%",
        }}
      >
        <View className="pos-absolute top-25 left-0 h-76 px-65 box-border w-full flex items-center">
          <View className="w-full">
            <CusNoticeBar>{state.rule?.collectAdvContent}</CusNoticeBar>
          </View>
        </View>

        <View className="flex justify-center pt-140">
          <View className="inline-grid cols-4" style={{ gap: "16rpx 6rpx" }}>
            {state.list.map((item, idx) => (
              <View
                key={item.id}
                className={clsx(
                  "pos-relative w-148 h-188 overflow-hidden",
                  state.active == idx
                    ? Style.active
                    : state.active == null
                    ? ""
                    : Style.activeDelay
                )}
                onClick={() => onCardTap(idx)}
              >
                <View
                  className={clsx(
                    Style.face2,
                    "box-border b-(6 solid #EBB88F) rounded-10 bg-#fff flex flex-col items-center justify-center"
                  )}
                  style={{ backfaceVisibility: "hidden" }}
                >
                  <Image
                    className="size-80"
                    mode="aspectFit"
                    src={item.projectImage}
                  />
                  <View className="text-(22 #11100F center) mt-7 line-clamp-1">
                    {item.projectName}
                  </View>
                </View>
                <Image
                  mode="scaleToFill"
                  className={clsx(Style.face1)}
                  src={img_jk_back}
                />
              </View>
            ))}
          </View>
        </View>

        <View className="pos-absolute left-0 bottom-210 w-full flex items-center justify-center">
          <View
            className="w-134 h-48 bg-#FFF5EE rounded-full text-(25/48 center #8E4E36) fw-bold"
            style={{ boxShadow: "5rpx 5rpx 5rpx rgba(183, 104, 74,.4)" }}
            onClick={() => setState({ rankOpen: true })}
          >
            排行榜
          </View>
          <View
            className="w-255 h-86 bg-#B7684A flex justify-center items-center rounded-full mx-30 "
            style={{
              boxShadow:
                "5rpx 5rpx 5rpx rgba(183,104,74,.4),-5rpx -5rpx 5rpx rgba(255,255,255,.4)",
            }}
            onClick={() => setState({ active: null })}
          >
            <View className="text-(30 #FFFCFA) mt-0">剩余</View>
            <Text className="text-(40 #FFFCFA) mx-5 fw-bold">
              {state.user?.lotteryNum || 0}
            </Text>
            <View className="text-(30 #FFFCFA) mt-0">次机会</View>
          </View>
          <View
            className="w-134 h-48 bg-#FFF5EE rounded-full text-(25/48 center #8E4E36) fw-bold"
            style={{ boxShadow: "5rpx 5rpx 5rpx rgba(183, 104, 74,.4)" }}
            onClick={() => setState({ numOpen: true })}
          >
            增加次数
          </View>
        </View>
      </View>

      <View className="py-30 px-35">
        <View className="flex items-center">
          <View className="flex-1">
            <View className="text-(35 #11100F)">我的集卡</View>
            <View className="text-(18 #955942) mt-5">
              暂未满足兑换条件，请继续加油！
            </View>
          </View>
          <View
            className="w-116 h-44 text-(22/44 #fff center) rounded-full bg-#B7684A"
            onClick={openExchange}
          >
            立即兑换
          </View>
        </View>

        <View className="flex justify-between py-40">
          {state.mine.map((item) => (
            <View className="pos-relative" key={item.id}>
              <View
                className="w-131 h-155 rounded-10 overflow-hidden"
                style={{
                  boxShadow: "0rpx 0rpx 5rpx rgba(0,0,0,.2)",
                  filter: item.totalNum < 1 ? "grayscale(1)" : "",
                }}
                onClick={() => (item.totalNum < 1 ? null : openGiftGive(item))}
              >
                <Image
                  className="block size-full"
                  mode="aspectFill"
                  src={item.cardImage}
                />
              </View>

              <View
                className="pos-absolute -top-16 right-10 size-38 rounded-full b-(2 solid #fff) text-(30 #fff) flex items-center justify-center box-border"
                style={{
                  background: `linear-gradient(-23deg, rgba(226, 90, 33, .63), rgba(226, 90, 33, 1))`,
                  boxShadow: "5rpx 5rpx 5rpx rgba(0,0,0,.2)",
                }}
              >
                {item.totalNum}
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* 排行榜 */}
      <CusPopup
        open={state.rankOpen}
        onClose={() => setState({ rankOpen: false })}
        title="排行榜"
      >
        <View className="px-60 max-h-60vh overflow-y-auto">
          {state.ranks.map((item, idx) => (
            <View
              className="flex items-center py-30 b-b-(1 solid #696969/20)"
              key={item.id}
            >
              {idx == 0 && (
                <View className="size-48 rounded-full text-(30/48 #fff center) bg-#FF9656 mr-18">
                  {idx + 1}
                </View>
              )}

              {idx == 1 && (
                <View className="size-48 rounded-full text-(30/48 #fff center) bg-#E6694F mr-18">
                  {idx + 1}
                </View>
              )}

              {idx == 2 && (
                <View className="size-48 rounded-full text-(30/48 #fff center) bg-#EF922C mr-18">
                  {idx + 1}
                </View>
              )}

              {idx > 2 && (
                <View className="size-48 rounded-full text-(30/48 #955942 center) mr-18">
                  {idx + 1}
                </View>
              )}

              <Image
                className="size-86 bg-#BFBFBF rounded-full"
                src={item.userPhoto || img_avatar}
              />
              <View className="flex-1 min-w-0 text-(27 #666) line-clamp-1 px-35">
                {item.userName}
              </View>
              <View className="text-(24 #955942)">
                集齐{item.completeNum}次
              </View>
            </View>
          ))}
        </View>
      </CusPopup>

      {/* 游戏规则 */}
      <CusPopup
        open={state.ruleOpen}
        onClose={() => setState({ ruleOpen: false })}
        title="游戏规则"
      >
        <View className="px-60 pt-10 pb-100 max-h-60vh overflow-y-auto">
          <RichContent html={state.rule?.collectRuleContent} />
        </View>
      </CusPopup>

      {/* 增加次数 */}
      <CusPopup
        open={state.numOpen}
        onClose={() => setState({ numOpen: false })}
        title="增加次数"
      >
        <View className="px-40 pb-50 max-h-60vh overflow-y-auto">
          <View
            className="!hidden flex items-center px-40 h-126 bg-#FFF7DA mb-20 rounded-16 bg-[linear-gradient(0deg,rgba(226,199,171,0.7),rgba(255,255,255,0.7))] opacity-80"
            style={{ boxShadow: "0rpx 5rpx 8rpx 0rpx rgba(0,0,0,0.2)" }}
          >
            <Image className="size-66 mr-28" mode="aspectFit" src={img_jk_s1} />
            <View className="flex-1 min-w-0">
              <View className="text-(26 #8C492A) fw-600">关注搞美公众号</View>
              <View className="text-(19 #C15C36) mt-5">+1次抽奖机会</View>
            </View>
            <View className="w-110 h-42 text-(24/42 center #fff) rounded-full bg-#E24E3F bg-[linear-gradient(-23deg,rgba(255,253,176,0.37),rgba(255,253,155,0))]">
              去关注
            </View>
          </View>

          <View
            className="flex items-center px-40 h-126 bg-#FFF7DA mb-20 rounded-16 bg-[linear-gradient(0deg,rgba(226,199,171,0.7),rgba(255,255,255,0.7))] opacity-80"
            style={{ boxShadow: "0rpx 5rpx 8rpx 0rpx rgba(0,0,0,0.2)" }}
          >
            <Image className="size-66 mr-28" mode="aspectFit" src={img_jk_s2} />
            <View className="flex-1 min-w-0">
              <View className="text-(26 #8C492A) fw-600">分享好友注册</View>
              <View className="text-(19 #C15C36) mt-5">+1次抽奖机会</View>
            </View>
            <View className="pos-relative w-110 h-42 text-(24/42 center #fff) rounded-full bg-#E24E3F bg-[linear-gradient(-23deg,rgba(255,253,176,0.37),rgba(255,253,155,0))]">
              去分享
              <Button
                openType="share"
                className="pos-absolute top-0 left-0 size-full opacity-0"
                onClick={() => setState({ numOpen: false })}
              ></Button>
            </View>
          </View>

          <View
            className="flex items-center px-40 h-126 bg-#FFF7DA mb-20 rounded-16 bg-[linear-gradient(0deg,rgba(226,199,171,0.7),rgba(255,255,255,0.7))] opacity-80"
            style={{ boxShadow: "0rpx 5rpx 8rpx 0rpx rgba(0,0,0,0.2)" }}
            onClick={() => {
              setState({ numOpen: false });
              Taro.navigateTo({ url: `/pages/mall2/mmall/index` });
            }}
          >
            <Image className="size-66 mr-28" mode="aspectFit" src={img_jk_s3} />
            <View className="flex-1 min-w-0">
              <View className="text-(26 #8C492A) fw-600">前往搞美商城兑换</View>
              <View className="text-(19 #C15C36) mt-5">兑换抽奖机会</View>
            </View>
            <View className="w-110 h-42 text-(24/42 center #fff) rounded-full bg-#E24E3F bg-[linear-gradient(-23deg,rgba(255,253,176,0.37),rgba(255,253,155,0))]">
              去兑换
            </View>
          </View>
        </View>
      </CusPopup>

      {/* 获奖弹窗 */}
      <CusModal open={state.resOpen} onClose={onResClose}>
        {state.resObj?.property == 30 && (
          <View className="flex flex-col items-center justify-center">
            <View className="text-(40 #fff) mb-50">恭喜获得</View>
            <View className="pos-relative w-462 h-548">
              <View
                className="pos-absolute -top-40 right-30 rounded-full size-86 bg-#E25A21 b-(1 solid #fff) text-(45/86 center #fff)"
                style={{
                  backgroundImage:
                    "linear-gradient(-23deg, rgba(255,253,176,0.37), rgba(255,253,155,0))",
                  boxShadow: "3rpx 3rpx 3rpx rgba(0,0,0,.2)",
                }}
              >
                *1
              </View>
              <Image
                className="block size-full"
                mode="aspectFit"
                src={state.resObj?.projectImage}
              />
            </View>
            <View className="flex items-center justify-center pt-60">
              <View
                className="pos-relative w-233 h-74 rounded-full text-(36/74 center #fff) bg-#B7684A"
                style={{
                  boxShadow: "0 0 5rpx rgba(255,255,255,.5)",
                  backgroundImage:
                    "linear-gradient(-23deg, rgba(255,253,176,0.37), rgba(255,253,155,0))",
                }}
                onClick={onResClose}
              >
                开心收下
              </View>
              {/* <View
                className="pos-relative w-175 h-74 rounded-full text-(36/74 center #fff) bg-#E0834B ml-25"
                style={{
                  boxShadow: "0 0 5rpx rgba(255,255,255,.5)",
                  backgroundImage:
                    "linear-gradient(-23deg, rgba(255,253,176,0.37), rgba(255,253,155,0))",
                }}
                onClick={onResClose}
              >
                <View className="pos-absolute right-0 -top-24 w-135 h-28 bg-#F6D5B2 rounded-full text-(16/28 #A13F16 center)">
                  <View
                    className="pos-absolute left-1/2 top-1/1"
                    style={{
                      borderTop: "6rpx solid #F6D5B2",
                      borderLeft: "6rpx solid transparent",
                      borderRight: "6rpx solid transparent",
                    }}
                  ></View>
                  点我赠送给朋友
                </View>
                赠送
              </View> */}
            </View>
          </View>
        )}

        {state.resObj?.property != 30 && state.resObj?.property != 0 && (
          <View className="flex flex-col items-center justify-center">
            <View className="text-(40 #fff) mb-50">恭喜获得</View>
            <View
              className="w-488 h-422 rounded-30 bg-#fff flex flex-col items-center justify-center"
              style={{
                backgroundImage:
                  "linear-gradient(0deg, rgba(226,199,171,0.7), rgba(255,255,255,0.7))",
                boxShadow: "0rpx 0rpx 10rpx 0rpx #191919",
              }}
            >
              <Image
                className="block size-200"
                mode="aspectFit"
                src={state.resObj?.projectImage}
              />
              <View className="text-(40 #8E4E36) mt-20 line-clamp-1 fw-bold">
                {state.resObj?.projectName}
              </View>
            </View>

            <View className="flex items-center justify-center pt-60">
              <View
                className="pos-relative w-350 h-74 rounded-full text-(36/74 center #fff) bg-#B7684A"
                style={{
                  boxShadow: "0 0 5rpx rgba(255,255,255,.5)",
                  backgroundImage:
                    "linear-gradient(-23deg, rgba(255,253,176,0.37), rgba(255,253,155,0))",
                }}
                onClick={onResClose}
              >
                开心收下
              </View>
            </View>
          </View>
        )}

        {state.resObj?.property == 0 && (
          <View className="flex flex-col items-center justify-center">
            <View
              className="w-488 h-320 rounded-30 bg-#fff flex flex-col items-center justify-center"
              style={{
                backgroundImage:
                  "linear-gradient(0deg, rgba(209, 209, 209,0.7), rgba(255,255,255,0.7))",
                boxShadow: "0rpx 0rpx 10rpx 0rpx #191919",
              }}
            >
              <Image
                className="block size-100"
                mode="aspectFit"
                src={state.resObj?.projectImage}
              />
              <View className="text-(40 #5E5E5E) mt-20 line-clamp-1 fw-bold">
                谢谢参与
              </View>
            </View>
          </View>
        )}
      </CusModal>

      {/* 兑换-未集齐 */}
      <CusModal open={state.exOpen1}>
        <View className="flex flex-col items-center justify-center">
          <View
            className="w-488 rounded-30 bg-#fff flex flex-col"
            style={{
              backgroundImage:
                "linear-gradient(0deg, rgba(209, 209, 209,0.7), rgba(255,255,255,0.7))",
              boxShadow: "0rpx 0rpx 10rpx 0rpx #191919",
            }}
          >
            <View className="text-(40 #5E5E5E center) fw-bold pt-40">
              您还暂未集齐哦
            </View>
            <View className="text-(20 #5E5E5E center) py-10">
              集齐四卡后可兑奖品一览：
            </View>
            <View className="p-35">
              <View className="flex justify-center items-center flex-wrap gap-20">
                {state.exlist.map((item) => (
                  <View key={item.id} className="w-110">
                    <Image
                      className="size-110 block mx-auto rounded-10 bg-#eee"
                      mode="aspectFit"
                      src={item.coverImage}
                    />
                    <View className="text-(20/32 #8E4E36 center) h-32 line-clamp-1">
                      {item.name}
                    </View>
                  </View>
                ))}
              </View>
            </View>
          </View>

          <View
            className="w-230 h-74 mt-30 mx-auto rounded-full bg-#B7684A text-(30/74 #fff center)"
            onClick={() => setState({ exOpen1: false })}
          >
            立即去集卡
          </View>
        </View>
      </CusModal>

      {/* 赠送卡 */}
      <CusModal
        open={state.giftOpen}
        onClose={() => setState({ giftOpen: false })}
      >
        <View className="flex flex-col items-center justify-center">
          <View className="text-(40 #fff) mb-50">
            {state.giftData?.cardName}
          </View>
          <View className="pos-relative w-462 h-548">
            <View
              className="pos-absolute -top-40 right-30 rounded-full size-86 bg-#E25A21 b-(1 solid #fff) text-(45/86 center #fff)"
              style={{
                backgroundImage:
                  "linear-gradient(-23deg, rgba(255,253,176,0.37), rgba(255,253,155,0))",
                boxShadow: "3rpx 3rpx 3rpx rgba(0,0,0,.2)",
              }}
            >
              *{state.giftData?.totalNum}
            </View>
            <Image
              className="block size-full"
              mode="aspectFit"
              src={state.giftData?.cardImage}
            />
          </View>
          <View className="flex items-center justify-center pt-60">
            <View
              className="pos-relative w-233 h-74 rounded-full text-(36/74 center #fff) bg-#B7684A"
              style={{
                boxShadow: "0 0 5rpx rgba(255,255,255,.5)",
                backgroundImage:
                  "linear-gradient(-23deg, rgba(255,253,176,0.37), rgba(255,253,155,0))",
              }}
              onClick={() => setState({ giftOpen: false })}
            >
              关闭
            </View>
            <View
              className="pos-relative w-175 h-74 rounded-full text-(36/74 center #fff) bg-#E0834B ml-25"
              style={{
                boxShadow: "0 0 5rpx rgba(255,255,255,.5)",
                backgroundImage:
                  "linear-gradient(-23deg, rgba(255,253,176,0.37), rgba(255,253,155,0))",
              }}
            >
              <Button
                openType="share"
                className="pos-absolute top-0 left-0 size-full opacity-0"
                onClick={() => setState({ giftOpen: false })}
                // @ts-ignore
                data-share={state.giftData}
              ></Button>
              <View className="pos-absolute right-0 -top-24 w-135 h-28 bg-#F6D5B2 rounded-full text-(16/28 #A13F16 center)">
                <View
                  className="pos-absolute left-1/2 top-1/1"
                  style={{
                    borderTop: "6rpx solid #F6D5B2",
                    borderLeft: "6rpx solid transparent",
                    borderRight: "6rpx solid transparent",
                  }}
                ></View>
                点我赠送给朋友
              </View>
              赠送
            </View>
          </View>
        </View>
      </CusModal>

      {/* 获取卡 */}
      <CusModal
        open={state.giftOpen2}
        onClose={() => setState({ giftOpen2: false })}
      >
        <View className="flex flex-col items-center justify-center">
          <View className="text-(40 #fff) mb-50">好友赠送了您1张</View>
          <View className="pos-relative w-462 h-548">
            <Image
              className="block size-full"
              mode="aspectFit"
              src={state.giftData2?.cardImage}
            />
          </View>
          <View className="flex items-center justify-center pt-60">
            <View
              className="pos-relative w-233 h-74 rounded-full text-(36/74 center #fff) bg-#B7684A"
              style={{
                boxShadow: "0 0 5rpx rgba(255,255,255,.5)",
                backgroundImage:
                  "linear-gradient(-23deg, rgba(255,253,176,0.37), rgba(255,253,155,0))",
              }}
              onClick={() => setState({ giftOpen2: false })}
            >
              开心收下
            </View>
          </View>
        </View>
      </CusModal>
    </View>
  );
}
