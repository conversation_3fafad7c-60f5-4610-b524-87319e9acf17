.animTxt {
  animation: anim-txt 18s linear infinite;
}

@keyframes anim-txt {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.face1,
.face2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  transition: all 0.4s ease-in-out;
}

.face2 {
  transform: rotateY(180deg);
}

.activeDelay,
.active {
  .face1 {
    transform: rotateY(180deg);
  }

  .face2 {
    transform: rotateY(0);
  }
}

.activeDelay {
  .face1,
  .face2 {
    transition-delay: 1s;
  }
}
