import CusApi from "@/services/CusApi";
import { Stepper } from "@taroify/core";
import { Image, Text, View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useSetState } from "ahooks";

export default function Cart() {
  const [state, setState] = useSetState({
    data: [] as any[],
  });

  useDidShow(() => {
    init();
  });

  const init = async () => {
    const res = await CusApi.getCart();
    setState({ data: res || [] });
  };

  const toggleAll = async (group, ck) => {
    const ids = group.productList.map((n) => n.id).join(",");
    await CusApi.putCart({
      ids,
      selectState: ck ? 0 : 1,
    });
    await init();
  };

  const toggleOne = async (item) => {
    await CusApi.putCart({
      ids: item.id,
      selectState: item.selectState == 1 ? 0 : 1,
    });
    await init();
  };

  const handleNumChange = async (num, item) => {
    // console.log(item);
    Taro.showLoading({ title: "加载中...", mask: true });
    await CusApi.putCart({
      ids: item.id,
      cartCount: num,
    });
    await init();
    Taro.hideLoading();
  };

  const handleDel = async (evt, group) => {
    evt.stopPropagation();
    const modal = await Taro.showModal({ title: "确定要删除吗？" });
    if (modal.confirm) {
      Taro.showLoading({ title: "删除中" });
      const ids = group.productList.map((n) => n.id).join(",");
      await CusApi.delCart({ ids });
      await init();
      Taro.hideLoading();
    }
  };

  const handleBuy = async (group) => {
    const ids = group.productList.map((n) => n.id).join(",");
    if (!ids) return Taro.showToast({ title: "请选择商品", icon: "none" });
    Taro.navigateTo({ url: `/pages/mall2/order/index?cart=${group.id}` });
  };

  return (
    <View className="p-30 pos-relative">
      {!state.data?.length && (
        <>
          <View className="pos-absolute top-0 left-0 w-100vw h-100vh flex flex-col items-center justify-center">
            <Image
              className="size-173"
              mode="aspectFit"
              src="https://vip.gaomei168.com/file-release/file/100/2024/0508/16/1237802922767392768_size_9088.png"
            ></Image>
            <View className="text-(35 #aaa) mt-40">购物车空空如也</View>
            <View
              className="w-210 h-80 bg-#ad7f6d rounded-full text-(36/80 #fff center) mt-50"
              onClick={() => {
                Taro.switchTab({ url: `/pages/tabs/cate/index` });
              }}
            >
              去购物
            </View>
          </View>
        </>
      )}

      {state.data.map((group) => {
        const list = group.productList || [];
        const allCk =
          list.filter((n) => n.selectState === 1).length == list.length;

        return (
          <View key={group.id} className="bg-#fff rounded-20 px-30 mb-30">
            <View
              className="flex items-center b-b-(1 solid #eee) h-100"
              onClick={() => toggleAll(group, allCk)}
            >
              <Image
                mode="aspectFit"
                className="size-28"
                src={
                  allCk
                    ? require("src/assets/ck-2.png")
                    : require("src/assets/ck-1.png")
                }
              ></Image>
              <View className="text-(31 #000) flex-1 min-w-0 px-20">
                {group.shopName}
              </View>
              <View
                className="flex h-full items-center"
                onClick={(e) => handleDel(e, group)}
              >
                <Image
                  mode="aspectFit"
                  className="size-28"
                  src="https://vip.gaomei168.com/file-release/file/100/2024/0506/10/1236987603027075072_size_630.png"
                ></Image>
                <View className="text-(24 #bababa) ml-7">清空</View>
              </View>
            </View>

            <View className="py-15">
              {group.productList?.map((item) => {
                return (
                  <View
                    className="flex items-center py-15"
                    key={item.id}
                    onClick={() => toggleOne(item)}
                  >
                    <Image
                      className="size-28 mr-20"
                      src={
                        item.selectState == 1
                          ? require("src/assets/ck-2.png")
                          : require("src/assets/ck-1.png")
                      }
                    ></Image>
                    <Image
                      mode="aspectFill"
                      className="size-150 rounded-10 bg-#f5f5f5 mr-40"
                      src={item.productCoverImage}
                    ></Image>
                    <View className="flex-1 h-150 min-w-0 flex flex-col justify-between box-border py-5">
                      <View className="text-(32 #000) line-clamp-1">
                        {item.productName}
                      </View>

                      <View className="text-(22 #ad7f6d)">
                        {item.productSpecificationName}
                      </View>

                      <View className="flex items-end">
                        <View className="text-(35 #000)">
                          <Text className="text-24">&yen;</Text>
                          {item.cartPrice}
                        </View>

                        <View
                          className="ml-auto"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Stepper
                            // shape="circular"
                            value={item.cartCount}
                            onChange={(e) => handleNumChange(e, item)}
                          >
                            <Stepper.Button />
                            <Stepper.Input disabled />
                            <Stepper.Button />
                          </Stepper>
                        </View>
                      </View>
                    </View>
                  </View>
                );
              })}
            </View>

            <View className="h-134 b-t-(1 solid #eee) flex items-center justify-end">
              <View className="text-(28 #000)">
                共计：&yen;<Text className="text-35">{group.totalMoney}</Text>
              </View>
              <View
                className="w-176 h-66 bg-#ad7f6d rounded-full text-(28/66 #fff center) ml-35"
                onClick={() => handleBuy(group)}
              >
                去付款
              </View>
            </View>
          </View>
        );
      })}
    </View>
  );
}
