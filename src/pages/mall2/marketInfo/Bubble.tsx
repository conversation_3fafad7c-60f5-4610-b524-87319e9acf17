import { makeApi } from "@/utils/request";
import { Image, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState, useUpdateEffect } from "ahooks";
import { useId, useMemo } from "react";
import { sleep } from "@/utils/tools";

const getList = makeApi("get", `/api/appMessage/group/query_list`);

export const Bubble = () => {
  const uid = "bub" + useId();
  const router = useRouter();
  const [state, setState] = useSetState({
    list: [] as any[],
    idx: 0,
    cls: "",
  });

  useMount(async () => {
    const res = await getList({ marketProductId: router.params.id });
    const list = res?.list || [];
    setState({ list });
  });

  const crt = useMemo(() => state.list[state.idx], [state.list, state.idx]);

  useUpdateEffect(() => {
    if (crt) loop();
  }, [crt]);

  const goIn = (ms) => {
    return new Promise((resolve) => {
      const page = Taro.Current.page;
      page?.animate?.(
        "#" + uid,
        [
          { opacity: 0, translateY: "30rpx", ease: "easeIn" },
          { opacity: 1, translateY: "0rpx", ease: "easeIn" },
        ],
        ms,
        resolve as any
      );
    });
  };

  const goOut = (ms) => {
    return new Promise((resolve) => {
      const page = Taro.Current.page;
      page?.animate?.(
        "#" + uid,
        [
          { opacity: 1, translateY: "0rpx", ease: "easeIn" },
          { opacity: 0, translateY: "-30rpx", ease: "easeIn" },
        ],
        ms,
        resolve as any
      );
    });
  };

  const loop = async () => {
    await sleep(1000 * 1);
    await goIn(500);
    await sleep(1000 * 10);
    await goOut(500);
    await sleep(1000 * 4);
    setState((p) => ({ idx: (p.idx + 1) % p.list.length }));
  };

  if (!crt) return;

  return (
    <View id={uid} className="pos-absolute top-80 left-30 opacity-0">
      <View className="flex items-center p-8 gap-10 bg-gray-8 rounded-10">
        <Image
          className="size-36 rounded-full overflow-hidden bg-#eee"
          mode="aspectFill"
          src={crt.image}
        ></Image>
        <View className="text-(24 #fff)">{crt.content}</View>
      </View>
    </View>
  );
};
