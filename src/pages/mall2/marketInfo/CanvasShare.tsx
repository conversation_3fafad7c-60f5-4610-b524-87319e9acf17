import CusApi from "@/services/CusApi";
import { Popup } from "@taroify/core";
import { Button, Canvas, Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useSetState } from "ahooks";
import avatar1 from "@/assets/avatar.png";
import { useEffect } from "react";

const breakTextWithEllipsis = (
  ctx: CanvasRenderingContext2D,
  text,
  x,
  y,
  mw,
  lh,
  mh = 2
) => {
  const words = text.split("");
  let ct = "";
  let cw = 0;
  let cl = 0;

  for (let t of words) {
    const cw2 = ctx.measureText(t).width;

    if (cw + cw2 > mw) {
      if (cl + 1 >= mh) {
        ct += "...";
        ctx.fillText(ct, x, y + cl * lh);
        return;
      }

      ctx.fillText(ct, x, y + cl * lh);
      ct = "";
      cw = 0;
      cl += 1;
    } else {
      ct += t;
      cw += cw2;
    }
  }

  ctx.fillText(ct, x, y + cl * lh);
};

const loadImg = (cvs, url) => {
  return new Promise<any>((onOk) => {
    const img = cvs.createImage();
    img.src = "";
    img.onload = () => onOk(img);
    img.onerror = () => onOk(img);
    img.src = url;

    if (!url) return onOk(img);
  });
};

const drawCover = async (img, ctx, cw, ch) => {
  // const img: any = await loadImg(cvs, url);

  const iw = img.width;
  const ih = img.height;
  const r1 = cw / ch;
  const r2 = iw / ih;
  let sx, sy, sw;

  if (r1 < r2) {
    sx = (iw - ih) / 2;
    sy = 0;
    sw = ih;
  } else {
    sx = 0;
    sy = (ih - iw) / 2;
    sw = iw;
  }

  ctx.drawImage(img, sx, sy, sw, sw, 0, 0 + 140, cw, cw);
  // img.src = "";
};

export function CanvasShare(props: { data: any; open: boolean; onOK: any }) {
  const [state, setState] = useSetState({ img: "" });

  const getData = async (cvs) => {
    const data = props.data || {};

    let cover = data.coverImage || "";
    const title = data.name || "";
    const price = `¥` + (data.salePrice || "??");

    const user = Taro.getStorageSync("user");
    let avatar = user?.photo || avatar1;
    const nick = user?.name || user?.mobile || "微信好友";
    const code = user?.serialNo || "";

    Taro.showLoading({ title: "生成中..." });
    let qr: any = null;
    const res = await CusApi.getQrcode({
      wxAppId: process.env.TARO_APP_ID,
      path: `/pages/mall2/marketInfo/index?id=${data.id}&promotionUserCode=${code}`,
    });

    const all = await Promise.all(
      [cover, avatar, res.base64Image || ""].map((n) => loadImg(cvs, n))
    );

    cover = all?.[0];
    avatar = all?.[1];
    qr = all?.[2];

    Taro.hideLoading();

    return {
      cover,
      title,
      price,
      avatar,
      nick,
      qr,
    };
  };

  useEffect(() => {
    if (props.open && !state.img) init();
  }, [state.img, props.open]);

  const init = () => {
    Taro.createSelectorQuery()
      .select("#cvs")
      .node()
      .exec(async (res) => {
        const cvs = res[0].node;
        const ctx: CanvasRenderingContext2D = cvs.getContext("2d");
        const dpr = Taro.getSystemInfoSync().pixelRatio;

        const data = await getData(cvs);

        const cw = 600;
        const ch = 985;

        cvs.width = cw * dpr;
        cvs.height = ch * dpr;
        ctx.scale(dpr, dpr);

        // clean
        ctx.fillStyle = "#fff";
        ctx.fillRect(0, 0, cw, ch);

        // header
        ctx.save();
        ctx.arc(20 + 50, 20 + 50, 50, 0, 2 * Math.PI);
        ctx.clip();
        // const avatar = await loadImg(cvs, data.avatar);
        ctx.drawImage(data.avatar, 20, 20, 100, 100);
        // fixed same img onload not trigger
        // data.avatar.src = "";
        ctx.restore();

        ctx.textBaseline = "hanging";
        ctx.font = "30px sans-serif";
        ctx.fillStyle = "#333";
        ctx.fillText(data.nick, 140, 35);
        ctx.font = "26px sans-serif";
        ctx.fillStyle = "#666";
        ctx.fillText("向您推荐 重庆搞美医疗美容诊所", 140, 75);

        // cover
        await drawCover(data.cover, ctx, cw, ch);

        // footer
        ctx.font = "30px sans-serif";
        ctx.fillStyle = "#333";
        // ctx.fillText(data.title, 20, 760);
        breakTextWithEllipsis(ctx, data.title, 20, 760, 350, 45);
        ctx.font = "40px sans-serif";
        ctx.fillStyle = "#ad7f6d";
        ctx.fillText(data.price, 20, 760 + 110);
        ctx.font = "26px sans-serif";
        ctx.fillStyle = "#666";
        ctx.fillText("(长按识别二维码)", 380, 930);
        // const qr = await loadImg(cvs, data.qr);
        ctx.drawImage(data.qr, 400, 760, 160, 160);
        // qr.src = "";

        Taro.canvasToTempFilePath({
          canvas: cvs,
          fileType: "jpg",
          quality: 0.8,
          success: (res) => {
            setState({ img: res.tempFilePath });
          },
        });
      });
  };

  return (
    <View>
      <Canvas
        id="cvs"
        type="2d"
        // className="w-600 h-985"
        // style={{ display: "none" }}
        className="size-0"
      ></Canvas>
      <Popup
        open={props.open}
        className="!bg-transparent"
        onClose={() => props.onOK?.()}
      >
        <View className="w-600 bg-#fff rounded-20 overflow-hidden">
          <Image
            className="w-600 h-985"
            src={state.img}
            mode="scaleToFill"
            showMenuByLongpress
          ></Image>
        </View>
        <View className="mt-20 flex">
          <View
            className="mr-20 pos-relative flex-1 bg-#ad7f6d rounded-full h-70 text-(28/70 #fff center)"
            onClick={() => {
              Taro.saveImageToPhotosAlbum({ filePath: state.img });
              props.onOK();
            }}
          >
            保存图片
          </View>
          <View className="pos-relative flex-1 bg-#ad7f6d rounded-full h-70 text-(28/70 #fff center)">
            <Button
              className="pos-absolute top-0 left-0 size-full opacity-0"
              openType="share"
              onClick={() => props.onOK()}
            ></Button>
            分享好友
          </View>
        </View>
      </Popup>
    </View>
  );
}
