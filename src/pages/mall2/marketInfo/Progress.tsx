import { View } from "@tarojs/components";
import { useMemo } from "react";

export const WeProgress = (props: { data: any }) => {
  const num = useMemo(() => {
    return Math.floor(
      ((props.data?.totalInventoryCount - props.data?.inventoryCount) /
        props.data?.totalInventoryCount) *
        100
    );
  }, [props.data]);

  return (
    <View className="flex items-center">
      <View className="w-180 h-16 rounded-full overflow-hidden bg-red/20">
        <View
          className="h-full w-0 rounded-full bg-red"
          style={{
            width: `${num}%`,
            backgroundSize: "30rpx 30rpx",
            backgroundImage: `linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 0px, transparent 50%, rgba(255, 255, 255, 0.15) 0px, rgba(255, 255, 255, 0.15) 75%, transparent 0px, transparent);`,
          }}
        />
      </View>
      <View className="text-(20 #666) ml-20">已抢{num}%</View>
    </View>
  );
};
