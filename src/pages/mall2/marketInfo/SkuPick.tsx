import { PopUp } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import { makeApi } from "@/utils/request";
import { Image, ScrollView, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useSetState } from "ahooks";
import clsx from "clsx";
import { useEffect } from "react";

const getQueueId = makeApi("post", `/api/mallMarketOrder/get_queue_id`);

const Tags = (props: {
  list?: any[];
  value?: any;
  onChange?: any;
  render?: any;
}) => {
  return (
    <View className="flex flex-wrap gap-30">
      {props.list?.map((item) => (
        <View
          className={clsx(
            "b-(2 solid #f8f7fc) max-w-7/10 p-10 text-(26 #333) bg-#f8f7fc rounded-10",
            props.value == item.id &&
              "uno-layer-z1-(bg-#8E4E36/4 b-#8E4E36 text-#8E4E36)"
          )}
          onClick={() => props.onChange(item)}
        >
          <View className="line-clamp-1">
            {props.render ? props.render(item) : item.name}
          </View>
        </View>
      ))}
    </View>
  );
};

export const SkuPick = (props: {
  open?: boolean;
  onClose?: any;
  data?: any;
  children?: any;
  exdata?: any;
}) => {
  const product = props.data?.product || {};
  const mtype = props.data?.marketType;

  const [state, setState] = useSetState({
    num: 1,

    modes: [] as any[],
    mode: null as any,

    skus: [] as any[],
    sku: null as any,
  });

  useEffect(() => {
    const data = props.data?.product || {};

    const modes: any[] = [];
    if (data.offlineConsumeTag) {
      modes.push({ id: 1, name: "到店服务" });
    }
    if (data.onlineDeliveryTag) {
      modes.push({ id: 2, name: "送货到家" });
    }
    const mode = modes[0]?.id;

    const tmp = props.data?.productSpecification;
    const skus = tmp ? [tmp] : [];
    const sku = skus[0];

    setState({ modes, mode, skus, sku });
  }, [props.data]);

  const goBuy = async () => {
    const data = {
      data: props.data,
      mode: state.mode,
      num: state.num,

      groupId: "",
      queueId: "",
    };

    if (mtype == 30) {
      data.groupId = props.exdata?.groupId;
    }

    if (mtype == 60) {
      const res = await getQueueId(
        { marketId: props.data?.id },
        { _loading: true }
      );
      data.queueId = res.queueId;
    }

    props.onClose?.();
    Taro.preload(data);
    Taro.navigateTo({ url: `/pages/mall2/marketPay/index` });
  };

  return (
    <PopUp open={props.open} onClose={props.onClose}>
      <View className="bg-#fff">
        <View className="flex gap-20 pos-relative px-30 pt-30">
          <Image
            className="size-150 bg-#f5f5f5"
            mode="aspectFill"
            src={product.coverImage}
          />
          <View className="flex-1 min-w-0 flex flex-col py-4">
            <View className="text-(34 #8D451F) fw-bold first-letter:(text-25 mr-5)">
              &yen;{props.data?.salePrice}
            </View>

            <View className="text-(28 #333) fw-bold line-clamp-1 mt-10">
              {state.sku?.name}
            </View>

            {!!mtype && ![30, 60].includes(mtype) && (
              <View className="inline-flex mt-a">
                <View
                  className="size-40 bg-#f5f4f9 flex items-center justify-center"
                  onClick={() =>
                    setState((p) => ({ num: Math.max(1, p.num - 1) }))
                  }
                >
                  <UnoIcon className="i-icon-park-outline:minus size-30 text-#515055" />
                </View>
                <View className="text-(24 #333) min-w-60 flex items-center justify-center">
                  {state.num}
                </View>
                <View
                  className="size-40 bg-#f5f4f9 flex items-center justify-center"
                  onClick={() => setState((p) => ({ num: p.num + 1 }))}
                >
                  <UnoIcon className="i-icon-park-outline:plus size-30 text-#515055" />
                </View>
              </View>
            )}
          </View>
          <View onClick={props.onClose}>
            <UnoIcon className="i-icon-park-outline:close size-34 text-#666" />
          </View>
        </View>

        <ScrollView scrollY className="max-h-50vh min-h-0">
          <View className="px-30 pb-80">
            {!state.modes.length && (
              <>
                <View className="text-(28 #333) fw-bold pt-40 pb-30">
                  服务类型
                </View>

                <Tags
                  list={state.modes}
                  value={state.mode}
                  onChange={(e) => setState({ mode: e.id })}
                />
              </>
            )}

            <View className="text-(28 #333) fw-bold pt-40 pb-30">产品规格</View>

            <Tags
              value={state.sku?.id}
              list={state.skus}
              onChange={(e) => setState({ sku: e })}
            />
          </View>
        </ScrollView>

        <View className="p-30 !pb-0">
          {mtype == 60 && (
            <>
              <View className="flex h-80 b-(1 solid #8E4E36) rounded-full overflow-hidden">
                <View
                  className="flex-1 flex items-center justify-center text-(30 #fff) bg-#8E4E36"
                  onClick={goBuy}
                >
                  立即抢购
                </View>
              </View>
            </>
          )}

          {mtype == 30 && (
            <>
              <View className="flex h-80 b-(1 solid #8E4E36) rounded-full overflow-hidden">
                <View
                  className="flex-1 flex items-center justify-center text-(30 #fff) bg-#8E4E36"
                  // onClick={toBuy}
                >
                  {props.exdata?.gid ? "加入团购" : "发起团购"}
                </View>
              </View>
            </>
          )}

          <View
            style={{
              height: `env(safe-area-inset-bottom)`,
              minHeight: "30rpx",
            }}
          />
        </View>
      </View>
    </PopUp>
  );
};
