import { View } from "@tarojs/components";
import { useMount, useSetState } from "ahooks";
import CusApi from "@/services/CusApi";
import { MarketBanner } from "./Banner";
import { MarketContent } from "./Content";
import { useRouter } from "@tarojs/taro";
import { Footer } from "./Footer";
import dayjs from "dayjs";
import { UnoIcon } from "@/components/UnoIcon";
import { WeCountDown } from "./CountDown";
import { WeProgress } from "./Progress";
import { usePageStat } from "@/stores/usePageStat";
import { useShare } from "@/stores/useShare";
import { CanvasShare } from "./CanvasShare";

const checkDate = (data) => {
  if (!data) return "Forever";

  if (!data.saleTimeLimit) return "Forever";

  const start = dayjs(data.saleStartDate);
  const end = dayjs(data.saleEndDate);
  const now = dayjs();

  if (now.isBefore(start)) {
    return "Upcoming";
  }

  if (now.isAfter(end)) {
    return "Ended";
  }

  return "Live";
};

export default function Page() {
  const router = useRouter();
  const [state, setState] = useSetState({
    id: router.params.id,
    data: null as any,
    shareOpen: false,
  });

  useMount(() => {
    fetchData();
  });

  usePageStat({
    id: state.data?.id,
    title: state.data?.name,
  });

  useShare(() => {
    return {
      title: state.data?.name,
      imageUrl: state.data?.coverImage,
      path: `/pages/mall2/marketInfo/index?id=${router.params.id}`,
    };
  });

  const fetchData = async () => {
    const id = state.id;
    const res = await CusApi.getMarketProductInfo({ id });

    const types = [
      { id: 10, name: "积分商城" },
      { id: 20, name: "集卡商城" },
      { id: 30, name: "团购商城" },
      { id: 40, name: "推广商城" },
      { id: 50, name: "新人专区" },
      { id: 60, name: "秒杀商城" },
    ];

    res._marketTypeName = types.find((n) => n.id == res.marketType)?.name || "";

    const props = [
      { id: 10, name: "商品" },
      { id: 20, name: "优惠券" },
      { id: 30, name: "抽奖次数" },
      { id: 40, name: "礼品" },
    ];

    res._propertyName = props.find((n) => n.id == res.property)?.name || "";

    res._dateStatus = checkDate(res);
    // res._dateStatus = "Upcoming";

    res._oldPrice =
      res.product?.oldPrice && res.product?.oldPrice > res.salePrice
        ? res.product?.oldPrice
        : 0;

    setState({ data: res });
  };

  return (
    <View>
      <MarketBanner data={state.data} />

      <View className="px-30">
        <View className="bg-#fff rounded-20 mt-20 p-30">
          <View className="flex items-center">
            <View className="flex items-center">
              <View className="text-(66 #000) fw-bold first-letter:(text-25 mr-5)">
                &yen;{state.data?.salePrice}
              </View>
              <View className="ml-10">
                <View className="text-(24 #333)">秒杀价</View>
                {!!state.data?._oldPrice && (
                  <View className="text-(24 #999) line-through">
                    原价{state.data?._oldPrice}
                  </View>
                )}
              </View>
            </View>

            {state.data?._dateStatus == "Live" && (
              <View className="ml-a flex flex-col items-end">
                <View className="text-(24 #444 right) flex items-center">
                  <UnoIcon className="i-icon-park-outline:alarm-clock size-30 text-#444 mr-10" />
                  限时秒杀
                </View>
                <View className="text-(22 #666 right) flex items-center mt-8">
                  <View>仅剩</View>
                  <WeCountDown className="ml-8" end={state.data?.saleEndDate} />
                </View>

                <View className="mt-15">
                  <WeProgress data={state.data} />
                </View>
              </View>
            )}

            {state.data?._dateStatus == "Upcoming" && (
              <View className="ml-a flex flex-col items-end">
                <View className="text-(24 #444 right) flex items-center">
                  <UnoIcon className="i-icon-park-outline:alarm-clock size-30 text-#444 mr-10" />
                  限时秒杀
                </View>
                <View className="text-(22 #666 right) flex items-center mt-8">
                  <View>距开始</View>
                  <WeCountDown
                    className="ml-8"
                    end={state.data?.saleStartDate}
                  />
                </View>
              </View>
            )}
          </View>

          <View className="text-(34 #000) pb-30 pt-20 leading-normal">
            {state.data?.name}
          </View>
        </View>
      </View>

      <MarketContent data={state.data} />

      <Footer data={state.data} />

      <>
        <View
          className="z-40 pos-fixed right-30 bottom-200 bg-#8E4E36 rounded-full flex flex-col items-center justify-center size-80"
          onClick={() => setState({ shareOpen: true })}
        >
          <UnoIcon className="i-icon-park-outline:share size-30 text-#fff" />
          <View className="text-(20 #fff) mt-4">分享</View>
        </View>
        {!!state.data?.id && (
          <CanvasShare
            data={state.data}
            open={state.shareOpen}
            onOK={() => setState({ shareOpen: false })}
          />
        )}
      </>
    </View>
  );
}
