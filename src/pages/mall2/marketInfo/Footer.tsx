import CusApi from "@/services/CusApi";
import { openConcat } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { SkuPick } from "./SkuPick";
import { useSetState } from "ahooks";

export const Footer = (props: { data?: any }) => {
  const data = props.data;
  const mtype = props.data?.marketType;
  const mprop = props.data?.property;

  const [state, setState] = useSetState({
    open: false,
  });

  const goBuyCoupon = async () => {
    const data = {
      data: props.data,
      num: 1,
      queueId: "",
    };

    const res = await CusApi.getMarketQueueId(
      { marketId: props.data?.id },
      { _loading: true }
    );

    data.queueId = res.queueId;

    Taro.preload(data);
    Taro.navigateTo({ url: `/pages/mall2/marketPay/index` });
  };

  return (
    <View>
      <View className="h-160"></View>
      <View className="pos-fixed left-0 bottom-0 z-50 h-160 w-full bg-#fff box-border flex items-center">
        <View
          className="flex flex-col items-center justify-center px-30"
          onClick={() => openConcat()}
        >
          <Image
            className="size-50"
            mode="scaleToFill"
            src="https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241019/1297147475462459392.png"
          />
          <View className="text-(22 #333) mt-5">立即咨询</View>
        </View>

        {mtype == 30 && (
          <>
            {mprop == 10 && (
              <>
                <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #8E4E36)">
                  <View className="flex-1 w-200 text-(30/80 #fff center) bg-#8E4E36">
                    团购按钮
                  </View>
                </View>
              </>
            )}
          </>
        )}

        {mtype == 60 && (
          <>
            {mprop == 10 && (
              <>
                {["Upcoming"].includes(data._dateStatus) && (
                  <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #B5B5B5)">
                    <View className="flex-1 w-200 text-(30/80 #fff center) bg-#B5B5B5">
                      敬请期待
                    </View>
                  </View>
                )}

                {["Ended"].includes(data._dateStatus) && (
                  <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #B5B5B5)">
                    <View className="flex-1 w-200 text-(30/80 #fff center) bg-#B5B5B5">
                      已结束
                    </View>
                  </View>
                )}

                {["Forever", "Live"].includes(data._dateStatus) && (
                  <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #B5B5B5)">
                    <View
                      className="flex-1 w-200 text-(30/80 #fff center) bg-#8E4E36"
                      onClick={() => setState({ open: true })}
                    >
                      立即抢购
                    </View>
                  </View>
                )}
              </>
            )}

            {mprop == 20 && (
              <>
                {["Upcoming"].includes(data._dateStatus) && (
                  <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #B5B5B5)">
                    <View className="flex-1 w-200 text-(30/80 #fff center) bg-#B5B5B5">
                      敬请期待
                    </View>
                  </View>
                )}

                {["Ended"].includes(data._dateStatus) && (
                  <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #B5B5B5)">
                    <View className="flex-1 w-200 text-(30/80 #fff center) bg-#B5B5B5">
                      已结束
                    </View>
                  </View>
                )}

                {["Forever", "Live"].includes(data._dateStatus) && (
                  <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #B5B5B5)">
                    <View
                      className="flex-1 w-200 text-(30/80 #fff center) bg-#8E4E36"
                      onClick={goBuyCoupon}
                    >
                      立即抢购
                    </View>
                  </View>
                )}
              </>
            )}
          </>
        )}
      </View>

      <SkuPick
        open={state.open}
        onClose={() => setState({ open: false })}
        data={props.data}
      />
    </View>
  );
};
