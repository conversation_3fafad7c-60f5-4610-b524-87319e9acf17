import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { Rate } from "@taroify/core";
import { Textarea, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useSetState } from "ahooks";
import { CusUpload } from "@/components/Custom/Upload";

export default () => {
  const router = useRouter();
  const [state, setState] = useSetState({
    imgs: "",
    score: 5,
    txt: "",
  });

  const handleComment = async () => {
    const data = {
      orderId: router.params.id,
      evaluationLevel: state.score,
      evaluationDesc: state.txt,
      evaluationImage: state.imgs,
    };

    Taro.showLoading({ title: "评价中...", mask: true });
    await CusApi.putOrderRate(data);
    await showToast("评价成功");
    Taro.navigateBack();
    Taro.eventCenter.trigger("order.reload");
  };

  return (
    <View className="p-25">
      <View className="bg-#fff rounded-20 px-20 py-30">
        <View className="flex py-20">
          <View className="text-(30 #000) w-160 fw-bold">评价星级</View>
          <View className="flex-1 min-w-0">
            <View className="pb-20">
              <Rate
                style={
                  {
                    "--rate-icon-size": "40rpx",
                    "--rate-icon-full-color": "#8E4E36",
                  } as any
                }
                value={state.score}
                onChange={(e) => setState({ score: e })}
              />
            </View>
          </View>
        </View>
        <View className="flex py-20">
          <View className="text-(30 #000) w-160 fw-bold">评价图片</View>
          <View className="flex-1 min-w-0">
            <CusUpload max={3} onChange={(e) => setState({ imgs: e })} />
          </View>
        </View>
        <View className="flex py-20">
          <View className="text-(30 #000) w-160 fw-bold">评价说明</View>
          <View className="flex-1 min-w-0">
            <Textarea
              className="text-(30 #333) block w-full h-200"
              placeholder="请输入评价说明"
              value={state.txt}
              onInput={(e) => setState({ txt: e.detail.value })}
            ></Textarea>
          </View>
        </View>
      </View>

      <View
        className="h-80 rounded-full bg-#ad7f6d text-(28/80 #fff center) my-100"
        onClick={handleComment}
      >
        提交评论
      </View>
    </View>
  );
};
