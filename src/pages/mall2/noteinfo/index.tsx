import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { str2arr } from "@/utils/tools";
import {
  Image,
  ScrollView,
  Swiper,
  SwiperItem,
  Video,
  View,
} from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { useRef } from "react";
import { NavBar } from "../noteroot/unit";
import { NoteComments } from "../noteroot/Comments";
import RichContent from "@/components/RichContent";
import { useShare } from "@/stores/useShare";
import { Related } from "./related";
import clsx from "clsx";
import { usePageStat } from "@/stores/usePageStat";
import { makeApi } from "@/utils/request";

const postStat = makeApi("post", `/api/appInformationArticleInteraction/add`);

export default function NoteInfo() {
  const router = useRouter();
  const comRef = useRef<any>(null);

  const [state, setState] = useSetState({
    id: router.params.id || "",
    data: null as any,
    idx: 0,
  });

  usePageStat({
    id: state.data?.id,
    title: state.data?.title,
  });

  useMount(() => {
    postStat({ source: 1, articleId: router.params.id, type: 10 });
  });

  useMount(() => {
    fetchInfo();
  });

  useShare(() => {
    postStat({ source: 1, articleId: router.params.id, type: 40 });
    return {
      title: state.data?.title,
      imageUrl: state.data?._cover,
      path: `/pages/mall2/noteinfo/index?id=${state.data?.id}`,
    };
  });

  const fetchInfo = async () => {
    const id = router.params.id;
    const res = await CusApi.getNoteInfo({ id });
    const imgs = str2arr(res?.images);
    const cover = res?.model == 1 ? imgs?.[0] : res?.videoCoverImage;
    res._imgs = imgs;
    res._cover = cover;

    setState({ data: res });
  };

  const toggleLike = async () => {
    if (!state.data?.id) return;
    const data = { source: 1, articleId: state.id, type: 20 };
    if (state.data?.likesTag) {
      await CusApi.delNoteAct(data);
    } else {
      await CusApi.addNoteAct(data);
    }
    fetchInfo();
  };

  return (
    <View className="h-100vh flex flex-col">
      <NavBar data={state.data} />

      <ScrollView
        scrollY
        className="flex-1 min-h-0"
        onScrollToLower={() => comRef.current.getNext()}
      >
        {state.data?.model == "1" && (
          <View className="pos-relative h-1000 bg-#fff">
            <Swiper
              className="size-full"
              indicatorDots={false}
              current={state.idx}
              onChange={(e) => setState({ idx: e.detail.current })}
            >
              {state.data?._imgs?.map((item, idx) => (
                <SwiperItem className="size-full" key={idx}>
                  <Image
                    className="size-full block"
                    mode="aspectFit"
                    src={item + "?x-oss-process=image/resize,w_1080,limit_0"}
                    onClick={() => {
                      Taro.previewImage({
                        urls: state.data?._imgs,
                        current: item,
                      });
                    }}
                  />
                </SwiperItem>
              ))}
            </Swiper>

            <View className="pos-absolute left-0 bottom-50 w-full flex items-center justify-center gap-6">
              {state.data?._imgs?.map((item, idx) => (
                <View
                  key={item.id}
                  className={clsx(
                    "size-10 bg-#000/30 rounded-full",
                    state.idx == idx && "uno-layer-z1-(bg-#AE7F6D)"
                  )}
                />
              ))}
            </View>
          </View>
        )}

        {state.data?.model == "2" && (
          <View className="h-400 bg-#aaa">
            <Video
              className="block size-full"
              src={state.data?.videoUrl ?? ""}
              poster={state.data?.videoCoverImage ?? ""}
              posterSize="cover"
              controls
              autoplay={true}
              muted={false}
            />
          </View>
        )}

        <View className="px-35 bg-#fff">
          <View
            className="py-20 text-(32/44 #282828) fw-bold"
            onClick={() => {
              const url: string = state.data?.jumpKey || "";
              if (url) {
                if (url.startsWith("/pages/")) {
                  Taro.navigateTo({ url });
                } else if (/^https?:\/\//.test(url)) {
                  const link = encodeURIComponent(url);
                  Taro.navigateTo({
                    url: `/pages/mall/webview/index?url=${link}`,
                  });
                }
              }
            }}
          >
            {state.data?.title}
          </View>

          {[20, 50].includes(state.data?.type) ? (
            <View className="pb-50 text-(30/50 #282828) ws-pre">
              <RichContent html={state.data?.content} />
            </View>
          ) : (
            <View className="pb-50 text-(30/50 #282828) ws-pre-line">
              {state.data?.content}
            </View>
          )}
        </View>

        <Related data={state.data?.productDataList} />

        <View className="px-35 bg-#fff">
          <NoteComments
            ref={comRef}
            aid={state.id}
            num={state.data?.commentsNum}
          />
        </View>
      </ScrollView>

      <View
        className="h-130 bg-#fff flex items-center box-border px-25"
        style={{ boxShadow: "0rpx 3rpx 7rpx 0rpx rgba(0,0,0,0.48)" }}
      >
        <View
          className="w-310 h-66 bg-#F5F5F5 rounded-full flex items-center justify-center"
          onClick={() => comRef.current?.openReply()}
        >
          <UnoIcon className="i-icon-park-outline:file-editing-one size-36 c-#666" />
          <View className="ml-15 text-(31 #666)">发布评论</View>
        </View>
        <View className="flex-1 min-w-0 flex px-50">
          <View
            className="flex-1 flex items-center justify-center"
            onClick={() => comRef.current?.openReply()}
          >
            <UnoIcon className="i-icon-park-outline:message-one size-40 c-#333" />
            <View className="text-(33 #5F5F5F) ml-10">
              {state.data?.commentsNum || 0}
            </View>
          </View>
          <View
            className="flex-1 flex items-center justify-center"
            onClick={() => toggleLike()}
          >
            {state.data?.likesTag ? (
              <UnoIcon className="i-icon-park-solid:like size-40 c-#8E4E36" />
            ) : (
              <UnoIcon className="i-icon-park-outline:like size-40 c-#333" />
            )}
            <View className="text-(33 #5F5F5F) ml-10">
              {state.data?.likesNum || 0}
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}
