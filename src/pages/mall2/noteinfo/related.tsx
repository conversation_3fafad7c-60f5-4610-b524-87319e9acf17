import { Image, ScrollView, View } from "@tarojs/components";
import Taro from "@tarojs/taro";

export const Related = (props: { data: any }) => {
  if (!props.data?.length) return null;

  return (
    <View className="px-30 bg-#fff b-t-(1 solid #f5f5f5) pb-30">
      <View className="flex items-center py-30">
        <View className="flex-1 text-(30 #333) fw-bold">即刻闪耀能量套装</View>
      </View>
      <View className="h-330 overflow-hidden">
        <ScrollView className="h-400" scrollX>
          <View className="ws-nowrap">
            {props.data?.map((item) => (
              <View
                className="w-200 h-330 inline-block ws-normal mr-20"
                key={item.id}
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/mall2/info/index?id=${item.id}`,
                  });
                }}
              >
                <Image
                  className="size-200 bg-#f5f5f5 rounded-10"
                  mode="aspectFill"
                  src={item.coverImage}
                />
                <View className="h-72 text-(26/36 #333)">
                  <View className="line-clamp-2">{item.name}</View>
                </View>
                <View className="first-letter:text-20 text-(32 #8E4E36) fw-bold mt-5">
                  &yen;{item.salePrice}
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};
