import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { Image, ScrollView, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";
import { useEffect } from "react";

const darr1 =
  "https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241014/1295446024583385088.png";
const darr2 =
  "https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241014/1295446024730185728.png";
const darr3 =
  "https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241014/1295446366171697152.png";

export default function GoodsList() {
  const router = useRouter();
  const [state, setState] = useSetState({
    property: 2,
    keyword: decodeURIComponent(router.params.keyword || ""),
    sort: "1",

    loading: false,
  });

  useMount(() => {
    if (state.keyword) {
      Taro.setNavigationBarTitle({ title: state.keyword });
    }
  });

  useEffect(() => {
    listAct.reload();
  }, [state.keyword, state.sort]);

  const [list, listAct] = useList({
    api: CusApi.getGoods,
    params: {
      property: 2,
      name: state.keyword,
      sortType: state.sort,
    },
  });

  const toggleSort = (...n) => {
    const s = state.sort;
    const i = n.findIndex((n) => n == s);
    const j = (i + 1) % n.length;
    setState({ sort: n[j] });
  };

  return (
    <View className="h-100vh bg-#f5f5f5 flex flex-col">
      <View className="h-90 bg-#fff grid cols-4">
        <View
          className={clsx(
            "text-(28 #010101) flex items-center justify-center",
            state.sort == "1" && "text-#8E4E36"
          )}
          onClick={() => toggleSort("1")}
        >
          综合
        </View>
        <View
          className={clsx(
            "text-(28 #010101) flex items-center justify-center",
            state.sort == "2" && "text-#8E4E36"
          )}
          onClick={() => toggleSort("2")}
        >
          最新
        </View>
        <View
          className={clsx(
            "text-(28 #010101) flex items-center justify-center",
            state.sort == "5" && "text-#8E4E36"
          )}
          onClick={() => toggleSort("5")}
        >
          销量
        </View>
        <View
          className={clsx(
            "text-(28 #010101) flex items-center justify-center",
            (state.sort == "3" || state.sort == "4") && "text-#8E4E36"
          )}
          onClick={() => toggleSort("3", "4")}
        >
          价格
          {state.sort != "3" && state.sort != "4" && (
            <Image className="size-20 ml-5" mode="aspectFit" src={darr1} />
          )}
          {state.sort == "4" && (
            <Image className="size-20 ml-5" mode="aspectFit" src={darr3} />
          )}
          {state.sort == "3" && (
            <Image className="size-20 ml-5" mode="aspectFit" src={darr2} />
          )}
        </View>
      </View>
      <ScrollView
        className="flex-1 min-h-0"
        scrollY
        onScrollToLower={() => listAct.getNext()}
        refresherEnabled
        refresherTriggered={state.loading}
        refresherBackground="#f5f5f5"
        onRefresherRefresh={async () => {
          setState({ loading: true });
          await listAct.reload().finally(() => setState({ loading: false }));
        }}
      >
        <View className="grid cols-2 gap-35 px-35 pt-35 bg-#fff">
          {list.list.map((item) => (
            <View
              className="rounded-25 overflow-hidden"
              key={item.id}
              onClick={() => {
                Taro.navigateTo({
                  url: `/pages/mall2/info/index?id=${item.id}`,
                });
              }}
            >
              <View className="pt-1/1 pos-relative">
                <Image
                  className="pos-absolute top-0 left-0 size-full bg-#ddd"
                  mode="aspectFill"
                  src={item.coverImage}
                />
              </View>
              <View className="px-10 pt-20 pb-20">
                <View className="h-40 text-(28/40 #11100F) line-clamp-1 fw-bold">
                  {item.name}
                </View>
                <View className="flex pt-20 items-center">
                  <View className="text-(20 #666) b-(1 solid #999) px-10 py-5">
                    我来点开看看
                  </View>
                  <View className="ml-auto text-(24 #ED9521) fw-bold [&:first-letter]:(text-(20 #11100F) mr-5)">
                    &yen;{item.salePrice}
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
        <View className="py-30 text-(24 #999 center)">
          {list.empty ? "暂无数据" : list.more ? "加载中" : "没有更多数据了"}
        </View>
      </ScrollView>
    </View>
  );
}
