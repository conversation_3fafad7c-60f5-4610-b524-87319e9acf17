import RichContent from "@/components/RichContent";
import CusApi from "@/services/CusApi";
import { View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useRequest } from "ahooks";

export default () => {
  const router = useRouter();
  const res = useRequest(async () => {
    const res = await CusApi.getDoctorInfo({ id: router.params.id });
    return res;
  });
  const data: any = res.data || {};

  // const data = useMemo(() => {
  //   return Taro.getCurrentInstance().preloadData || {};
  // }, []);

  return (
    <View className="p-20">
      <View className="bg-#fff rounded-20 p-20">
        {data.personalInformation ? (
          <RichContent html={data.personalInformation}></RichContent>
        ) : (
          <View className="py-30 text-(24 #aaa center)">暂无内容</View>
        )}
      </View>

      <>
        <View className="h-150"></View>
        <View className="z-999 pos-fixed left-0 bottom-0 w-full h-150 box-border bg-#fff flex items-center justify-center">
          <View
            className="w-666 h-80 bg-#ad7f6d rounded-full text-(29/80 #fff center)"
            onClick={() =>
              Taro.navigateTo({
                url: `/pages/mall2/due/index?tab=1&shopId=${router.params.shopId}&docId=${data.id}`,
              })
            }
          >
            立即预约
          </View>
        </View>
      </>
    </View>
  );
};
