import CusApi from "@/services/CusApi";
import { fdate, openConcat, showToast } from "@/utils/tools";
import { Button, Image, Text, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useCountDown, useMount, useSetState } from "ahooks";
import * as oact from "src/utils/orderAct";
import { useEffect, useRef } from "react";
import { img_share } from "@/utils/images";
import { UnoIcon } from "@/components/UnoIcon";
import { QrCode } from "@/components/Qrcode";
import { useShare } from "@/stores/useShare";

const useCd = (target) => {
  const [cd, fd] = useCountDown({ targetDate: target });
  const pad = (n) => (n < 10 ? "0" + n : n + "");

  return {
    end: cd <= 0,
    str: [pad(fd.hours), pad(fd.minutes), pad(fd.seconds)].join(":"),
  };
};

const copy = (text: string, msg = "复制成功") => {
  Taro.setClipboardData({
    data: text,
    success: () => showToast(msg),
  });
};

export default () => {
  const timer = useRef<any>(null);
  const router = useRouter();
  const [state, setState] = useSetState({
    data: {} as any,
  });

  const cd = useCd(state.data._payLast ? state.data._payTime : 0);

  useShare((evt: any) => {
    // console.log(evt);
    const data = evt?.target?.dataset?.share;
    const name = data?.groupData?.productName;
    const cover = data?.groupData?.productCoverImage || img_share;
    const id = data?.groupData?.marketProductId;
    const gid = data?.groupData?.id;

    return {
      title: `[拼团] ${name}`,
      imageUrl: cover,
      path: `/pages/mall2/groupInfo/index?id=${id}&gid=${gid}`,
    };
  });

  useMount(() => {
    fetchData();
  });

  useEffect(() => {
    if (state.data.verifyTag == 1) {
      timer.current = setInterval(fetchState, 1000 * 5);
    }

    return () => {
      clearInterval(timer.current);
    };
  }, [state.data.verifyTag]);

  useEffect(() => {
    Taro.eventCenter.on("order.reload", fetchData);
    return () => {
      Taro.eventCenter.off("order.reload");
    };
  }, []);

  const fetchData = async () => {
    const id = router.params.id;
    const res = await CusApi.getOrderInfo({ id });
    const data = oact.patchOrder(res);

    setState({ data });
  };

  const fetchState = async () => {
    const id = router.params.id;
    const res = await CusApi.getOrderInfo({ id });
    if (res?.verifyTag == 2) {
      clearInterval(timer.current);
      // fetchData();
      Taro.eventCenter.trigger("order.reload");
      Taro.showModal({
        title: "核销成功",
        showCancel: false,
        confirmText: "我知道了",
      });
    }
  };

  return (
    <View className="p-20">
      <View className="px-30 pb-40 pt-20">
        <View className="flex items-center mb-15">
          {state.data.orderState == 40 ? (
            <UnoIcon className="i-icon-park-outline:check-one mr-10 size-46 text-#333"></UnoIcon>
          ) : (
            <UnoIcon className="i-icon-park-outline:chart-proportion mr-10 size-46 text-#333"></UnoIcon>
          )}
          <View className="text-(40 #333) font-bold">
            {state.data._stateName}
          </View>
        </View>

        {state.data._payLast && (
          <View className="text-(24 #666) fw-normal">
            剩余付款时间: {cd.str}
          </View>
        )}

        {state.data._verifyLast && (
          <View className="text-(24 #999)">
            请在
            {fdate(state.data._verifyTime, "YYYY/MM/DD")}
            (含)前到店消费
          </View>
        )}
      </View>

      {state.data.model == 2 && (
        <View className="flex items-center bg-#fff rounded-20 px-40 py-25 mb-20">
          <Image
            className="size-32 mr-15"
            mode="aspectFit"
            src="https://vip.gaomei168.com/file-release/file/100/2024/0507/11/1237365086738030593_size_1603.png"
          ></Image>
          <View className="flex-1 min-w-0">
            <View className="text-(31 #000)">{state.data.contactAddress}</View>
            <View className="text-(24 #aaa) mt-10">
              {state.data.contactName} {state.data.contactMobile}
            </View>
          </View>
        </View>
      )}

      {state.data._hx && (
        <View className="pos-relative p-40 min-h-120 items-center rounded-20 mb-20 bg-#fff text-(36 #000)">
          <View className="pos-relative size-200 bg-#eee mx-auto">
            <QrCode key={state.data._hxCode} code={state.data._hxCode}></QrCode>
          </View>
          <View className="min-w-0 text-center pt-30">
            <View
              className="text-(30 #333) flex items-center justify-center"
              onClick={() => copy(state.data._hxCode)}
            >
              核销码：
              <Text className="text-#333 font-bold">
                {state.data._hxCode.replace(/(.{4})/g, "$1 ")}
              </Text>
              <UnoIcon className="i-icon-park-outline:copy ml-10 text-#999"></UnoIcon>
            </View>
          </View>
          <View
            className="pos-absolute top-1/2 right-20 px-10 py-8 text-(20/30 #fff) bg-#B7684A rounded-10"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall2/due/index` });
            }}
          >
            立即预约
          </View>
        </View>
      )}

      <View className="bg-#fff rounded-20 px-25">
        <View className="flex items-center h-90 b-b-(1 solid #eee) px-20">
          <Image
            className="size-32 mr-15"
            mode="aspectFit"
            src="https://vip.gaomei168.com/file-release/file/100/2024/0507/11/1237365086738030592_size_2076.png"
          ></Image>
          <View className="flex-1 min-w-0 line-clamp-1 text-(28 #000)">
            {state.data.shopName}
          </View>
        </View>
        <View className="px-20 py-10">
          {state.data.mallOrderItemList?.map((item) => (
            <View className="flex py-10" key={item.id}>
              <Image
                className="size-140 rounded-10 overflow-hidden mr-25 bg-#f5f5f5"
                mode="aspectFill"
                src={item.productCoverImage}
              ></Image>
              <View className="flex-1 min-w-0 min-h-140 flex flex-col justify-between">
                <View className="text-(28 #000)">{item.productName}</View>
                {[1, 2, 12].includes(item.productModel) && (
                  <View className="text-(22 #ad7f6d) mt-10">
                    门诊费: &yen;{item.outpatientFee} &nbsp;&nbsp;&nbsp;
                    {!!item.serviceUserId && (
                      <>
                        {item.serviceUserName}{" "}
                        {!!item.serviceFee && (
                          <>: &yen;{item.serviceFee || "--"}</>
                        )}
                      </>
                    )}
                  </View>
                )}
                <View className="text-(28 #000) flex mt-10">
                  <View>&yen;{item.productPrice}</View>
                  <View className="ml-auto text-23">x{item.totalCount}</View>
                </View>
              </View>
            </View>
          ))}
        </View>
        <View className="b-t-(1 solid #eee) py-20 px-20">
          <View className="flex py-10">
            <View className="flex-1 text-(24 #000)">商品总额</View>
            <View className="text-(24 #000)">&yen;{state.data.totalMoney}</View>
          </View>
          <View className="flex py-10">
            <View className="flex-1 text-(24 #000)">会员折扣</View>
            <View className="text-(24 #000)">
              -&yen;{state.data.discountPreferentialMoney}
            </View>
          </View>
          <View className="flex py-10">
            <View className="flex-1 text-(24 #000)">使用优惠券</View>
            <View className="text-(24 #000)">
              -&yen;{state.data.couponConvertMoney}
            </View>
          </View>
          <View className="flex py-10">
            <View className="flex-1 text-(24 #000)">使用M币</View>
            <View className="text-(24 #000)">
              -&yen;{state.data.integralConvertMoney}
            </View>
          </View>
          <View className="text-(28 #000 right) py-10">
            共计：&yen;
            <Text className="text-35 fw-bold">{state.data.payMoney}</Text>
          </View>
        </View>
      </View>

      <View className="bg-#fff rounded-20 mt-20 px-30 py-20">
        <View
          className="text-(24 #000) py-15"
          onClick={() => copy(state.data.serialNo)}
        >
          订单号：{state.data.serialNo}
          <UnoIcon className="i-icon-park-outline:copy ml-10 text-(#999 24) inline-block"></UnoIcon>
        </View>
        <View className="text-(24 #000) py-15">
          下单时间：
          {fdate(state.data.createDate, "YYYY-MM-DD HH:mm:ss") || "--"}
        </View>
        <View className="text-(24 #000) py-15">
          下单备注：
          {state.data.orderRemark || "--"}
        </View>
      </View>

      <>
        <View className="h-150"></View>
        <View className="pos-fixed left-0 bottom-0 w-full h-150 bg-#fff box-border px-20 flex items-center">
          <View
            className="flex-1 flex items-center"
            onClick={() => openConcat()}
          >
            <View className="flex flex-col justify-center items-center px-20">
              <UnoIcon className="i-icon-park-outline:message-unread size-40 text-#666"></UnoIcon>
              <View className="text-(20 #666 center) mt-6">客服</View>
            </View>
          </View>

          {state.data._toMall && (
            <View
              className="w-178 h-68 b-(1 solid #ad7f6d) rounded-full text-(28/68 #ad7f6d center) ml-25"
              onClick={() => {
                Taro.switchTab({ url: `/pages/tabs/cate/index` });
              }}
            >
              去使用
            </View>
          )}

          {state.data._comment && (
            <View
              className="w-178 h-68 b-(1 solid #ad7f6d) rounded-full text-(28/68 #ad7f6d center) ml-25"
              onClick={() => oact.handleComment(state.data)}
            >
              去评论
            </View>
          )}

          {state.data._refundCancel && (
            <View
              className="w-178 h-68 b-(1 solid #ad7f6d) rounded-full text-(28/68 #ad7f6d center) ml-25"
              onClick={() => oact.handleRefundCancel(state.data, fetchData)}
            >
              取消退款
            </View>
          )}

          {state.data._refund && (
            <View
              className="w-178 h-68 b-(1 solid #ad7f6d) rounded-full text-(28/68 #ad7f6d center) ml-25"
              onClick={() => oact.handleRefund(state.data)}
            >
              申请退款
            </View>
          )}
          {state.data._confirm && (
            <View
              className="w-178 h-68 b-(1 solid #ad7f6d) rounded-full text-(28/68 #ad7f6d center) ml-25 !text-#fff !bg-#ad7f6d"
              onClick={() => oact.handleConfirm(state.data, fetchData)}
            >
              确认收货
            </View>
          )}

          {state.data._close && (
            <View
              className="w-178 h-68 b-(1 solid #ad7f6d) rounded-full text-(28/68 #ad7f6d center) ml-25"
              onClick={() => oact.handleClose(state.data, fetchData)}
            >
              取消订单
            </View>
          )}

          {state.data._pay && (
            <View
              className="w-178 h-68 b-(1 solid #ad7f6d) rounded-full text-(28/68 #ad7f6d center) ml-25 !text-#fff !bg-#ad7f6d"
              onClick={() => oact.handlePay(state.data, fetchData)}
            >
              去付款
            </View>
          )}

          {state.data._shareGroup && (
            <View className="pos-relative w-178 h-68 b-(1 solid #ad7f6d) rounded-full text-(28/68 #ad7f6d center) ml-25 !text-#fff !bg-#ad7f6d">
              <Button
                // onClick={() => setState({ share: item })}
                openType="share"
                className="pos-absolute top-0 left-0 size-full opacity-0"
                data-share={state.data}
              />
              邀请拼团
            </View>
          )}
        </View>
      </>
    </View>
  );
};
