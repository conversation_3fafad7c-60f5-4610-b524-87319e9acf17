import { CusModal } from "@/components/Custom/Modal";
import { CusPopup } from "@/components/Custom/PopUp";
import RichContent from "@/components/RichContent";
import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import {
  img_arr,
  img_sign_check,
  img_sign_coin1,
  img_sign_coin2,
  img_sign_modal2,
} from "@/utils/images";
import { Image, Text, View } from "@tarojs/components";
import Taro, { useReachBottom } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";

const sorts = [
  { name: "综合排序", key: "1" },
  { name: "积分升序", key: "2" },
  { name: "积分倒序", key: "3" },
];

export default function Sign() {
  const [state, setState] = useSetState({
    rule: null as any,
    ruleOpen: false,

    sign: null as any,
    signRes: null as any,
    signOpen: false,

    // ...
    sort: "1",
    sortShow: false,
  });

  const [list, listAct] = useList({
    api: CusApi.getMarketProduct,
    params: { marketType: 10 },
  });

  useMount(() => {
    listAct.reload();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  useMount(() => {
    fetchSignList();
    fetchRule();
  });

  const fetchRule = async () => {
    const res = await CusApi.getSignRule();
    setState({ rule: res });
  };

  const fetchSignList = async () => {
    const res = await CusApi.getSignList();
    setState({ sign: res });
  };

  const onSign = async () => {
    if (!state.sign.signTag) return;

    Taro.showLoading({ title: "签到中...", mask: true });
    const res = await CusApi.getSignStart();
    Taro.hideLoading();
    setState({ signRes: res, signOpen: true });
    fetchSignList();
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="pl-65 pr-40 pt-30 pb-70">
        <View className="flex items-center">
          <View className="flex-1 text-(56 #8E4E36) fw-500">
            搞美「每日签到」
          </View>
          <View
            className="text-(20 #2F2F2F) b-(1 solid #333333) rounded-full py-8 px-20 flex items-center"
            onClick={() => setState({ ruleOpen: true })}
          >
            <View>签到规则</View>
            <Image className="size-14 ml-10" mode="aspectFit" src={img_arr} />
          </View>
        </View>
        <View className="text-(24 #11100F) mt-15">
          签到领取M币 累计获得：{state.sign?.totalSignedCoin ?? "--"}
        </View>
      </View>

      <View
        className="mx-40 b-(3 solid #E3D0C6) mb-45"
        style={{
          borderRadius: "20rpx 20rpx 0 0",
          backgroundImage:
            "linear-gradient(0deg, rgba(226,199,171,0.7), rgba(255,255,255,0.7))",
        }}
      >
        <View className="grid cols-4 gap-col-6 gap-row-16 px-30 py-60">
          {state.sign?.signConfigList.map((item, idx) => (
            <View
              key={item.id}
              className={clsx(
                "pos-relative h-188 bg-#fff rounded-10 box-border b-(6 solid #EFEFEF) rounded-12 b-x-4 last:col-span-2",
                "![&.active]:(bg-#E0AB81 b-#FDE3BA) ![&.active_.dtit]:(invisible) ![&.active_.dnum]:(text-#fff) ![&.active_.dck]:(visible) ![&.active_.dico1]:(block) ![&.active_.dico2]:(hidden)",
                { active: idx < (state.sign?.continueSignNum ?? 0) }
              )}
            >
              <View className="dtit text-(18 #666 center) py-10">
                第{item.signNo}天
              </View>

              <Image
                mode="aspectFit"
                className="dico1 block size-77 mx-auto hidden"
                src={img_sign_coin1}
              />

              <Image
                mode="aspectFit"
                className="dico2 block size-77 mx-auto"
                src={img_sign_coin2}
              />

              <View className="dnum text-(26 #8E4E36 center) py-5 fw-bold">
                +{item.coin}
              </View>
              <Image
                className="dck invisible pos-absolute left-1/2 -bottom-14 size-30 -translate-x-1/2"
                mode="aspectFit"
                src={img_sign_check}
              />
            </View>
          ))}
        </View>

        <View
          className={clsx(
            "w-375 h-86 rounded-full bg-#B7684A mx-auto text-(45/86 #FFFCFA center)",
            "![&.dis]:(bg-#A0A0A0 text-#fff)",
            { dis: !state.sign?.signTag }
          )}
          onClick={() => onSign()}
        >
          {state.sign?.signTag ? "签到领M币" : "已签到"}
        </View>
        <View className="text-(22 #955942 center) pt-10 pb-30">
          你已连续签到{state.sign?.continueSignNum ?? "--"}天
        </View>
      </View>

      {/* --------------------------- */}

      <View className="py-20">
        <View className="pos-relative flex px-40 h-84">
          <View className="text-(28/84 #8E4E36) fw-bold">全部商品</View>
          <View
            className="ml-a flex items-center"
            onClick={() => setState({ sortShow: true })}
          >
            <View className="text-(28 #666)">排序</View>
            <UnoIcon className="i-icon-park-outline:down size-20 text-#666 ml-5" />
          </View>
          {state.sortShow && (
            <>
              <View
                className="z-50 pos-fixed top-0 left-0 size-full bg-#000/60"
                catchMove
                onClick={() => setState({ sortShow: false })}
              ></View>
              <View
                className="z-50 pos-absolute top-1/1 right-25 w-288 bg-#fff rounded-5"
                catchMove
              >
                {sorts.map((item) => (
                  <View
                    key={item.key}
                    className={clsx(
                      "h-80 mx-44 b-t-(1 solid #eee) text-(26/80 center #232323) first:b-0",
                      state.sort == item.key && "!text-#92533C"
                    )}
                    onClick={() =>
                      setState({ sort: item.key, sortShow: false })
                    }
                  >
                    {item.name}
                  </View>
                ))}
              </View>
            </>
          )}
        </View>
        <View className="grid cols-2 px-40 gap-40">
          {list.list.map((item) => (
            <View
              className="rounded-24 overflow-hidden bg-#fff"
              key={item.id}
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/mall2/minfo/index?id=${item.id}`,
                })
              }
            >
              <Image
                className="h-315 w-full bg-#aaa"
                mode="aspectFill"
                src={item.coverImage}
              />
              <View className="h-145 px-15">
                <View className="h-80 text-(32/80 #11100F) line-clamp-1">
                  {item.name}
                </View>
                <View className="flex items-center">
                  <View className="flex-1 min-w-0 mr-20">
                    <Text className="text-(28 #11100F)">{item.saleCoin}</Text>
                    <Text className="text-(20 #11100F)"> M币</Text>
                  </View>
                  <View className="text-(20/36 center #fff) bg-#DEA266 rounded-full w-82 h-36">
                    兑换
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>

        <View className="text-(22 #999 center) py-30">
          {list.more ? "加载中..." : "没有更多数据了"}
        </View>
      </View>

      {/* ---- */}
      <CusPopup
        open={state.ruleOpen}
        onClose={() => setState({ ruleOpen: false })}
        title={"签到规则"}
      >
        <View className="px-60 pt-10 pb-100 max-h-60vh overflow-y-auto">
          <RichContent html={state.rule?.collectRuleContent} />
        </View>
      </CusPopup>

      <CusModal
        open={state.signOpen}
        onClose={() => setState({ signOpen: false })}
      >
        <View
          className="w-623 h-554 rounded-30 pt-100 bg-#fff box-border pos-relative"
          style={{
            backgroundImage:
              "linear-gradient(0deg, rgba(226,199,171,0.7), rgba(255,255,255,0.7))",
            boxShadow: "0rpx 0rpx 10rpx 0rpx rgba(0,0,0,.3)",
          }}
        >
          <View className="flex overflow-hidden justify-center -mb-40">
            <Image
              className="w-225 h-61 mt-10"
              mode="aspectFit"
              src={img_sign_modal2}
            />
            <View
              className="w-120 h-36 bg-#8E4E36 text-(22/36 center #fff)"
              style={{ borderRadius: "16rpx 16rpx 16rpx 0" }}
            >
              签到成功
            </View>
          </View>
          <View className="text-(72 center #8E4E36)">
            获得{state.signRes?.coin ?? "??"}M币
          </View>
          <View className="text-(30 #666 center) mt-40">
            连续签到可获得更多的M币
          </View>
          <View
            className="w-324 h-80 bg-#DEA266 rounded-full text-(38/80 center #11100F) mx-auto mt-90"
            style={{ boxShadow: "0rpx 0rpx 10rpx 0rpx rgba(0,0,0,.2)" }}
            onClick={() => setState({ signOpen: false })}
          >
            知道了
          </View>
        </View>
      </CusModal>
    </View>
  );
}
