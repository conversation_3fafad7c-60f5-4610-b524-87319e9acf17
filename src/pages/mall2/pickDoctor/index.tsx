import { Image, Text, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";

export default () => {
  const router = useRouter();
  const type = router.params.type || "";
  const [state, setState] = useSetState({
    data: null as any,
    crt: null as any,
  });

  useMount(() => {
    const data = Taro.getCurrentInstance().preloadData;
    const crt = type == "1" ? data?.shop : data?.doctor;
    setState({ data, crt });
  });

  const onOk = () => {
    const name = type == "1" ? "goods.pick.shop" : "goods.pick.doctor";
    Taro.eventCenter.trigger(name, state.crt);
    Taro.navigateBack();
  };

  const getNum = (item) => {
    const n = item?.doctor?.reservationTimes;
    if (n > 999) return "999+";
    return n || 0;
  };

  return (
    <View>
      <View className="p-25">
        {type == "1" &&
          state.data?.shops.map((item) => (
            <View
              key={item.id}
              className="bg-#fff rounded-15 flex items-center p-30 mt-20 first:mt-0"
              onClick={() => setState({ crt: item })}
            >
              <Image
                className="size-120 bg-#f5f5f5 rounded-15 mr-20"
                mode="aspectFill"
                src={item.shop?.logo}
                onClick={(e) => {
                  e.stopPropagation();
                  // console.log(item);
                  Taro.navigateTo({
                    url: `/pages/mall2/shopInfo/index?id=${item.shop?.id}`,
                  });
                }}
              ></Image>
              <View className="flex-1 min-w-0 flex flex-col">
                <View className="text-(28 #000)">{item.shop?.name}</View>
                <View className="text-(20 #666) py-5">
                  {item.shop?.address}
                </View>
                {/* <View className="text-(28 #000)">
                  门诊服务费：
                  <Text className="text-(24 #ad7f6d)">
                    &yen;<Text className="text-32">{item.outpatientFee}</Text>
                  </Text>
                </View> */}
              </View>
              <View className="p-30 pr-0">
                {state.crt?.id == item.id ? (
                  <View className="size-28 mx-auto bg-no-repeat bg-contain bg-center bg-[url(https://oss.gaomei168.com/file-release/20241123/1309814371477368832.png)]"></View>
                ) : (
                  <View className="size-28 mx-auto bg-no-repeat bg-contain bg-center bg-[url(https://oss.gaomei168.com/file-release/20241123/1309814461818482688.png)]"></View>
                )}
              </View>
            </View>
          ))}

        {type == "2" &&
          state.data?.shops
            ?.find((n) => n.id == state.data?.shop?.id)
            ?.servicerList.map((item) => (
              <View
                key={item.id}
                className="bg-#fff rounded-15 flex items-center p-30 mt-20 first:mt-0"
                onClick={() => {
                  setState({ crt: item });
                }}
              >
                <Image
                  className="size-120 bg-#f5f5f5 rounded-15 mr-20"
                  mode="aspectFill"
                  src={item?.doctor?.photo}
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log(item);
                    Taro.navigateTo({
                      url: `/pages/mall2/doctorInfo/index?id=${item.doctor?.id}&shopId=${item.serviceShopId}`,
                    });
                  }}
                ></Image>
                <View className="flex-1 min-w-0 flex flex-col">
                  <View className="text-(28 #000)">
                    项目治疗医生：{item?.doctor?.name}
                  </View>
                  <View className="text-(20 #666) py-5">
                    预约次数：{getNum(item)}次
                  </View>
                  {!!item?.serviceFee && (
                    <View className="text-(28 #000)">
                      治疗费用：
                      <Text className="text-(24 #ad7f6d)">
                        &yen;
                        <Text className="text-32">{item?.serviceFee}</Text>
                      </Text>
                    </View>
                  )}
                </View>
                <View className="p-30 pr-0">
                  {state.crt?.id == item.id ? (
                    <View className="size-28 mx-auto bg-no-repeat bg-contain bg-center bg-[url(https://oss.gaomei168.com/file-release/20241123/1309814371477368832.png)]"></View>
                  ) : (
                    <View className="size-28 mx-auto bg-no-repeat bg-contain bg-center bg-[url(https://oss.gaomei168.com/file-release/20241123/1309814461818482688.png)]"></View>
                  )}
                </View>
              </View>
            ))}
      </View>

      <View className="h-146"></View>
      <View className="pos-fixed left-0 bottom-0 h-146 w-full bg-#fff box-border p-30 flex">
        <View
          className="flex-1 flex items-center justify-center bg-#ad7f6d rounded-full text-(34 #fff)"
          onClick={onOk}
        >
          下一步
        </View>
      </View>
    </View>
  );
};
