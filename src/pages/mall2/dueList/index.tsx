import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { fdate, showToast } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import Taro, { useReachBottom } from "@tarojs/taro";
import { useMount } from "ahooks";
import clsx from "clsx";

const StateMap = [
  { id: 10, name: "待确认" },
  { id: 20, name: "待到店" },
  { id: 30, name: "已到店" },
  { id: 40, name: "已超时" },
  { id: 99, name: "已取消" },
];

export default function Page() {
  const [list, listAct] = useList({
    api: CusApi.getDueList,
  });

  useMount(() => {
    listAct.reload();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  const handleCancel = async (item: any) => {
    const md = await Taro.showModal({ title: "确定要取消这条预约吗？" });
    if (!md.confirm) return;

    await CusApi.cancelDue({ id: item.id });
    showToast("取消成功");
    listAct.reload();
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF px-30">
      <View className="py-80">
        {list.list.map((item) => (
          <View
            className="bg-#FDFCFA rounded-10 mt-20 first:mt-0 px-35"
            key={item.id}
          >
            <View className="flex items-center py-30">
              <Image
                className="size-100 rounded-full bg-#eee mr-50"
                mode="aspectFill"
                src={item.shop?.logo}
              />
              <View className="flex-1 min-w-0">
                <View className="text-(28 #1A1A1A) line-clamp-1">
                  {item.shop?.name}
                </View>
                <View className="text-(22 #636363) line-clamp-1 mt-5">
                  {item.shop?.address}
                </View>
              </View>
            </View>

            <View className="flex py-5">
              <View className="text-(26 #636363) w-150">预约项目</View>
              <View className="text-(29 #11100F) flex-1 min-w-0">
                {item.typeName}
              </View>
            </View>

            <View className="flex py-5">
              <View className="text-(26 #636363) w-150">预约时间</View>
              <View className="text-(29 #11100F) flex-1 min-w-0">
                {fdate(item.preTime, "YYYY-MM-DD HH:mm")}
              </View>
            </View>

            <View className="flex py-5">
              <View className="text-(26 #636363) w-150">预约备注</View>
              <View className="text-(29 #11100F) flex-1 min-w-0">
                {item.content}
              </View>
            </View>

            <View className="flex justify-end pb-40 pt-30">
              {[10, 20, 40].includes(item.state) && (
                <View
                  className="w-174 h-64 bg-#EFEFEF rounded-16 text-(26/64 center #636363) ml-15"
                  onClick={() => handleCancel(item)}
                >
                  取消
                </View>
              )}

              <View
                className={clsx(
                  "w-174 h-64 bg-#EFEFEF rounded-16 text-(26/64 center #636363) ml-15",
                  [10, 20].includes(item.state) && "!bg-#8E4E36 !text-#fff"
                )}
              >
                {StateMap.find((n) => n.id == item.state)?.name}
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
}
