import { UnoIcon } from "@/components/UnoIcon";
import { Button, Image, Text, Video, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";
import { useRef } from "react";
import { Comment } from "./Comment";
import { usePageStat } from "@/stores/usePageStat";
import { useShare } from "@/stores/useShare";
import CusApi from "@/services/CusApi";
import { str2arr } from "@/utils/tools";
import { img_avatar } from "@/utils/images";
import { makeApi } from "@/utils/request";

const ftime = (n) => {
  const s = Math.floor(n % 60);
  const m = Math.floor(n / 60);
  const pad = (n) => (n < 10 ? "0" + n : "" + n);

  return pad(m) + ":" + pad(s);
};

const postStat = makeApi("post", `/api/appInformationArticleInteraction/add`);

export default function Page() {
  const router = useRouter();
  const vref = useRef<any>(null);
  const [state, setState] = useSetState({
    rect: null as any,
    sh: 44,
    ww: 375,

    playing: true,

    crt: 0,
    duration: 0,
    touching: false,
    rate: 0,

    openComment: false,
    showContent: false,

    data: null as any,
    userVip: null as any,
  });

  useMount(() => {
    postStat({ source: 1, articleId: router.params.id, type: 10 });
  });

  useMount(() => {
    const win = Taro.getWindowInfo();
    const sh = win.statusBarHeight || 0;
    const ww = win.windowWidth;

    const rect = Taro.getMenuButtonBoundingClientRect();

    setState({ sh, ww, rect });

    vref.current = Taro.createVideoContext("myVideo");
  });

  // =====
  usePageStat({
    id: state.data?.id,
    title: state.data?.title,
  });

  useMount(() => {
    fetchInfo();
    fetchUserVip();
  });

  const fetchUserVip = async () => {
    // 判断本地是否有token，只有在有token的情况下才请求用户VIP信息
    const token = Taro.getStorageSync("token");
    if (token) {
      try {
        const userVip = await CusApi.getUserInfo();
        setState({ userVip });
      } catch (error) {
        console.log("获取用户VIP信息失败", error);
      }
    }
  };

  useShare(() => {
    postStat({ source: 1, articleId: router.params.id, type: 40 });
    return {
      title: state.data?.title,
      imageUrl: state.data?._cover,
      path: `/pages/mall2/noteinfo2/index?id=${state.data?.id}`,
    };
  });

  const fetchInfo = async () => {
    const id = router.params.id;
    const res = await CusApi.getNoteInfo({ id });
    const imgs = str2arr(res?.images);
    const cover = res?.model == 1 ? imgs?.[0] : res?.videoCoverImage;
    res._imgs = imgs;
    res._cover = cover;

    setState({ data: res });
  };

  const toggleLike = async () => {
    if (!state.data?.id) return;
    const data = { source: 1, articleId: router.params.id, type: 20 };
    if (state.data?.likesTag) {
      await CusApi.delNoteAct(data);
    } else {
      await CusApi.addNoteAct(data);
    }
    fetchInfo();
  };

  return (
    <View className="h-100vh bg-#000 flex flex-col">
      <View style={{ height: state.sh }} />
      <View className="flex-1 min-h-0 pos-relative">
        <Video
          id="myVideo"
          className="size-full"
          src={state.data?.videoUrl}
          controls={false}
          autoplay
          loop
          onTimeUpdate={(e) => {
            setState({
              crt: e.detail.currentTime,
              duration: e.detail.duration,
            });
          }}
          onPlay={() => setState({ playing: true })}
          onPause={() => setState({ playing: false })}
          onClick={() => {
            if (state.playing) {
              vref.current?.pause();
            } else {
              vref.current?.play();
            }
          }}
        />

        <View
          className="pos-fixed top-0 left-0 flex items-center justify-center bg-#000/30 rounded-full"
          style={{
            top: state.rect?.top,
            height: state.rect?.height,
            width: state.rect?.height,
            marginLeft: 7,
          }}
          onClick={() => {
            Taro.navigateBack({
              fail: () => Taro.reLaunch({ url: "/pages/tabs/mall/index" }),
            });
          }}
        >
          <UnoIcon className="i-icon-park-outline:left size-50 text-#fff" />
        </View>

        {!state.playing && (
          <Image
            className="pos-absolute top-1/2 left-1/2 -translate-1/2 size-120 opacity-90 pointer-events-none"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250325/1354044653039943680_200_200.png"
            style={{ filter: "drop-shadow(0 0 4rpx #ccc)" }}
          />
        )}

        <View
          className={clsx(
            "z-5 pos-absolute left-0 bottom-0 w-full",
            state.touching && "opacity-0"
          )}
        >
          <View className="px-30">
            <View
              className="flex items-center"
              onClick={() => {
                // 只有当用户不是NomalUser时才允许跳转
                if (
                  state.userVip &&
                  state.userVip.vipLevelCode !== "NomalUser"
                ) {
                  Taro.navigateTo({
                    url: `/pages/user/user/index?id=${state.data?.appUserId}`,
                  });
                }
                // 如果是普通用户，点击不做任何操作
              }}
            >
              <Image
                className="size-70 bg-#eee rounded-full mr-20"
                src={state.data?.appUser?.photo || img_avatar}
              />
              <View className="text-(32 #fff) fw-bold">
                {state.data?.appUser?.name}
              </View>
            </View>
            <View className="text-(28/50 #fff) mt-20 whitespace-pre-wrap break-all">
              {state.data?.title}{" "}
              <Text
                className="text-#666 px-20"
                onClick={() =>
                  setState({ openComment: true, showContent: true })
                }
              >
                展开
              </Text>
            </View>
          </View>
          <View
            className="pt-20"
            onTouchMove={(e: any) => {
              const ts = e.touches[0].pageX;
              const rate = ts / state.ww;
              setState({ rate, touching: true });
            }}
            onTouchEnd={(e: any) => {
              const ts = e.changedTouches[0].pageX;
              const rate = ts / state.ww;
              setState({ rate, touching: false });
              vref.current?.seek(state.duration * rate);
            }}
          >
            <View className="h-4 w-full bg-#333">
              <View
                className="w-0 h-full bg-#666"
                style={{
                  width: Math.floor((state.crt / state.duration) * 100) + "%",
                }}
              />
            </View>
          </View>
        </View>

        <View
          className={clsx(
            "pos-absolute left-0 bottom-0 w-full pointer-events-none",
            !state.touching && "opacity-0"
          )}
        >
          <View className="flex items-center justify-center pb-10">
            <View className="text-(32 #ccc)">
              <View className="inline-block text-#fff">
                {ftime(state.rate * state.duration)}
              </View>
              <View className="inline-block mx-10">/</View>
              <View className="inline-block">{ftime(state.duration)}</View>
            </View>
          </View>
          <View className="pt-20">
            <View className="h-6 w-full bg-#333 rounded-6">
              <View
                className="w-0 h-full bg-#fff"
                style={{ width: state.rate * 100 + "%" }}
              />
            </View>
          </View>
        </View>
      </View>
      <View className="h-100 flex items-center px-30">
        <View className="flex items-center pos-relative">
          <UnoIcon className="i-icon-park-outline:share size-30 text-#fff" />
          <View className="text-(24 #fff center) ml-10">分享好友</View>
          <Button
            className="pos-absolute top-0 left-0 size-full opacity-0"
            openType="share"
          ></Button>
        </View>
        <View className="flex-1"></View>
        <View className="flex items-center" onClick={() => toggleLike()}>
          {state.data?.likesTag ? (
            <UnoIcon className="i-icon-park-solid:like size-54 text-red ml-30" />
          ) : (
            <UnoIcon className="i-icon-park-outline:like size-54 text-#fff ml-30" />
          )}
          <View className="text-(28 #fff center) ml-10">
            {state.data?.likesNum || 0}
          </View>
        </View>
        <View
          className="flex items-center"
          onClick={() => setState({ openComment: true, showContent: false })}
        >
          <UnoIcon className="i-icon-park-outline:message-one size-54 text-#fff ml-30" />
          <View className="text-(28 #fff center) ml-10">
            {state.data?.commentsNum || 0}
          </View>
        </View>
      </View>
      <View style={{ height: `env(safe-area-inset-bottom)` }} />

      <Comment
        aid={router.params.id}
        open={state.openComment}
        onClose={() => setState({ openComment: false })}
        showContent={state.showContent}
        data={state.data}
      />
    </View>
  );
}
