import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { fdate } from "@/utils/tools";
import { View } from "@tarojs/components";
import Taro, { useReachBottom } from "@tarojs/taro";
import { useMount } from "ahooks";
import { useEffect } from "react";

export default function Page() {
  const [list, listAct] = useList({
    api: CusApi.getFeed,
  });

  useEffect(() => {
    Taro.eventCenter.on("mailbox.del", () => listAct.reload());
    return () => {
      Taro.eventCenter.off("mailbox.del");
    };
  }, []);

  useMount(() => {
    listAct.reload();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  const handleDel = async (item: any) => {
    const md = await Taro.showModal({ title: "确定要删除条建议吗？" });
    if (!md.confirm) return;
    await CusApi.delFeed({ ids: item.id }, { _loading: "删除中..." });
    listAct.reload();
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="py-80 px-25">
        {list.list.map((item) => (
          <View
            key={item.id}
            className="bg-#FDFCFA rounded-10 mt-20 first:mt-0 px-35 py-40"
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/mall2/mailboxInfo/index?id=${item.id}`,
              });
            }}
          >
            <View className="text-(28/38 #1A1A1A)">{item.mobile}</View>

            <View className="text-(26/38 #636363) py-10">{item.content}</View>

            <View className="mt-20">
              <View>
                <View className="inline-block text-(26 #636363) w-160">
                  提交时间
                </View>
                <View className="inline-block text-(29 #11100F)">
                  {fdate(item.createDate, "YYYY-MM-DD HH:mm:ss")}
                </View>
              </View>
              {!!item.readDate && (
                <View>
                  <View className="inline-block text-(26 #636363) w-160">
                    结束时间
                  </View>
                  <View className="inline-block text-(29 #11100F)">
                    {fdate(item.readDate, "YYYY-MM-DD HH:mm:ss")}
                  </View>
                </View>
              )}
            </View>

            <View className="flex justify-end pt-100">
              <View
                className="ml-20 w-174 h-62 rounded-16 bg-#EFEFEF text-(30/62 #5A5A5A center) b-(1 solid #EFEFEF)"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDel(item);
                }}
              >
                删除
              </View>
              {!item.state ? (
                <View className="ml-20 w-174 h-62 rounded-16 bg-#EFEFEF text-(30/62 #5A5A5A center) b-(1 solid #EFEFEF) uno-layer-z1-(b-#8E4E36 text-#fff bg-#8E4E36)">
                  待查看
                </View>
              ) : (
                <View className="ml-20 w-174 h-62 rounded-16 bg-#EFEFEF text-(30/62 #5A5A5A center) b-(1 solid #EFEFEF) uno-layer-z1-(b-#8E4E36 text-#8E4E36 bg-#F7F4EF)">
                  已收到
                </View>
              )}
            </View>
          </View>
        ))}
      </View>
    </View>
  );
}
