import { useShare } from "@/stores/useShare";
import { Button, Image, Text, View } from "@tarojs/components";

const img_bg = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241021/1297947539831263232.png`;
const img_logo = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241021/1297947539822874624.png`;
const img_cur = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241021/1297947539873206272.png`;
const img_qr = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241021/1297966497439420416.png`;

export default function Referrer() {
  useShare(() => {
    return {
      title: "搞美推荐官",
      // path: ''
      // imageUrl: '',
    };
  });

  return (
    <View className="min-h-100vh bg-#F7F4EF overflow-hidden">
      <View className="flex justify-end pt-35 -mb-397">
        <Image className="size-397" mode="aspectFit" src={img_bg} />
      </View>
      <Image
        className="pos-relative w-194 h-40 mt-140 block mx-auto mt-10"
        mode="aspectFit"
        src={img_logo}
      />
      <View className="pos-relative text-(80 #1a1a1a center) fw-bold font-italic">
        搞美推荐官
      </View>
      <View className="pos-relative h-26 w-624 rounded-full b-(24 solid #8E4E36) bg-#AD7F6D mt-50 mx-auto -mb-37"></View>

      <View className="pos-relative w-593 bg-#fff mx-auto overflow-hidden mb-100">
        {false && (
          <>
            <View className="text-(38 #11100F center) fw-bold pt-60 pb-20">
              申请条件
            </View>
            <View className="text-(30/46 #1A1918 center)">
              你已成功推荐1位品牌新友到店消费
            </View>
            <View className="text-(30/46 #1A1918 center)">
              即刻申请认证成为
              <Text className="text-#8E4E36">「搞美推荐官」</Text>
            </View>

            <View className="text-(38 #11100F center) fw-bold pt-65 pb-25">
              「搞美推荐官」认证成功
            </View>

            <View className="text-(30/46 #1A1918 center)">
              解锁推荐返现特权（返现比例
              <Text className="text-#8E4E36">10%</Text>）
            </View>
            <View className="text-(30/46 #1A1918 center)">解锁团购价特权</View>
          </>
        )}

        <View className="text-(38 #11100F center) fw-bold pt-100 pb-20">
          暂未上线 敬请期待
        </View>

        <Image
          className="size-320 block mx-auto mt-70"
          showMenuByLongpress
          mode="aspectFit"
          src={img_qr}
        />
        <View className="text-(22 #666 center) mt-10 pb-70">
          长按识别二维码
        </View>
      </View>

      <View
        className="pos-relative w-554 h-112 rounded-full bg-#fff text-(42 #8E4E36) mx-auto mt-50 mb-200 flex items-center justify-center"
        style={{
          backgroundImage:
            "linear-gradient(0deg, rgba(142,78,54,.3),rgba(255,230,221,.3))",
        }}
      >
        <View>立即成为搞美推荐官</View>
        <Image className="size-53 ml-15" mode="aspectFit" src={img_cur} />
        <Button
          openType="share"
          className="pos-absolute top-0 left-0 size-full opacity-0"
        ></Button>
      </View>
    </View>
  );
}
