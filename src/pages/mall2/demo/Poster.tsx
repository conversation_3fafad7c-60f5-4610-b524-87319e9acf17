import { Button, Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";

export default () => {
  return (
    <View>
      <View className="w-600 bg-pink-2" id="demo">
        <Image
          className="size-600"
          mode="aspectFill"
          src="https://gd-hbimg.huaban.com/6f0329646422daf8dce4c898a4f08f413d9ca7afb1c1-tqIXX6_fw658webp"
        ></Image>
        <View className="flex items-center">
          <View>
            <View>我是某个面膜</View>
            <View>100.00</View>
          </View>
        </View>
      </View>

      <Button
        onClick={() => {
          Taro.createSelectorQuery()
            .select("#demo")
            .node()
            .exec((res) => {
              console.log(res);
            });
        }}
      >
        生成
      </Button>
    </View>
  );
};
