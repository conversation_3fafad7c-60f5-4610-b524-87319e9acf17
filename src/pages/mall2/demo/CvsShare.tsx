import { Canvas, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount } from "ahooks";

export default () => {
  useMount(() => {
    init();
  });

  const init = () => {
    Taro.createSelectorQuery()
      .select("#cvsShare")
      .fields({ node: true, size: true })
      .exec(async (res) => {
        // console.log(res);
        const cvs = res[0].node;
        const ctx: CanvasRenderingContext2D = cvs.getContext("2d");
        const dpr = Taro.getSystemInfoSync().pixelRatio;

        const cw = res[0].width;
        const ch = res[0].height;
        cvs.width = cw * dpr;
        cvs.height = ch * dpr;
        ctx.scale(dpr, dpr);

        const loadimg = async (url): Promise<any> => {
          return new Promise((onOk) => {
            const img = cvs.createImage();
            img.onload = () => onOk(img);
            img.src = url;
          });
        };

        const img = await loadimg(
          "https://gd-hbimg.huaban.com/6f0329646422daf8dce4c898a4f08f413d9ca7afb1c1-tqIXX6_fw658webp"
        );

        const iw = img.width;
        const ih = img.height;
        const r1 = cw / ch;
        const r2 = iw / ih;
        let sx, sy, sw;

        if (r1 < r2) {
          console.log(111);
          sx = (iw - ih) / 2;
          sy = 0;
          sw = ih;
        } else {
          console.log(222);
          sx = 0;
          sy = (ih - iw) / 2;
          sw = iw;
        }

        ctx.drawImage(img, sx, sy, sw, sw, 0, 0, cw, cw);

        ctx.font = "14px sans-serif";
        ctx.textBaseline = "middle";
        ctx.fillText("某某面膜某某面膜面膜...", 20, cw + 20);
        ctx.fillText("折扣价：123123", 20, cw + 20 + 24);
        console.log(cw, ch);
      });
  };

  return (
    <View>
      <View>123123</View>
      <Canvas className="w-600 h-800 bg-pink" type="2d" id="cvsShare" />
    </View>
  );
};
