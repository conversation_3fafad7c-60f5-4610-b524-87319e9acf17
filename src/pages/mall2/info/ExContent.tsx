import CusApi from "@/services/CusApi";
import { Image, View } from "@tarojs/components";
import { useRequest } from "ahooks";

export const ExContent = () => {
  const req = useRequest(CusApi.getAdv, {
    defaultParams: [{ showPosition: 4 }],
  });

  const img = req.data?.list?.[0]?.image || "";

  return (
    // 16 * 1.12 = 17.92
    <View className="-mt-[19px]">
      <Image src={img} className="w-full h-auto block" mode="widthFix" />
    </View>
  );
};
