import { View } from "@tarojs/components";
import { useSetState } from "ahooks";
import clsx from "clsx";
import { useEffect } from "react";

export const ModeList = (props: { data: any; onChange?: any }) => {
  const [state, setState] = useSetState({
    mode: null as any,
    modes: [] as any[],
  });

  useEffect(() => {
    const data = props.data;
    const modes: any[] = [];

    if (data.offlineConsumeTag) {
      modes.push({ id: 1, name: "到店服务" });
    }
    if (data.onlineDeliveryTag) {
      modes.push({ id: 2, name: "送货到家" });
    }

    const mode = modes?.[0]?.id ?? null;

    setState({ mode, modes });
  }, [props.data.id]);

  useEffect(() => {
    props.onChange?.(state.mode);
  }, [state.mode]);

  return (
    <View>
      <View className="text-(28 #333) fw-bold pt-40 pb-30">服务类型</View>

      <View className="flex flex-wrap gap-30">
        {state.modes.map((item) => (
          <View
            className={clsx(
              "relative b-(2 solid #f8f7fc) max-w-7/10 p-10 text-(26 #333) bg-#f8f7fc rounded-10", // UnoCSS: Base style
              item.id == state.mode &&
                "uno-layer-z1-(bg-#8E4E36/4 b-#8E4E36 text-#8E4E36)"
            )}
            onClick={() => setState({ mode: item.id })}
          >
            <View className="line-clamp-1">{item.name}</View>
          </View>
        ))}
      </View>
    </View>
  );
};
