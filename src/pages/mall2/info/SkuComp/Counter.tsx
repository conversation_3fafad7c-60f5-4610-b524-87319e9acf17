import { UnoIcon } from "@/components/UnoIcon";
import { View } from "@tarojs/components";
import React from "react";

interface CounterProps {
  value: number;
  onChange: (newValue: number) => void;
  min?: number; // Optional minimum value, defaults to 1
  max?: number; // Optional maximum value
}

export const Counter: React.FC<CounterProps> = ({
  value,
  onChange,
  min = 1,
  max,
}) => {
  return (
    <View className="inline-flex mt-a">
      <View
        className="size-40 bg-#f5f4f9 flex items-center justify-center"
        onClick={() => {
          const num = Math.max(min, value - 1);
          onChange(num);
        }}
      >
        <UnoIcon className="i-icon-park-outline:minus size-30 text-#515055" />
      </View>
      <View className="text-(24 #333) min-w-60 flex items-center justify-center">
        {value}
      </View>
      <View
        className="size-40 bg-#f5f4f9 flex items-center justify-center"
        onClick={() => {
          const num = value + 1;
          if (!max || num <= max) onChange(num);
        }}
      >
        <UnoIcon className="i-icon-park-outline:plus size-30 text-#515055" />
      </View>
    </View>
  );
};
