import { View, Text } from "@tarojs/components";
import { useState, useEffect, useMemo, useCallback } from "react";
import clsx from "clsx";

// --- 将类型定义移到内部 ---
export interface AttributeValue {
  attributeValue: string;
  id: string;
}

export interface Attribute {
  attributeValueList: AttributeValue[];
  name: string;
  id: string;
}

export interface ProductSpecification {
  updateDate?: number;
  costFee?: number;
  productId?: string;
  salePrice?: number;
  oldPrice?: number;
  inventoryCount?: number;
  inventoryLimit?: number; // 0: 不限制, 1: 限制
  saleIn?: number;
  sort?: number;
  includeNum?: number;
  productName?: string;
  serialNo?: string;
  soldCount?: number;
  name?: string; // e.g., "红色,1mg,手打,1年"
  recommendTag?: number;
  id: string;
  attributeValueIds: string; // Comma-separated IDs
  createDate?: number;
  [key: string]: any; // Allow other properties
}

export interface SkuData {
  attributeList?: Attribute[];
  productSpecificationList?: ProductSpecification[];
  [key: string]: any; // Allow other top-level properties if needed
}
// --- 类型定义结束 ---

// 定义规格值状态
type ValueState = "normal" | "selected" | "disabled" | "outOfStock";

interface SkuListProps {
  data?: SkuData;
  loading?: boolean;
  error?: Error | null;
  defaultSpecification?: ProductSpecification;
  onChange?: (
    selectedSku: ProductSpecification | null,
    missingAttrs?: Attribute[]
  ) => void;
}

// 自定义 Hook: 处理 SKU 状态
const useSkuState = (
  data: SkuData | undefined,
  selectedSpecs: Record<string, string>
) => {
  // 创建 valueId -> attributeId 的映射
  const valueIdToAttributeIdMap = useMemo(() => {
    const map = new Map<string, string>();
    data?.attributeList?.forEach((attr) => {
      attr.attributeValueList.forEach((val) => {
        map.set(val.id, attr.id);
      });
    });
    return map;
  }, [data?.attributeList]);

  // 创建所有 SKU 的规格值 ID 集合映射
  const skuValueIdsMap = useMemo(() => {
    const map = new Map<string, Set<string>>();
    data?.productSpecificationList?.forEach((sku) => {
      map.set(sku.id, new Set(sku.attributeValueIds.split(",")));
    });
    return map;
  }, [data?.productSpecificationList]);

  // 创建规格值 ID 到 SKU 的映射
  const valueIdToSkusMap = useMemo(() => {
    const map = new Map<string, ProductSpecification[]>();
    data?.productSpecificationList?.forEach((sku) => {
      const valueIds = sku.attributeValueIds.split(",");
      valueIds.forEach((vid) => {
        if (!map.has(vid)) {
          map.set(vid, []);
        }
        map.get(vid)?.push(sku);
      });
    });
    return map;
  }, [data?.productSpecificationList]);

  // 缓存规格值状态
  const valueStateCache = useMemo(() => {
    const cache = new Map<string, ValueState>();

    if (!data?.attributeList) return cache;

    data.attributeList.forEach((attribute) => {
      attribute.attributeValueList.forEach((value) => {
        const key = `${attribute.id}-${value.id}`;
        const isSelected = selectedSpecs[attribute.id] === value.id;
        const skusContainingValue = valueIdToSkusMap.get(value.id) || [];

        let isCompatible = false;
        let isAvailable = false;

        for (const sku of skusContainingValue) {
          const skuValueIds = skuValueIdsMap.get(sku.id);
          if (!skuValueIds) continue;

          let currentSkuCompatible = true;
          for (const [attrId, selectedValId] of Object.entries(selectedSpecs)) {
            if (attrId === attribute.id) continue;
            if (!skuValueIds.has(selectedValId)) {
              currentSkuCompatible = false;
              break;
            }
          }

          if (currentSkuCompatible) {
            isCompatible = true;
            const hasStock = !(
              sku.inventoryLimit === 1 && sku.inventoryCount === 0
            );
            if (hasStock) {
              isAvailable = true;
              break;
            }
          }
        }

        if (isSelected) {
          cache.set(key, "selected");
        } else if (isCompatible && isAvailable) {
          cache.set(key, "normal");
        } else if (isCompatible && !isAvailable) {
          cache.set(key, "outOfStock");
        } else {
          cache.set(key, "disabled");
        }
      });
    });

    return cache;
  }, [data?.attributeList, selectedSpecs, valueIdToSkusMap, skuValueIdsMap]);

  const getValueState = useCallback(
    (attributeId: string, valueId: string): ValueState => {
      return valueStateCache.get(`${attributeId}-${valueId}`) || "disabled";
    },
    [valueStateCache]
  );

  return {
    getValueState,
    valueIdToAttributeIdMap,
    skuValueIdsMap,
    valueIdToSkusMap,
  };
};

export const SkuList = ({
  data,
  loading,
  error,
  defaultSpecification,
  onChange,
}: SkuListProps) => {
  // 初始化默认选中的规格
  const getInitialSelectedSpecs = useCallback(() => {
    if (!defaultSpecification?.attributeValueIds || !data?.attributeList) {
      return {};
    }

    const defaultValueIds = defaultSpecification.attributeValueIds.split(",");
    const initialSpecs: Record<string, string> = {};

    // 创建 valueId -> attributeId 的映射
    const valueIdToAttributeIdMap = new Map<string, string>();
    data.attributeList.forEach((attr) => {
      attr.attributeValueList.forEach((val) => {
        valueIdToAttributeIdMap.set(val.id, attr.id);
      });
    });

    // 根据默认规格的 attributeValueIds 设置初始选中状态
    defaultValueIds.forEach((valueId) => {
      const attributeId = valueIdToAttributeIdMap.get(valueId);
      if (attributeId) {
        initialSpecs[attributeId] = valueId;
      }
    });

    return initialSpecs;
  }, [defaultSpecification, data?.attributeList]);

  const [selectedSpecs, setSelectedSpecs] = useState<Record<string, string>>(
    getInitialSelectedSpecs
  );

  const { getValueState, skuValueIdsMap } = useSkuState(data, selectedSpecs);

  // 当 defaultSpecification 或 data 变化时，重新设置默认选中状态
  useEffect(() => {
    const initialSpecs = getInitialSelectedSpecs();
    setSelectedSpecs(initialSpecs);
  }, [getInitialSelectedSpecs]);

  // 计算未选择的属性
  const missingAttributes = useMemo(() => {
    if (!data?.attributeList) return [];
    return data.attributeList.filter((attr) => !selectedSpecs[attr.id]);
  }, [data?.attributeList, selectedSpecs]);

  // 派生状态：当前选中的 SKU
  const selectedSku = useMemo(() => {
    if (!data?.productSpecificationList || !data?.attributeList) return null;

    const selectedValueIds = Object.values(selectedSpecs);
    if (selectedValueIds.length !== data.attributeList.length) {
      return null;
    }

    const selectedIdsSet = new Set(selectedValueIds);

    return (
      data.productSpecificationList.find((sku) => {
        const skuValueIds = skuValueIdsMap.get(sku.id);
        return (
          skuValueIds &&
          skuValueIds.size === selectedIdsSet.size &&
          [...skuValueIds].every((id) => selectedIdsSet.has(id))
        );
      }) || null
    );
  }, [selectedSpecs, data, skuValueIdsMap]);

  // 当选中 SKU 变化时，通过 onChange 回调通知父组件
  useEffect(() => {
    onChange?.(
      selectedSku,
      missingAttributes.length > 0 ? missingAttributes : undefined
    );
  }, [selectedSku, missingAttributes, onChange]);

  // 处理规格项点击事件
  const handleSpecClick = useCallback(
    (attributeId: string, valueId: string) => {
      setSelectedSpecs((prev) => {
        const currentSelectedValue = prev[attributeId];
        if (currentSelectedValue === valueId) {
          const { [attributeId]: _, ...rest } = prev;
          return rest;
        } else {
          return {
            ...prev,
            [attributeId]: valueId,
          };
        }
      });
    },
    []
  );

  if (loading) {
    return (
      <View className="flex items-center justify-center p-40">
        <Text className="text-(28 #999)">加载中...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex items-center justify-center p-40">
        <Text className="text-(28 #ff4d4f)">加载失败，请重试</Text>
      </View>
    );
  }

  if (!data?.attributeList?.length) {
    return null;
  }

  return (
    <View className="">
      {data.attributeList.map((attribute) => (
        <View key={attribute.id}>
          <View className="text-(28 #333) fw-bold pt-40 pb-30">
            {attribute.name}
          </View>

          <View className="flex flex-wrap gap-30">
            {attribute.attributeValueList.map((value) => {
              const state = getValueState(attribute.id, value.id);
              const isClickable = state === "normal" || state === "selected";

              return (
                <View
                  key={value.id}
                  className={clsx(
                    "relative b-(2 solid #f8f7fc) max-w-7/10 p-10 text-(26 #333) bg-#f8f7fc rounded-10",
                    state === "selected" &&
                      "uno-layer-z1-(bg-#8E4E36/4 b-#8E4E36 text-#8E4E36)",
                    (state === "disabled" || state === "outOfStock") &&
                      "uno-layer-z2-(b-#eee bg-#fdfdfd text-#bbb opacity-60 line-through)",
                    isClickable && "cursor-pointer hover:opacity-80"
                  )}
                  onClick={() =>
                    isClickable && handleSpecClick(attribute.id, value.id)
                  }
                >
                  <View className="line-clamp-1">{value.attributeValue}</View>
                  {state === "outOfStock" && (
                    <Text className="absolute top--8 right--8 text-(18) bg-gray-400 text-white p-(x4 y2) rounded-sm scale-80 origin-top-right">
                      缺货
                    </Text>
                  )}
                </View>
              );
            })}
          </View>
        </View>
      ))}
    </View>
  );
};
