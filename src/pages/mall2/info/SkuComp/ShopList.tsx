import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { formatDis } from "@/utils/tools";
import { Image, ScrollView, Text, View } from "@tarojs/components"; // 添加 Text
import Taro from "@tarojs/taro";
import { useSetState, useUpdateEffect } from "ahooks"; // 添加 useUpdateEffect
import clsx from "clsx";
// import { useEffect } from "react"; // 移除未使用的 useEffect

const WeScroll = (props: { children?: any }) => {
  return (
    <View className="overflow-hidden">
      <View className="-mb-30">
        <ScrollView scrollX enhanced showScrollbar={false}>
          <View className="ws-nowrap text-0">{props.children}</View>
          <View className="h-30"></View>
        </ScrollView>
      </View>
    </View>
  );
};

// 更新 props 定义
export const ShopList = (props: {
  data: any;
  mode: any;
  sku: any;
  onChange?: (shop: any, doctor: any) => void; // 更新 onChange 回调，包含 doctor
  hideUI?: boolean; // 添加 hide 参数
}) => {
  // 更新 state 定义
  const [state, setState] = useSetState<{
    shops: any[];
    shop: any;
    doctors: any[]; // 添加 doctors
    doctor: any; // 添加 doctor
  }>({
    shops: [],
    shop: null,
    doctors: [], // 初始化 doctors
    doctor: null, // 初始化 doctor
  });

  const getShops = async () => {
    // 需要同时选择了 mode 和 sku 才去请求
    if (!props.mode || !props.sku) {
      setState({ shops: [], shop: null, doctors: [], doctor: null }); // 清空门店和医生
      props.onChange?.(null, null); // 通知父组件清空选择 (shop, doctor)
      return;
    }

    const pos = Taro.getStorageSync("pos");
    // Taro.showLoading({ title: "加载门店..." }); // 移除加载提示
    try {
      const res = await CusApi.getGoodsSkuShop({
        productId: props.data?.id,
        model: props.mode, // 使用 props.mode
        longitude: pos?.longitude,
        latitude: pos?.latitude,
        productSpecificationId: props.sku?.id, // 使用 props.sku.id
      });
      const shops = res?.list || [];
      const firstShop = shops[0] || null; // 获取第一个门店，如果列表为空则为 null
      setState({ shops, shop: firstShop, doctors: [], doctor: null }); // 设置第一个门店为选中，清空医生
      props.onChange?.(firstShop, null); // 通知父组件默认选中第一个门店 (shop, doctor)
    } catch (error) {
      console.error("获取门店失败:", error);
      setState({ shops: [], shop: null, doctors: [], doctor: null }); // 出错时清空门店和医生
      props.onChange?.(null, null); // 通知父组件清空选择 (shop, doctor)
    } finally {
      // Taro.hideLoading(); // 移除加载提示
    }
  };

  const getDoctors = async () => {
    // 确保 mode, sku, shop 都已选择
    if (!props.mode || !props.sku?.id || !state.shop?.id) {
      setState({ doctors: [], doctor: null }); // 清空医生列表
      // 不需要通知父组件，因为医生是门店的子集
      return;
    }

    // Taro.showLoading({ title: "加载医生..." }); // 移除加载提示
    try {
      const res = await CusApi.getGoodsSkuCalc({
        model: props.mode, // 使用 props.mode
        productSpecificationId: props.sku?.id, // 使用 props.sku.id
        shopId: state.shop?.id, // 使用 state.shop.id
      });

      const list = res.serviceFeeList?.map((n) => {
        return {
          id: n.doctor?.id,
          name: n.doctor?.name ?? "--",
          photo: n.doctor?.photo ?? "",
          fee: n.serviceFee,
        };
      });

      const doctors = list || [];
      const doctor = doctors[0]; // 默认选中第一个医生
      setState({ doctors, doctor });
      props.onChange?.(state.shop, doctor); // 通知父组件医生选择变化
    } catch (error) {
      console.error("获取医生失败:", error);
      setState({ doctors: [], doctor: null }); // 出错时清空
      props.onChange?.(state.shop, null); // 通知父组件医生选择变化
    } finally {
      // Taro.hideLoading(); // 移除加载提示
    }
  };

  // mode 或 sku 变化时重新获取门店，并清空医生
  useUpdateEffect(() => {
    setState({ doctors: [], doctor: null }); // 清空医生
    getShops();
  }, [props.mode, props.sku]);

  // shop 变化时获取医生
  useUpdateEffect(() => {
    getDoctors();
  }, [state.shop]); // 依赖 state.shop

  const handleSelectShop = (item) => {
    // 如果点击的是当前已选门店，则不执行任何操作
    if (state.shop?.id === item.id) {
      return;
    }
    // 选择门店时，清空已选医生，并触发医生列表获取
    setState({ shop: item, doctor: null });
    props.onChange?.(item, null); // 通知父组件门店变化，医生清空
    // getDoctors 会在 shop 的 useUpdateEffect 中自动触发
  };

  const handleSelectDoctor = (item) => {
    // 如果点击的是当前已选医生，则不执行任何操作
    if (state.doctor?.id === item.id) {
      return;
    }
    setState({ doctor: item });
    props.onChange?.(state.shop, item); // 通知父组件医生选择变化
  };

  // 如果未选择 mode 或 sku，则不显示门店选择区域
  if (!props.mode || !props.sku) {
    return null;
  }

  // 如果 hide 为 true，则不渲染 UI，但逻辑照常执行
  if (props.hideUI) {
    return null;
  }

  return (
    <View>
      {!!state.shops.length && (
        <View>
          <View className="text-(28 #333) fw-bold pt-40 pb-30">选择门店</View>
          <WeScroll>
            {state.shops.map((item) => {
              const dis = formatDis(item.distance);
              const active = state.shop?.id === item.id; // 使用 === 比较

              return (
                <View
                  key={item.id}
                  className={clsx(
                    "ws-normal inline-flex flex-col gap-10 justify-center box-border b-(2 solid #f2f2f2) px-25 py-20 w-450 bg-#f2f2f2 ml-30 first:ml-0 rounded-10",
                    active && "!uno-layer-z1-(bg-#8E4E36/4 b-#8E4E36)"
                  )}
                  onClick={() => handleSelectShop(item)} // 调用 handleSelectShop
                >
                  <View className="flex items-center">
                    <View className="flex-1 min-w-0 text-(30 #333) fw-bold line-clamp-1">
                      {item.name || "门店名称"}
                    </View>
                    {/* 恢复原始图标 */}
                    <View className="ml-20">
                      <UnoIcon className="i-icon-park-outline:send-one size-30 c-#333"></UnoIcon>
                    </View>
                  </View>
                  <View className="flex">
                    {" "}
                    {/* 移除 items-center */}
                    <View className="flex-1 min-w-0 text-(26 #666) line-clamp-1">
                      {item.fullAddress || "门店地址"}
                    </View>
                    {dis && <View className="text-(26 #666) ml-20">{dis}</View>}
                  </View>

                  {/* 门诊费显示逻辑 */}
                  {item.outpatientFee != null && item.outpatientFee > 0 && (
                    <View className="flex b-t-(1 solid #eee) pt-10 mt-10">
                      <View className="flex-1 min-w-0 text-(26 #666) line-clamp-1">
                        门诊费
                      </View>
                      <View className="text-(26 #8E4E36) ml-20">
                        &yen;{item.outpatientFee}
                      </View>
                    </View>
                  )}
                </View>
              );
            })}
          </WeScroll>
        </View>
      )}

      {!!state.doctors.length && (
        <View>
          <View className="text-(28 #333) fw-bold pt-40 pb-30">选择医生</View>

          <WeScroll>
            {state.doctors.map((item) => {
              const active = state.doctor?.id == item.id;

              return (
                <View
                  key={item.id}
                  className={clsx(
                    "ws-normal inline-flex items-center box-border b-(2 solid #f2f2f2) px-20 w-400 h-120 bg-#f2f2f2 ml-30 first:ml-0 rounded-10",
                    active && "!uno-layer-z1-(bg-#8E4E36/4 b-#8E4E36)"
                  )}
                  onClick={() => handleSelectDoctor(item)} // 调用 handleSelectDoctor
                >
                  <Image
                    className="size-80 rounded-full overflow-hidden bg-#fff mr-20"
                    mode="aspectFit"
                    src={item.photo}
                  ></Image>
                  <View className="flex-1 min-w-0">
                    <View className="text-(30 #333) line-clamp-1">
                      {item.name}
                    </View>
                    <View className="text-(26 #8E4E36) line-clamp-1">
                      服务费: &yen;{item.fee}
                    </View>
                  </View>
                </View>
              );
            })}
          </WeScroll>
        </View>
      )}
    </View>
  );
};
