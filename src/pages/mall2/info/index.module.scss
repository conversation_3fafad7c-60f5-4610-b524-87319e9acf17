.page {
  padding: 0 30px;
}

.banner {
  margin: 0 -30px;
  height: 1080px;
  position: relative;
  overflow: hidden;

  .swiper,
  .item,
  .img {
    width: 100%;
    height: 100%;
  }

  .img {
    display: block;
  }

  .dots {
    position: absolute;
    bottom: 150px;
    left: 50%;
    display: flex;
    transform: translate(-50%, 0);

    .dot {
      margin: 0 10px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      &.on {
        background: rgba(255, 255, 255, 1);
      }
    }
  }
}

.pane {
  position: relative;
  z-index: 10;
  background: #fff;
  border-radius: 30px;
  // margin-top: -120px;
  margin-top: 30px;
  padding: 0 30px;
  margin-bottom: 30px;

  .tit {
    font-size: 58px;
    // font-weight: bold;
    padding: 60px 0 20px;
  }

  .info {
    display: flex;
    padding-bottom: 20px;
    .left {
      flex: 1;
      min-width: 0;
    }
    .price {
      display: flex;
      align-items: center;
      font-weight: bold;
      .num {
        font-size: 58px;
        &::first-letter {
          font-size: 30px;
          margin-right: 4px;
        }
      }
      .tag {
        margin-left: 25px;
        width: 115px;
        height: 43px;
      }
    }
    .old {
      margin-top: 8px;
      font-size: 36px;
      color: #aaa;
      text-decoration: line-through;
    }
    .stock {
      margin-top: auto;
      font-size: 30px;
      color: #aaa;
    }
  }

  .foot {
    border-top: 1px solid #eee;
    display: flex;
    flex-flow: row wrap;
    align-items: center;

    .tag {
      display: flex;
      align-items: center;
      padding: 30px 30px 30px 0;
    }
    .ico {
      width: 36px;
      height: 36px;
      margin-right: 10px;
    }
    .txt {
      font-size: 34px;
    }
  }

  .list {
    padding: 10px 0;
    .item {
      display: flex;
      line-height: 1.3;
      padding: 13px 0;

      .dot {
        font-size: 34px;
        color: #000;
        width: 30px;
      }
      .name {
        flex: 1;
        min-width: 0;
        font-size: 34px;
        color: #000;

        // overflow: hidden;
        // display: -webkit-box;
        // -webkit-box-orient: vertical;
        // -webkit-line-clamp: 1;
        // line-clamp: 1;
      }
      .price1,
      .price2 {
        font-size: 34px;
        color: #000;
        margin-left: 15px;
      }
      .price2 {
        color: #aaa;
        text-decoration: line-through;
      }
    }
  }
}

.tipbar {
  border-radius: 30px;
  background: #fff;
  align-items: center;
  padding: 55px 46px;
  display: flex;

  .btn {
    border: 1px solid #d5b47c;
    border-radius: 10px;
    font-size: 36px;
    color: #d5b47c;
    padding: 10px;
  }
  .txt {
    padding-left: 20px;
    min-width: 0;
    flex: 1;
    font-size: 36px;
  }
}

.mtitle {
  height: 124px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 39px;
}

.box1 {
  background: #fff;
  .img {
    display: block;
    width: 100%;
    height: auto;
  }
}

.footer {
  &Holder {
    padding-top: 30px;
    height: 213px;
  }

  z-index: 100;
  box-sizing: border-box;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 213px;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 50px;

  .menu {
    position: relative;
    padding: 0 35px;
    .ico {
      display: block;
      width: 65px;
      height: 65px;
      margin: 0 auto;
    }
    .txt {
      margin-top: 5px;
      font-size: 25px;
      color: #494949;
      text-align: center;
    }
    .hide {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
    }
  }

  .btn {
    margin-left: auto;
    display: flex;
    border: 1px solid #ad7f6d;
    width: 580px;
    height: 112px;
    border-radius: 61px;
    overflow: hidden;

    .item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 41px;
      color: #ad7f6d;
      &:last-child {
        background: #ad7f6d;
        color: #fff;
      }
    }
  }
}

.fixbtn {
  z-index: 99;
  position: fixed;
  right: 55px;
  bottom: 330px;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  // background: #ad7f6d;
  background: #ad7f6d;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .hide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
  }

  .ico {
    width: 38px;
    height: 44px;
  }
  .txt {
    margin-top: 10px;
    font-size: 25px;
    color: #fff;
  }
}

.cart {
  padding: 0 50px;
  .head {
    display: flex;
    align-items: center;
    padding: 40px 0 20px;
    .ico {
      width: 60px;
      height: 60px;
      margin-left: auto;
    }
  }
  .body {
    display: flex;
    .cover {
      width: 180px;
      height: 180px;
      background: #f5f5f5;
      border-radius: 20px;
      margin-right: 50px;
    }
    .cont {
      min-width: 0;
      flex: 1;
      display: flex;
      flex-flow: column;
      justify-content: center;
      .tit {
        font-size: 45px;
        font-weight: bold;
      }
      .price {
        margin-top: 30px;
        font-size: 48px;
        font-weight: bold;
        &::first-letter {
          font-size: 33px;
        }
      }
    }
  }
  .numbar {
    display: flex;
    align-items: center;
    padding: 70px 0;
    .label {
      font-size: 39px;
      font-weight: bold;
    }
    .counter {
      margin-left: auto;
      // width: 200px;
      // height: 56px;
      // background: #f5f5f5;
    }
  }
  .desc {
    display: flex;
    align-items: center;
    .dot {
      font-size: 40px;
      color: #d90000;
    }
    .txt {
      font-size: 39px;
      font-weight: bold;
    }
    .sm {
      margin-left: 10px;
      font-size: 29px;
      color: #999;
    }
    .extra {
      margin-left: auto;
      display: flex;
      align-items: center;
      .em {
        font-size: 32px;
        color: #ad7f6d;
      }
    }
  }
  .btns {
    margin: 65px 0;
    display: flex;
    height: 114px;
    line-height: 114px;
    border-radius: 60px;
    border: 1px solid #ad7f6d;
    color: #ad7f6d;
    font-size: 41px;
    text-align: center;
    overflow: hidden;
    .btn {
      flex: 1;

      &:last-child {
        background: #ad7f6d;
        color: #fff;
      }
    }
  }
}
