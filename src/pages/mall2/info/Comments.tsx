import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { Rate } from "@taroify/core";
import { Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useRequest } from "ahooks";
import dayjs from "dayjs";

export default (props: { pid: string }) => {
  const listRes = useRequest(CusApi.getOrderComment, {
    defaultParams: [{ productId: props.pid }],
  });
  const list = (listRes.data?.list || []).slice(0, 2);
  const str2arr = (s) => (s || "").split(",").filter((n) => n);
  const empty = !list.length;

  return (
    <View className="my-30 bg-#fff rounded-20 px-30">
      <View className="flex items-center h-88 b-b-(1 solid #eee)">
        <View className="text-(28 #000) flex-1">用户评价</View>
        {!empty && (
          <View
            className="flex items-center h-full pl-20"
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/mall2/orderCommentList/index?id=${props.pid}`,
              });
            }}
          >
            <View className="text-(22 #aaa)">查看全部评价</View>
            <UnoIcon className="i-icon-park-outline:right text-(28 #aaa) ml-8" />
          </View>
        )}
      </View>
      <View>
        {empty && (
          <View className="py-40 text-(22 #aaa)">
            暂无评价，期待您的首次评价!
          </View>
        )}
        {list.map((item) => {
          const imgs = str2arr(item.evaluationImage);

          return (
            <View className="b-t-(1 solid #eee) first:(b-0)" key={item.id}>
              <View className="flex items-center py-30">
                <Image
                  className="size-56 mr-15 bg-#f5f5f5 rounded-full overflow-hidden"
                  mode="aspectFill"
                  src={item.appUser?.photo}
                ></Image>
                <View className="flex-1 min-w-0">
                  <View className="text-(24 #000)">{item.appUser?.name}</View>
                  <View className="text-(22 #aaa) mt-4">
                    {dayjs(item.evaluationDate).format("YYYY-MM-DD")}
                  </View>
                </View>

                <View>
                  <Rate
                    value={item.evaluationScore}
                    readonly
                    style={
                      {
                        "--rate-icon-size": "25rpx",
                        "--rate-icon-gutter": "8rpx",
                        "--rate-icon-full-color": "#8E4E36",
                      } as any
                    }
                  />
                </View>
              </View>
              {!!item.evaluationDesc && (
                <View className="text-(28 #000) pb-25">
                  {item.evaluationDesc}
                </View>
              )}
              {!!imgs.length && (
                <View className="flex pb-25">
                  {imgs.map((src, idx) => (
                    <Image
                      key={idx}
                      className="size-100 bg-#f5f5f5 rounded-10 overflow-hidden mr-20"
                      mode="aspectFill"
                      src={src}
                      onClick={() => {
                        Taro.previewImage({
                          current: src,
                          urls: imgs,
                        });
                      }}
                    />
                  ))}
                </View>
              )}
            </View>
          );
        })}
      </View>
    </View>
  );
};
