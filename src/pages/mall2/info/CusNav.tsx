import { UnoIcon } from "@/components/UnoIcon";
import { View } from "@tarojs/components";
import Taro, { usePageScroll } from "@tarojs/taro";
import { useSetState } from "ahooks";
import clsx from "clsx";
// import clsx from "clsx";

const sys = Taro.getSystemInfoSync();
const rect = Taro.getMenuButtonBoundingClientRect();
const sh = sys.statusBarHeight || 0;
const px = sys.windowWidth - rect.right;

export default function CusNav() {
  const [state, setState] = useSetState({ hide: true });

  usePageScroll((e) => {
    setState({ hide: e.scrollTop < 200 });
  });

  return (
    <View
      className={clsx(
        "pos-fixed top-0 left-0 w-100vw z-100 transition-300",
        state.hide ? "bg-#fff/0" : "bg-#fff"
      )}
    >
      <View style={{ height: sh }}></View>
      <View
        style={{
          height: rect.height,
          paddingTop: rect.top - sh,
          paddingBottom: rect.top - sh,
          paddingLeft: px,
          paddingRight: rect.width + px * 2,
        }}
        className="pos-relative"
      >
        <View className="pos-relative z-2 flex">
          <View
            className="rounded-full bg-#fff/80 flex items-center justify-center box-border p-2 overflow-hidden"
            style={{ height: rect.height - 5, width: rect.height - 5 }}
            onClick={() => {
              Taro.navigateBack({
                fail: () => Taro.reLaunch({ url: `/pages/tabs/mall/index` }),
              });
            }}
          >
            <UnoIcon className="i-icon-park-outline:left bg-#666 size-full" />
          </View>
        </View>

        <View
          className={clsx(
            "pos-absolute top-0 left-0 right-0 bottom-0 m-auto z-1 flex items-center justify-center text-(36 #000) opacity-0 max-w-300 transition-300",
            state.hide ? "opacity-0" : "opacity-100"
          )}
        >
          <View className="line-clamp-1">商品详情</View>
        </View>
      </View>
    </View>
  );
}
