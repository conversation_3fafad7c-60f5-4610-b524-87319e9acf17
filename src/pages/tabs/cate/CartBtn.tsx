import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useSetState } from "ahooks";

export const CartBtn = () => {
  const [state, setState] = useSetState({
    num: 0,
  });

  useDidShow(async () => {
    const token = Taro.getStorageSync("token");
    if (!token) return;

    const res: any[] = await CusApi.getCart();
    const num = (res || []).reduce((p, c) => p + c.totalCount, 0);
    setState({ num });
  });

  return (
    <View className="z-50 pos-fixed bottom-0 right-30">
      <View
        className="pos-relative size-80 bg-#AD7F6D rounded-full flex items-center justify-center mb-30"
        onClick={() => {
          Taro.navigateTo({ url: `/pages/mall2/cart/index` });
        }}
      >
        <UnoIcon className="i-icon-park-outline:shopping size-38 text-#fff" />

        <View className="pos-absolute -top-5 -right-5 bg-red-5 size-34 rounded-full text-(20 #fff) flex items-center justify-center">
          {state.num > 99 ? 99 : state.num}
        </View>
      </View>
    </View>
  );
};
