import CusApi from "@/services/CusApi";
import { makeApi } from "@/utils/request";
import { str2arr } from "@/utils/tools";
import { Image, Text, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
// import { useSetState } from "ahooks";

export const getList = makeApi(
  "get",
  `/api/v2/mallProduct/query_category_product_list`
);

export const GoodsItem = (props: { item: any }) => {
  // const [state, setState] = useSetState({ open: false });

  const item = props.item;
  const labels = str2arr(item.labelName);
  // const labels = str2arr("中分子,痛感低,痛感低,痛感低");
  const parts = str2arr(item.partName).join("、");

  return (
    <View
      className="flex py-20"
      onClick={() =>
        Taro.navigateTo({ url: `/pages/mall2/info/index?id=${item.id}` })
      }
    >
      <Image
        className="size-180 bg-#aaa mr-20 rounded-6"
        mode="scaleToFill"
        src={item.coverImage + `?x-oss-process=image/resize,w_400,limit_0`}
      />
      <View className="flex-1 flex flex-col pt-5">
        <View className="flex items-center">
          <View className="text-(26 #111) line-clamp-1">{item.name}</View>
        </View>
        {!!labels?.length && (
          <View className="flex gap-8 flex-wrap mt-15">
            {labels.map((n, i) => {
              const colors = ["#985e4a", "#a38958"];
              const bgs = ["#f7f4ef", "#eee7df"];

              const c = colors[i % colors.length];
              const b = bgs[i % bgs.length];

              return (
                <View
                  key={n}
                  className="h-28 text-(22/28 #985e4a) bg-#f7f4ef px-8 rounded-4 overflow-hidden"
                  style={{ backgroundColor: b, color: c }}
                >
                  {n}
                </View>
              );
            })}
          </View>
        )}
        {!!parts?.length && (
          <View className="text-(22 #888) mt-15 break-all">
            推荐{parts}等部位
          </View>
        )}
        <View className="flex items-center mt-20">
          <Text className="text-(20 #8E4E36)">&yen;</Text>
          <Text className="text-(28 #8E4E36) fw-bold mx-8">
            {item.priceSensitiveTag ? "???" : item.salePrice}
            {/* {item.salePrice} */}
          </Text>
          {item.productSpecificationNum > 1 && (
            <Text className="text-(20 #8E4E36)">起</Text>
          )}
          <Text className="text-(20 #999) ml-20">已售 {item.soldCount}</Text>
        </View>
      </View>
    </View>
  );
};

export const fetchData = async (property = 2) => {
  const [res1, res2] = await Promise.all([
    CusApi.getCates({ property }),
    CusApi.getGoods({ property, pageSize: 9999 }),
  ]);
  const clist: any[] = res1?.list || [];
  const glist: any[] = res2?.list || [];
  const cmap: { [key: string]: any } = {};
  const ctree: any[] = [];

  // 生成分类字典 方便快速查询
  clist.forEach((item) => {
    item.children = [];
    item.goods = [];
    cmap[item.id] = item;
  });

  // 生成分类树结构
  clist.forEach((item) => {
    if (item.parentId) {
      cmap[item.parentId]?.children.push(item);
    }
  });

  // 把商品插入分类树中
  glist.forEach((item) => {
    const cid = item.shopCategoryId;
    cmap[cid]?.goods.push(item);
  });

  // 剔除无效分类，清除分类上下级关系
  clist.forEach((item) => {
    item.children = [];
    if (item.level > 2 || !item.goods?.length || item.mode == 2) {
      delete cmap[item.id];
    }
  });

  // 重新生成分类树
  Object.values(cmap).forEach((item) => {
    if (item.parentId) {
      cmap[item.parentId]?.children.push(item);
    } else {
      ctree.push(item);
    }
  });

  return ctree;
};
