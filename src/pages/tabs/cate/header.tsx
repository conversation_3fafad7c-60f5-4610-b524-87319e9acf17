import { CusSwiper } from "@/components/Custom/CusSwiper";
import { UnoIcon } from "@/components/UnoIcon";
import { fetchShopsByLocation } from "@/utils/tools";
import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";

export const Header = () => {
  const [state, setState] = useSetState({
    shop: null as any,

    ht: 48,
    hh: 32,
  });

  useMount(() => {
    const rect = Taro.getMenuButtonBoundingClientRect();

    const ht = rect.top;
    const hh = rect.height;

    setState({ ht, hh });
  });

  useMount(async () => {
    try {
      const pos = await Taro.getLocation({ type: "gcj02" });
      Taro.setStorageSync("pos", pos);
    } catch (e) {}

    const shops = await fetchShopsByLocation();
    const shop = shops?.[0];
    setState({ shop });
  });

  return (
    <View>
      <View style={{ paddingTop: state.ht }} className="pl-30 pr-300">
        <View
          style={{ height: state.hh }}
          className="bg-#f5f5f5 rounded-full flex items-center pl-55"
          onClick={() => Taro.navigateTo({ url: `/pages/mall2/search/index` })}
        >
          <UnoIcon className="i-icon-park-outline:search size-28 bg-#666 mr-10" />
          <View className="text-(28 #666)">搜项目</View>
        </View>
      </View>

      <View className="pt-35 pb-25 px-30">
        <View
          className="flex items-center"
          onClick={() =>
            Taro.navigateTo({ url: `/pages/mall3/shop-map/index` })
          }
        >
          <View className="text-(30 #303030) fw-bold line-clamp-1">
            {state.shop?.name || "??"}
          </View>
          <UnoIcon className="i-icon-park-outline:right size-28 bg-#303030 ml-6 flex-shrink-0" />
        </View>
        <View className="flex items-center mt-20">
          <UnoIcon className="i-icon-park-outline:local-two size-24 bg-#939393 mr-6" />
          <View className="text-(24 #939393)">
            距您{state.shop?._dis || "??"}
          </View>
        </View>
      </View>

      <View className="h-a mx-30 rounded-10 overflow-hidden bg-#fafafa">
        <CusSwiper pos={7} hideOnEmpty className="h-90" />
      </View>
    </View>
  );
};
