import { sleep } from "@/utils/tools";
import { View, ScrollView } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useSetState } from "ahooks";
import clsx from "clsx";
import { getList, GoodsItem } from "./unit";
import { Header } from "./header";
import { CartBtn } from "./CartBtn";

export default function Page() {
  const [state, setState] = useSetState({
    list: [] as any[],
    lv1: null as any,
    lv2: null as any,
    sid: "",
    observers: [] as any[], // 用于存储创建的Observer实例
  });

  // 添加useDidShow钩子，每次页面显示时都会执行
  useDidShow(() => {
    fetchData();
  });

  const fetchData = async () => {
    // 先销毁之前创建的所有Observer
    destroyObservers();

    const res = await getList({ property: 2 });
    const list = res?.list || [];
    setState({ list });

    await sleep(10);
    init();
  };

  // 销毁所有Observer的函数
  const destroyObservers = () => {
    state.observers.forEach((observer) => {
      if (observer && typeof observer.disconnect === "function") {
        observer.disconnect();
      }
    });
    setState({ observers: [] });
  };

  const init = () => {
    const pages = Taro.getCurrentPages();
    const page = pages[pages.length - 1];

    // 创建并保存Observer实例
    const observer1 = Taro.createIntersectionObserver(page, {
      observeAll: true,
    })
      .relativeTo(".box-lv1")
      .observe(".node-lv1", (res: any) => {
        const id = (res?.id || "").split("-")?.[1] || "";
        if (res.intersectionRatio <= 0) return;
        setState({ lv1: id });
      });

    const observer2 = Taro.createIntersectionObserver(page, {
      observeAll: true,
    })
      .relativeTo(".box-lv2")
      .observe(".node-lv2", (res: any) => {
        const id = (res?.id || "").split("-")?.[1] || "";

        if (res.intersectionRatio > 0) {
          setState({ lv2: id });
        } else {
          setState((p) => ({
            lv2: p.lv2 == id ? null : p.lv2,
          }));
        }
      });

    // 将创建的Observer实例保存到state中
    setState((prev) => ({
      observers: [...prev.observers, observer1, observer2],
    }));
  };

  return (
    <View className="h-100vh bg-#fff flex flex-col">
      <Header />

      <View className="flex-1 min-h-0 flex mt-20">
        <View className="w-160 bg-#f7f7f7">
          <View className="grid cols-1">
            {state.list.map((item) => (
              <View
                key={item.id}
                className={clsx(
                  "group h-100 flex items-center px-10 b-l-(6 solid #fff)",
                  state.lv1 == item.id &&
                    "uno-layer-z1-(bg-#fff b-l-#8E4E36) ![&_.title]:text-#8E4E36"
                )}
                onClick={async () => {
                  setState({ sid: "" });
                  await sleep(1);
                  setState({ sid: "sid-" + item.id });
                }}
              >
                <View className="flex-1 min-w-0 title text-(26/40 #333 center) fw-bold">
                  <View className="line-clamp-2">{item.name}</View>
                </View>
              </View>
            ))}
          </View>
        </View>
        <View className="pos-relative flex-1 min-w-0 mx-20 bg-#fff overflow-hidden node-box">
          <View className="pos-absolute top-0 h-2 w-full pointer-events-none box-lv1" />
          <View className="pos-absolute top-170 h-2 w-full pointer-events-none box-lv2" />
          <ScrollView
            scrollY
            className="h-full"
            enhanced
            showScrollbar={false}
            scrollIntoView={state.sid}
            // @ts-ignore
            scroll-into-view-offset={2}
          >
            <View className="px-20">
              {state.list.map((lv1) => (
                <View
                  key={lv1.id}
                  className="pos-relative node-lv1"
                  id={"lv1-" + lv1.id}
                >
                  <View id={"sid-" + lv1.id} />
                  <View className="pos-sticky top-0 z-20 bg-#fff">
                    <View className="h-90 text-30/90 #111) fw-bold">
                      {lv1.name}
                    </View>

                    <ScrollView
                      scrollX
                      enhanced
                      showScrollbar={false}
                      scrollIntoView={"c2-" + state.lv2}
                      scrollWithAnimation={true}
                      // @ts-ignore
                      scroll-into-view-offset={-50}
                    >
                      <View className="ws-nowrap text-0 pb-20">
                        {lv1.childList.map((lv2) => (
                          <View
                            className={clsx(
                              "pos-relative inline-block mr-20 px-30 h-56 text-(20/56 #999) bg-#f7f7f7 rounded-6",
                              state.lv2 == lv2.id &&
                                "uno-layer-z1-(bg-#333 text-#fff)"
                            )}
                            onClick={async () => {
                              setState({ sid: "" });
                              await sleep(1);
                              setState({ sid: "sid-" + lv2.id });
                            }}
                            id={`c2-` + lv2.id}
                          >
                            {/* <View
                              id={`c2-` + lv2.id}
                              className="pos-absolute top-0 -left-100 size-0"
                            /> */}
                            {lv2.name}&nbsp;&nbsp;({lv2.productList?.length})
                          </View>
                        ))}
                      </View>
                    </ScrollView>
                  </View>

                  <View className="">
                    <View className="">
                      {lv1.productList?.map((item) => (
                        <GoodsItem key={item.id} item={item} />
                      ))}
                    </View>

                    {lv1.childList.map((lv2) => {
                      return (
                        <View
                          className="pos-relative node-lv2"
                          key={lv2.id}
                          id={"lv2-" + lv2.id}
                        >
                          <View
                            className="pos-absolute left-0 -top-160 size-2 pointer-events-none"
                            id={"sid-" + lv2.id}
                          />
                          <View className="text-(22 #111) pt-15">
                            {lv2.name} ({lv2.productList?.length})
                          </View>
                          {lv2.productList.map((item) => (
                            <GoodsItem key={item.id} item={item} />
                          ))}
                        </View>
                      );
                    })}
                  </View>
                </View>
              ))}
            </View>

            <View className="h-500"></View>
          </ScrollView>
        </View>
      </View>

      <CartBtn />
    </View>
  );
}
