import { MasonryList } from "@/components/Masonry/List";
import CusApi from "@/services/CusApi";
import { getUserByToken } from "@/services/UserApi";
import { img_avatar } from "@/utils/images";
import {
  Image,
  ScrollView,
  Swiper,
  SwiperItem,
  View,
} from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";
import { useMemo } from "react";

const type = 10;

export default function Page() {
  const [state, setState] = useSetState({
    sh: 0,
    bh: 32,
    cates: [] as any[],
    cate: null as any,

    user: null as any,
    userVip: null as any,
  });

  const cateIdx = useMemo(() => {
    const idx = state.cates.findIndex((n) => n.id == state.cate?.id);
    return Math.max(0, idx);
  }, [state.cates, state.cate]);

  useMount(() => {
    const win = Taro.getWindowInfo();
    const rect = Taro.getMenuButtonBoundingClientRect();
    const sh = win.statusBarHeight || 0;
    const bh = (rect.top - sh) * 2 + rect.height;
    setState({ sh, bh });
  });

  useMount(() => {
    getCates();

    if (Taro.getStorageSync("token")) {
      fetchUser();
    }
  });

  const getCates = async () => {
    const res = await CusApi.getNoteCates({
      pageSize: 9999,
      type,
    });
    let cates = res?.list || [];

    cates = [{ id: "recommendTag", name: "推荐" }, ...cates];

    const cate = cates?.[0] || null;
    setState({ cates, cate });
  };

  const fetchUser = async () => {
    const user = await getUserByToken();
    // 获取用户VIP信息
    if (user) {
      const userVip = await CusApi.getUserInfo();
      setState({ user, userVip });
    } else {
      setState({ user });
    }
  };

  return (
    <View className="h-100vh overflow-hidden flex flex-col bg-#F5F1EE">
      <View className="bg-#fff b-b-(1 solid #f5f5f5)">
        <View style={{ height: state.sh }}></View>
        <View
          className="flex items-center px-30 pos-relative"
          style={{ height: state.bh }}
        >
          {/* <Image
            className="rounded-full overflow-hidden bg-#eee size-84"
            src={state.user?.photo || img_avatar}
            onClick={() => {
              Taro.navigateTo({ url: `/pages/user/user2/index` });
            }}
          /> */}
          <View className="pos-absolute top-1/2 left-1/2 -translate-1/2 text-(37 #010101)">
            搞美星球
          </View>
        </View>

        <View className="h-110 overflow-hidden">
          <ScrollView
            scrollX
            className="h-200 ws-nowrap fz-0"
            scrollIntoView={"cid-" + state.cate?.id}
            scrollWithAnimation={true}
          >
            {state.cates.map((item) => (
              <View
                className={clsx(
                  "pos-relative text-(28 #1A1A1A) px-35 py-45 inline-block",
                  state.cate?.id == item.id && "!text-#8E4E36"
                )}
                key={item.id}
                onClick={() => setState({ cate: item })}
              >
                <View>{item.name}</View>
                <View
                  className={clsx(
                    "pos-absolute left-1/2 top-85 w-25 h-5 bg-#8E4E36 -translate-x-1/2",
                    state.cate?.id != item.id && "!invisible"
                  )}
                />
                <View
                  className="pos-absolute top-0 -left-200 size-0"
                  id={`cid-` + item.id}
                />
              </View>
            ))}
          </ScrollView>
        </View>
      </View>

      <View className="flex-1 min-h-0">
        <Swiper
          className="h-full"
          current={cateIdx}
          onChange={(e) => {
            const idx = e.detail.current;
            const cate = state.cates[idx];
            setState({ cate });
          }}
        >
          {state.cates.map((item) => (
            <SwiperItem className="h-full" key={"sid-" + item.id}>
              <ScrollView className="h-full" scrollY>
                <View className="flex flex-col h-full">
                  <MasonryList type={type} cate={item} />
                </View>
              </ScrollView>
            </SwiperItem>
          ))}
        </Swiper>
      </View>

      {/* 只有当用户不是NomalUser时才显示悬浮按钮 */}
      {state.userVip && state.userVip.vipLevelCode !== "NomalUser" && (
        <View className="pos-fixed right-40 bottom-60">
          <View
            className="mt-35 pos-relative size-100 rounded-full b-(1 solid #8E4E36) flex items-center justify-center"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/user/user2/index` });
            }}
          >
            <Image
              mode="aspectFill"
              className="size-68 bg-#fff rounded-full"
              src={state.user?.photo || img_avatar}
            />
            <View className="pos-absolute -bottom-10 -left-5 w-110 h-30 rounded-full bg-#8E4E36 text-(20/30 #fff center)">
              我的主页
            </View>
          </View>
          {/* <View className="mt-35 pos-relative size-100 rounded-full b-(1 solid #8E4E36) flex items-center justify-center">
            <Image
              mode="aspectFill"
              className="size-68 bg-#fff rounded-full"
              src="https://oss.gaomei168.com/file-release/20250418/1362738695521075200_188_188.png"
            />
            <View className="pos-absolute -bottom-10 -left-5 w-110 h-30 rounded-full bg-#8E4E36 text-(20/30 #fff center)">
              发布内容
            </View>
          </View> */}
        </View>
      )}
    </View>
  );
}
