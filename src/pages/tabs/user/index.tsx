import CusApi from "@/services/CusApi";
import { getUserByToken } from "@/services/UserApi";
import { img_avatar, img_share } from "@/utils/images";
import { fdate, jumpAfterAdminShopChose } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useSetState } from "ahooks";
import { OrderMenu } from "./OrderMenu";
import { UnoIcon } from "@/components/UnoIcon";
import { useShare } from "@/stores/useShare";
import { UserSetting } from "./UserSetting";
import { useMemo } from "react";

export default function UserPage() {
  const [state, setState] = useSetState({
    isLogin: false,
    user1: null as any,
    user2: null as any,
    coupon: null as any,

    admin: null as any,

    serviceNum: 0,

    openU: false,
  });

  useShare(() => {
    return {
      title: "搞美医疗数字诊所",
      imageUrl: img_share,
      path: `/pages/tabs/mall/index`,
    };
  });

  useDidShow(() => {
    const isLogin = !!Taro.getStorageSync("token");
    setState({ isLogin });
    if (!isLogin) return;

    fetchUser();
    fetchCoupon();
    fetchAdmin();
    fetchServiceNum();
  });

  const fetchAdmin = async () => {
    const res = await CusApi.getAdminAuth();
    // console.log(res);
    setState({ admin: res });
  };

  const fetchUser = async () => {
    const [user1, user2] = await Promise.all([
      getUserByToken(),
      CusApi.getUserInfo(),
    ]);
    setState({ user1, user2 });

    // bindWx(user1);
  };

  const fetchCoupon = async () => {
    const res = await CusApi.getCoupon({ state: 10 });
    setState({ coupon: res });
  };

  const fetchServiceNum = async () => {
    const res = await CusApi.getServiceStat();
    setState({ serviceNum: res?.evaluationState1Num || 0 });
    // console.log(res);
  };

  const authJump = (url) => {
    if (!state.isLogin) {
      Taro.navigateTo({ url: `/pages/mall/login/index` });
    } else {
      Taro.navigateTo({ url });
    }
  };

  const bindWx = (user) => {
    if (user?.bindWxTag) return;
    Taro.showModal({
      title: "绑定公众号",
      content: "需要绑定搞美公众号",
      showCancel: false,
      confirmText: "立即前往",
      success: (res) => {
        if (res.confirm) {
          const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx02a88cc3fe35597d&redirect_uri=${encodeURIComponent(
            process.env.TARO_APP_API + `/api/wxApi/test_bind_openid`
          )}&response_type=code&scope=snsapi_base&state=${
            user.id
          }&connect_redirect=1#wechat_redirect`;
          Taro.navigateTo({
            url: `/pages/mall/webview/index?url=${encodeURIComponent(url)}`,
          });
        }
      },
    });
  };

  const vip = useMemo(() => {
    if (!state.user2) return null;

    const config = {
      NomalUser: {
        card: `https://oss.gaomei168.com/file-release/20250304/1346524489409368064_1392_614.png`,
        color: "#11100F",
        pointer: `https://oss.gaomei168.com/file-release/20250304/1346542700360110080_750_114.png`,
        bg: "#FFE6D2",
        color2: `linear-gradient(180deg,#F7E2D6,#FFFAF4)`,
      },
      VipUser: {
        card: `https://oss.gaomei168.com/file-release/20250304/1346524489400979456_1392_614.png`,
        color: "#CFB676",
        pointer: `https://oss.gaomei168.com/file-release/20250304/1346542700389470208_750_114.png`,
        bg: "#F5E6D1",
        color2: `linear-gradient(180deg,#F9E6BC,#FBF2E3)`,
      },
    };

    const code = state.user2.vipLevelCode;
    const name = state.user2.vipLevelName;

    const cfg = config[code == "NomalUser" ? "NomalUser" : "VipUser"];
    let date = state.user2.foreverTag
      ? "永久有效"
      : fdate(state.user2.expireDate, "YYYY-MM-DD") + "到期";
    date = code === "NomalUser" ? `立即解锁VIP会员 享更多权益` : date;
    const canJump = code == "NomalUser" || code == "VipUser";

    return { code, name, date, canJump, ...cfg };
  }, [state.user2]);

  return (
    <View
      className="min-h-100vh bg-#F7F4EF"
      // style={{
      //   backgroundImage:
      //     vip?.color2 || `linear-gradient(180deg,#FFE6D2,#F7F4EF 1200rpx)`,

      // }}
      style={{
        backgroundImage: `linear-gradient(180deg,${
          vip?.bg || "#FFE6D2"
        },#F7F4EF 550rpx)`,
      }}
    >
      <View style={{ height: "84px" }}></View>

      <View
        className="flex items-center px-40 pb-35"
        onClick={() => authJump("/pages/user/profile/index")}
      >
        <View className="size-120 p-2 bg-#fff rounded-full">
          <Image
            className="block size-full bg-#fff rounded-full"
            mode="aspectFill"
            src={state.user1?.photo || img_avatar}
          />
        </View>

        <View className="flex-1 min-w-0 px-25">
          {state.isLogin ? (
            <>
              <View className="text-(36 #111) fw-bold">
                {state.user1?.name}
              </View>
            </>
          ) : (
            <>
              <View className="text-(36 #111) fw-bold">hello!</View>
              <View className="text-(24 #595856) mt-10">
                登录享受更多惊喜服务
              </View>
            </>
          )}
        </View>
        {!state.isLogin && (
          <View className="w-160 h-76 bg-#fff rounded-full text-(24/76 #111 center) fw-bold">
            登录/注册
          </View>
        )}
      </View>

      {/* <View
        className="flex items-center pb-15 px-40"
        onClick={() => authJump("/pages/user/profile/index")}
      >
        <Image
          className="size-84 bg-#f5f5f5 rounded-full overflow-hidden mr-20"
          mode="aspectFill"
          src={state.user1?.photo || img_avatar}
        />
        <View className="text-(26 #11100F) fw-bold">
          {state.isLogin ? state.user1?.name : "点击登录"}
        </View>
        {state.isLogin && (
          <View
            className="ml-10"
            onClick={(e) => {
              e.stopPropagation();
              setState({ openU: true });
            }}
          >
            <UnoIcon className="i-icon-park-outline:edit size-28 text-#11100F" />
          </View>
        )}
      </View> */}

      {!!vip && (
        <View
          className="pos-relative mx-35"
          onClick={async () => {
            try {
              await Taro.requestSubscribeMessage({
                tmplIds: [
                  "2TF7S1V76kGdssG8hdAe7K2PuZku5OAX19itR3p7Ya4", // 服务评价提醒
                  "g3H57pxzVL6DJ5EwehjuvVkNhDJ1nPU8UXrlNFRD1zk", // 等级提醒
                  "KXC45BSOrvMEEDfUCA4Ai-CJLn6eBd9PGEe-ElkhYDM", // 会员账户变动提醒
                ],
              } as any);
            } catch (e) {}

            if ([10, 11].includes(state.admin?.promotionRole)) {
              authJump("/pages/mall/fxhome/index");
            } else if (vip.canJump) {
              authJump("/pages/user/vip/index");
            }
          }}
        >
          <Image
            className="block w-full h-300"
            mode="scaleToFill"
            src={vip.card}
          />
          <View className="pos-absolute top-17 left-0 w-146 h-46 text-(22/46 #fff center)">
            当前等级
          </View>
          <View
            className="pos-absolute top-80 left-30 text-(36 #11100F)"
            style={{ color: vip.color }}
          >
            {vip.name}
          </View>
          <View
            className="pos-absolute bottom-45 left-0 w-full flex items-center box-border px-30 text-#11100F"
            style={{ color: vip.color }}
          >
            <View className="text-24 flex-1">{vip.date}</View>

            {[10, 11].includes(state.admin?.promotionRole) && (
              <View className="text-22">推广中心&gt;</View>
            )}

            {vip.canJump && <View className="text-22">会员中心&gt;</View>}
          </View>
        </View>
      )}

      <View className="mx-40 py-30 flex gap-16">
        <View
          className="flex-1 h-136 rounded-20 bg-[linear-gradient(180deg,#F7E2D6,#FFFAF4)] flex flex-col items-center justify-center"
          style={{ backgroundImage: vip?.color2 }}
          onClick={() => authJump("/pages/mall/gmb/index")}
        >
          <View className="text-(30 #8F4F36) fw-bold">
            {state.user2?.coin ?? "--"}
          </View>
          <View className="text-(24 #666) mt-5">M币</View>
        </View>

        <View
          className="flex-1 h-136 rounded-20 bg-[linear-gradient(180deg,#F7E2D6,#FFFAF4)] flex flex-col items-center justify-center"
          style={{ backgroundImage: vip?.color2 }}
          onClick={() => authJump("/pages/user/coupon/index")}
        >
          <View className="text-(30 #8F4F36) fw-bold">
            {state.coupon?.total ?? "--"}
          </View>
          <View className="text-(24 #666) mt-5">卡券</View>
        </View>

        <View
          className="flex-1 h-136 rounded-20 bg-[linear-gradient(180deg,#F7E2D6,#FFFAF4)] flex flex-col items-center justify-center"
          style={{ backgroundImage: vip?.color2 }}
          onClick={() => authJump("/pages/mall/wallet/index")}
        >
          <View className="text-(30 #8F4F36) fw-bold">
            {state.user2?.balance ?? "--"}
          </View>
          <View className="text-(24 #666) mt-5">余额</View>
        </View>
      </View>

      <View className="mx-40">
        <Image
          className="block h-160 w-full rounded-20 overflow-hidden mb-30"
          mode="aspectFill"
          src="https://oss.gaomei168.com/file-release/20250306/1347154730460909568_681_160.png"
          onClick={() => authJump("/pages/user/invite/index")}
        />
      </View>

      <OrderMenu />

      <View className="mx-35 mb-30 py-30 bg-#fff rounded-25 grid cols-4">
        <View
          className="h-140 flex flex-col items-center justify-center"
          onClick={async () => {
            try {
              await Taro.requestSubscribeMessage({
                tmplIds: ["2TF7S1V76kGdssG8hdAe7K2PuZku5OAX19itR3p7Ya4"], // 服务评价提醒
              } as any);
            } catch (e) {}

            authJump("/pages/user/orderlog2/index");
          }}
        >
          <View className="pos-relative">
            <UnoIcon className="i-icon-park-outline:stethoscope text-(40 #8E4E36)"></UnoIcon>
            {!!state.serviceNum && (
              <View className="pos-absolute -top-26 left-1/2 -translate-x-1/2 bg-red-400 px-5 ws-nowrap flex items-center justify-center text-(20 #fff) rounded-full">
                评价有礼
              </View>
            )}
          </View>
          <View className="text-(21 #11100F) fw-500 mt-20">治疗记录</View>
        </View>

        <View
          className="h-140 flex flex-col items-center justify-center"
          onClick={() => authJump("/pages/mall2/projectList/index")}
        >
          <UnoIcon className="i-icon-park-outline:list-checkbox text-(40 #8E4E36)"></UnoIcon>
          <View className="text-(21 #11100F) fw-500 mt-20">我的项目</View>
        </View>

        <View
          className="h-140 flex flex-col items-center justify-center"
          onClick={() => authJump("/pages/user/presenter/index")}
        >
          <UnoIcon className="i-icon-park-outline:good-one text-(40 #8E4E36)"></UnoIcon>
          <View className="text-(21 #11100F) fw-500 mt-20">我的推荐</View>
        </View>

        <View
          className="pos-relative h-140 flex flex-col items-center justify-center"
          onClick={() => {
            Taro.openCustomerServiceChat({
              extInfo: {
                url: `https://work.weixin.qq.com/kfid/kfcc1793a8a8dbee924`,
              },
              corpId: "ww6ff0ba55cb7300f4",
              success: () => console.log("open service succ"),
            });
          }}
        >
          <UnoIcon className="i-icon-park-outline:headset-one text-(40 #8E4E36)"></UnoIcon>
          <View className="text-(21 #11100F) fw-500 mt-20">专属客服</View>
        </View>

        <View
          className="pos-relative h-140 flex flex-col items-center justify-center"
          onClick={() => Taro.navigateTo({ url: `/pages/mall2/mailbox/index` })}
        >
          <UnoIcon className="i-icon-park-outline:send-email text-(40 #8E4E36)"></UnoIcon>

          <View className="text-(21 #11100F) fw-500 mt-20">小小建议</View>
        </View>
      </View>

      <View className="mx-35 mb-30 py-30 bg-#fff rounded-25 grid cols-4 empty:hidden">
        {!!state.admin?.reservationManage && (
          <View
            className="h-140 flex flex-col items-center justify-center"
            onClick={() => authJump("/pages/mall/dueList2/index")}
          >
            <UnoIcon className="i-icon-park-outline:calendar text-(40 #8E4E36)"></UnoIcon>
            <View className="text-(21 #11100F) fw-500 mt-20">预约管理</View>
          </View>
        )}

        {!!state.admin?.orderVerify && (
          <View
            className="h-140 flex flex-col items-center justify-center"
            onClick={async () => {
              jumpAfterAdminShopChose("/pages/mall/verifyOrder/index");
            }}
          >
            <UnoIcon className="i-icon-park-outline:box text-(40 #8E4E36)"></UnoIcon>
            <View className="text-(21 #11100F) fw-500 mt-20">订单核销</View>
          </View>
        )}

        {!!state.user1?.isShopInvestor && (
          <View
            className="h-140 flex flex-col items-center justify-center"
            onClick={() => authJump("/pages/user/InvestorShop/index")}
          >
            <UnoIcon className="i-icon-park-outline:application text-(40 #8E4E36)"></UnoIcon>
            <View className="text-(21 #11100F) fw-500 mt-20">门店收益</View>
          </View>
        )}

        {/* {[10, 11].includes(state.admin?.promotionRole) && (
          <View
            className="h-140 flex flex-col items-center justify-center"
            onClick={() => authJump("/pages/mall/fxhome/index")}
          >
            <UnoIcon className="i-icon-park-outline:consume text-(40 #8E4E36)"></UnoIcon>
            <View className="text-(21 #11100F) fw-500 mt-20">推广</View>
          </View>
        )} */}
      </View>

      <View className="h-30"></View>

      <UserSetting
        open={state.openU}
        onOK={() => {
          fetchUser();
          setState({ openU: false });
        }}
      />
    </View>
  );
}
