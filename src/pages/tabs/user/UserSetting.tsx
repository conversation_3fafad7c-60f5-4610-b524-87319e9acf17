import { PopUp, PopUpTop } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import { getUserByToken, updateUser } from "@/services/UserApi";
import { img_avatar } from "@/utils/images";
import { Button, Image, Input, View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useSetState } from "ahooks";

export const UserSetting = (props: { open?: boolean; onOK?: any }) => {
  const [state, setState] = useSetState({
    open2: false,
    // user: null as any,
    _nick: "",
    nick: "",
    avatar: "",
  });

  useDidShow(() => {
    const isLogin = !!Taro.getStorageSync("token");
    if (!isLogin) return;
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await getUserByToken();
    setState({
      avatar: user?.photo,
      nick: user?.name,
    });
  };

  const setAvatar = async (e) => {
    e.stopPropagation();
    const path = e.detail.avatarUrl;
    Taro.showLoading({ title: "上传中..." });
    const res = await Taro.uploadFile({
      url: process.env.TARO_APP_UPLOAD || "",
      filePath: path,
      name: "file",
      header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
    });
    Taro.hideLoading();
    const data = JSON.parse(res.data);
    const url = data?.data?.allFullPath || "";
    setState({ avatar: url });
  };

  const onSave = async () => {
    const data = { photo: state.avatar, name: state.nick };
    await updateUser(data);
    props.onOK?.();
  };

  return (
    <View>
      <PopUp open={props.open} onClose={props.onOK}>
        <View className="bg-#fff" style={{ borderRadius: "20rpx 20rpx 0 0" }}>
          <View className="px-30 py-30 text-(26 #333) flex">
            <View>昵称头像设置</View>
            <View className="ml-a" onClick={props.onOK}>
              <UnoIcon className="i-icon-park-outline:close text-(30 #666) " />
            </View>
          </View>

          <View className="flex px-30 items-center py-20 pos-relative">
            <View className="flex-1 text-(26 #666)">头像</View>
            <View className="size-80 bg-#f5f5f5 rounded-full overflow-hidden">
              <Image
                className="size-full"
                mode="aspectFill"
                src={state.avatar || img_avatar}
              />
            </View>
            <UnoIcon className="i-icon-park-outline:right text-(30 #666)" />
            <Button
              openType="chooseAvatar"
              className="pos-absolute top-0 left-0 size-full opacity-0"
              onChooseAvatar={setAvatar}
            ></Button>
          </View>

          <View
            className="flex px-30 items-center py-20"
            onClick={() => setState({ open2: true, _nick: state.nick })}
          >
            <View className="flex-1 text-(26 #666)">昵称</View>
            <View className="text-(26 #333)">{state.nick}</View>
            <UnoIcon className="i-icon-park-outline:right text-(30 #666)" />
          </View>

          <View className="py-30 px-30">
            <View
              className="h-80 rounded-full bg-#8E4E36 text-(30/80 #fff center)"
              onClick={onSave}
            >
              保存
            </View>
            <View
              className="h-80 text-(30/80 #666 center)"
              onClick={props.onOK}
            >
              取消
            </View>
          </View>
        </View>
      </PopUp>

      <PopUpTop open={state.open2} onClose={() => setState({ open2: false })}>
        <View className="bg-#fff px-20 pt-200">
          <View className="h-90 flex items-center">
            <Input
              className="flex-1 text-(30 #333)"
              type="nickname"
              placeholder="请输入"
              value={state._nick}
              onInput={(e) => setState({ _nick: e.detail.value })}
            />
            <View
              className="ml-20 w-140 h-60 bg-#AD7F6D rounded-10 text-(26/60 #fff center)"
              onClick={() => setState({ nick: state._nick, open2: false })}
            >
              确定
            </View>
          </View>
        </View>
      </PopUpTop>
    </View>
  );
};
1;
