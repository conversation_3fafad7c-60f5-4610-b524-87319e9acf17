import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useRequest } from "ahooks";

// prettier-ignore
const menu = [
  {
    id: 1,
    name: "待付款",
    icon: <UnoIcon className="i-icon-park-outline:wallet text-(40 #8E4E36)" />,
    keys: ["state0Num"],
  },
  {
    id: 2,
    name: "可使用",
    icon: <UnoIcon className="i-icon-park-outline:commodity text-(40 #8E4E36)" />,
    keys: ["state10Num", "state20Num", "state30Num"],
  },
  {
    id: 3, 
    name: "退款/售后",
    icon: <UnoIcon className="i-icon-park-outline:credit text-(40 #8E4E36)" />,
    keys: ["state70Num"],
  },
  {
    id: 0,
    name: "全部",
    icon: <UnoIcon className="i-icon-park-outline:transaction-order text-(40 #8E4E36)" />,
    keys: [],
  },
];

export const OrderMenu = () => {
  const req = useRequest(
    async () => {
      const res = await CusApi.getOrderCount({});
      const data: any = {};
      menu.forEach((m) => {
        const v = m.keys.reduce((p, c) => p + res[c], 0);
        data[m.id] = v;
      });
      return data;
    },
    { manual: true }
  );

  useDidShow(() => {
    const token = Taro.getStorageSync("token");
    if (token) req.run();
  });

  return (
    <View>
      <View className="mx-35 mb-30 py-50 bg-#fff rounded-25 grid cols-4">
        {menu.map((item) => (
          <View
            className="flex flex-col items-center"
            key={item.id}
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/mall2/orderList/index?id=${item.id}`,
              });
            }}
          >
            <View className="pos-relative">
              {item.icon}
              {!!req.data?.[item.id] && (
                <View className="pos-absolute -top-10 -right-20 bg-red size-30 flex items-center justify-center text-(20 #fff) rounded-full">
                  {req.data?.[item.id]}
                </View>
              )}
            </View>
            <View className="text-(21 #11100F) fw-500 mt-20">{item.name}</View>
          </View>
        ))}
      </View>
    </View>
  );
};
