import {
  <PERSON>,
  ScrollView,
  Swiper,
  SwiperI<PERSON>,
  View,
} from "@tarojs/components";
import Taro, { usePullDownRefresh } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { sleep } from "@/utils/tools";
import { useShare } from "@/stores/useShare";
import { img_share } from "@/utils/images";
import { AdBox } from "./AdBox";
import { CusSwiper } from "@/components/Custom/CusSwiper";
import { Splash } from "./splash";
import clsx from "clsx";

const imgarr = [
  "https://oss.gaomei168.com/file-release/20250711/1393170454487166976_1080_1836.jpg",
  "https://oss.gaomei168.com/file-release/20250711/1393170454516527104_1080_1836.jpg",
  "https://oss.gaomei168.com/file-release/20250711/1393170454558470144_1080_1836.jpg",
  "https://oss.gaomei168.com/file-release/20250711/1393170454575247360_1080_1836.jpg",
  "https://oss.gaomei168.com/file-release/20250711/1393170454596218880_1080_1836.jpg",
  "https://oss.gaomei168.com/file-release/20250711/1393170454612996096_1080_1836.jpg",
];

export default function Home() {
  const [state, setState] = useSetState({
    update: 0,

    bidx: 0,

    adOk: null,

    swiperIn: false,
  });

  useShare(() => {
    return {
      title: "搞美医疗数字诊所",
      imageUrl: img_share,
      path: `/pages/tabs/mall/index`,
    };
  });

  usePullDownRefresh(async () => {
    setState({ update: state.update + 1 });
    await sleep(1000);
    Taro.stopPullDownRefresh();
  });

  useMount(async () => {
    const pages = Taro.getCurrentPages();
    const page = pages[pages.length - 1];

    Taro.createIntersectionObserver(page, {
      thresholds: [0.5],
    })
      .relativeToViewport()
      .observe(".swiper-box", (res: any) => {
        const isIn = res.intersectionRatio >= 0.5;
        setState({ swiperIn: isIn });
      });
  });

  const authJump = (url, needLogin = true) => {
    const isLogin = !!Taro.getStorageSync("token");
    if (needLogin && !isLogin) {
      Taro.navigateTo({ url: `/pages/mall/login/index` });
    } else {
      Taro.navigateTo({ url });
    }
  };

  return (
    <View className="bg-#f6f6f6 min-h-100vh">
      <View
        className="z-30 pos-fixed top-90 left-30 w-176 h-60 bg-#fff/80 rounded-6 flex items-center justify-center"
        onClick={() => {
          Taro.navigateTo({ url: `/pages/user/butler/index` });
        }}
      >
        <Image
          className="size-40"
          mode="aspectFit"
          src="https://oss.gaomei168.com/file-release/20250227/1344628050466611200.png"
        />
        <View className="text-(24 #11100F) ml-10">联系管家</View>
      </View>

      <CusSwiper pos={1} className="[&_.dots]:bottom-50 !h-710" />

      <View className="z-10 pos-relative mx-30 -mt-30 h-195 flex items-center bg-no-repeat bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250226/1344359264199024640.png)]">
        <View
          className="flex-1 h-full flex flex-col items-center justify-center"
          onClick={() => authJump("/pages/mall2/goodDoctor/index", false)}
        >
          <Image
            className="size-48"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250311/1348954084662317056_200_200.png"
          />
          <View className="text-(20 #11100F) mt-15">搞美医生</View>
        </View>
        <View className="h-90 w-1 bg-#E5E5E5"></View>

        <View
          className="flex-1 h-full flex flex-col items-center justify-center"
          onClick={() => authJump("/pages/mall2/noteroot50/index", false)}
        >
          <Image
            className="size-48"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250226/1344360217883090944.png"
          />
          <View className="text-(20 #11100F) mt-15">医美科普</View>
        </View>
        <View className="h-90 w-1 bg-#E5E5E5"></View>
        <View
          className="flex-1 h-full flex flex-col items-center justify-center"
          onClick={() => authJump("/pages/mall2/noteroot40/index", false)}
        >
          <Image
            className="size-48"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250226/1344360217815982080.png"
          />
          <View className="text-(20 #11100F) mt-15">术后须知</View>
        </View>
        <View className="h-90 w-1 bg-#E5E5E5"></View>
        <View
          className="flex-1 h-full flex flex-col items-center justify-center"
          onClick={() => authJump("/pages/mall2/due/index", false)}
        >
          <Image
            className="size-48"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250228/1345084487236325376_200_200.png"
          />
          <View className="text-(20 #11100F) mt-15">咨询预约</View>
        </View>
      </View>

      <CusSwiper
        pos={6}
        hideOnEmpty
        className="h-210 w-a mx-30 mt-30 rounded-20 overflow-hidden"
      />

      <View className="h-420 overflow-hidden mx-30 mt-30">
        <ScrollView className="h-500" scrollX>
          <View className="ws-nowrap">
            <View
              className="w-258 h-420 inline-flex flex-col items-center justify-center mr-20 last:mr-0 bg-no-repeat bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250226/1344364275146596352.png)]"
              onClick={() => authJump("/pages/user/invite/index")}
            >
              <Image
                className="size-146"
                mode="aspectFit"
                src="https://oss.gaomei168.com/file-release/20250226/1344367622788390912.png"
              />
              <View className="text-(32 #11100F) fw-bold mt-32">美力搭子</View>
              <View className="text-(20 #868485) mt-10">Beauty Partner</View>
              <View className="w-150 h-60 rounded-full bg-#fff text-(26/60 #11100F center) fw-bold mt-45">
                开启计划
              </View>
            </View>

            <View
              className="w-258 h-420 inline-flex flex-col items-center justify-center mr-20 last:mr-0 bg-no-repeat bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250226/1344364275146596352.png)]"
              onClick={() => authJump("/pages/mall2/cardCollect/index")}
            >
              <Image
                className="size-146"
                mode="aspectFit"
                src="https://oss.gaomei168.com/file-release/20250226/1344367622796779520.png"
              />
              <View className="text-(32 #11100F) fw-bold mt-32">趣味活动</View>
              <View className="text-(20 #868485) mt-10">Fun Activities</View>
              <View className="w-150 h-60 rounded-full bg-#fff text-(26/60 #11100F center) fw-bold mt-45">
                来玩吧
              </View>
            </View>

            <View
              className="w-258 h-420 inline-flex flex-col items-center justify-center mr-20 last:mr-0 bg-no-repeat bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250226/1344364275146596352.png)]"
              onClick={() => authJump("/pages/mall2/sign/index")}
            >
              <Image
                className="size-146"
                mode="aspectFit"
                src="https://oss.gaomei168.com/file-release/20250226/1344367622947774464.png"
              />
              <View className="text-(32 #11100F) fw-bold mt-32">每日签到</View>
              <View className="text-(20 #868485) mt-10">Daily Check</View>
              <View className="w-150 h-60 rounded-full bg-#fff text-(26/60 #11100F center) fw-bold mt-45">
                去签到
              </View>
            </View>
          </View>
        </ScrollView>
      </View>

      <View className="flex gap-20 mx-30 mt-30">
        <View
          className="pos-relative box-border pl-25 pt-30 flex-1 h-175 bg-no-repeat bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250226/1344370096148488192.png)]"
          onClick={() => authJump("/pages/user/newUser/index")}
        >
          <View className="text-(30 #11100F) fw-bold">新人专区</View>
          <View className="text-(20 #11100F) mt-10">好物专享</View>
          <Image
            className="size-30 pos-absolute right-30 bottom-20"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250227/1344612541293568000.png"
          />
        </View>
        <View
          className="pos-relative box-border pl-25 pt-30 flex-1 h-175 bg-no-repeat bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250226/1344370096165265408.png)]"
          onClick={() => authJump("/pages/mall2/mmall/index", false)}
        >
          <View className="text-(30 #11100F) fw-bold">M币商城</View>
          <View className="text-(20 #11100F) mt-10">礼品兑换</View>
          <Image
            className="size-30 pos-absolute right-30 bottom-20"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250227/1344612541293568000.png"
          />
        </View>
      </View>

      {/* <Image
        className="block w-full h-a"
        mode="widthFix"
        src="https://oss.gaomei168.com/file-release/20250312/1349445831181631488_1080_4609.jpg"
      /> */}

      <View className="pos-relative h-1275 bg-#eee swiper-box">
        <Swiper
          className="size-full"
          current={state.bidx}
          onChange={(e) => setState({ bidx: e.detail.current })}
          autoplay={state.swiperIn}
          circular
          interval={3000}
        >
          {imgarr.map((url, idx) => (
            <SwiperItem key={idx} className="size-full">
              <Image className="size-full" mode="aspectFill" src={url} />
            </SwiperItem>
          ))}
        </Swiper>
        <View className="pos-absolute left-40 bottom-40 flex">
          {imgarr.map((_, idx) => {
            const ison = state.bidx == idx;

            return (
              <View
                key={idx}
                className={clsx("w-30 h-6 bg-#fff", ison && "!bg-brand")}
              ></View>
            );
          })}
        </View>
      </View>

      <View className="h-30"></View>

      {state.adOk === false ? (
        <Splash />
      ) : (
        <AdBox onOK={(e) => setState({ adOk: e })} />
      )}
    </View>
  );
}
