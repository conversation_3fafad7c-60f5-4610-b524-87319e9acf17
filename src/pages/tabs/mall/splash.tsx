import { Modal } from "@/components/Custom/PopUp2";
import CusApi from "@/services/CusApi";
import { Image, Swiper, SwiperItem, View } from "@tarojs/components";
import { useMount, useSetState } from "ahooks";
import { UnoIcon } from "@/components/UnoIcon";
import Taro from "@tarojs/taro";
import dayjs from "dayjs";
import { CheckBox } from "@/components/CheckBox";

export const Splash = () => {
  const [state, setState] = useSetState({
    open: false,
    imgs: [] as any[],
    ck: false,
  });

  useMount(async () => {
    const date = Taro.getStorageSync("splash");
    if (date && dayjs().isBefore(date)) return;

    const res = await CusApi.getAdv({ showPosition: 5 });
    const list = res?.list || [];
    if (!list?.length) return;

    setState({ open: true, imgs: list });
  });

  const onClose = () => {
    setState({ open: false });

    if (state.ck) {
      Taro.setStorageSync("splash", dayjs().add(3, "day").valueOf());
      // Taro.setStorageSync("splash", dayjs().add(1, "minute").valueOf());
    }
  };

  const onAdClick = (item: any) => {
    const val: string = item.jumpKey || "";
    const val2 = encodeURIComponent(val);

    if (item.jumpType == 1) {
      if (val.startsWith("/pages/")) {
        Taro.navigateTo({ url: val });
      } else {
        Taro.navigateTo({ url: `/pages/mall/webview/index?url=${val2}` });
      }
    } else if (item.jumpType == 3) {
      Taro.navigateTo({ url: `/pages/mall/rich/index?html=${val2}` });
    } else if (item.jumpType == 4) {
      Taro.navigateTo({ url: `/pages/mall2/info/index?id=${val}` });
    }
  };

  return (
    <Modal open={state.open}>
      <View className="w-560">
        <View className="pos-relative pt-4/3">
          <View className="pos-absolute top-0 left-0 size-full rounded-10 overflow-hidden">
            <Swiper
              className="size-full"
              indicatorDots={true}
              indicatorActiveColor="#AE7F6D"
              autoplay
              circular
              interval={3500}
            >
              {state.imgs.map((item) => (
                <SwiperItem
                  className="size-full"
                  key={item.id}
                  onClick={() => onAdClick(item)}
                >
                  <Image
                    className="block size-full"
                    mode="aspectFill"
                    src={item.image}
                  ></Image>
                </SwiperItem>
              ))}
            </Swiper>
          </View>
        </View>
      </View>

      <View
        className="flex items-center justify-center py-30"
        onClick={() => setState({ ck: !state.ck })}
      >
        <CheckBox
          checked={state.ck}
          className="size-30 mr-5 text-gray-300"
        ></CheckBox>
        <View className="text-(26 gray-300)">三天内不打扰</View>
      </View>

      <View className="flex items-center justify-center">
        <View onClick={onClose}>
          <UnoIcon className="i-icon-park-outline:close-one size-60 bg-gray-300" />
        </View>
      </View>
    </Modal>
  );
};
