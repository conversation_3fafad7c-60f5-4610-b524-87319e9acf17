import { Modal } from "@/components/Custom/PopUp2";
import { makeApi } from "@/utils/request";
import { Image, View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useSetState, useUpdateEffect } from "ahooks";

const getMsg = makeApi("get", `/api/sysMessage/query_remind_list`);
const readMsg = makeApi("post", `/api/sysMessage/read`);

export const AdBox = (props: { onOK?: any }) => {
  const [state, setState] = useSetState({
    open: false,
    data: null as any,
  });

  useDidShow(() => {
    init();
  });

  useUpdateEffect(() => {
    props.onOK?.(!!state.data);
    return;
  }, [state.data]);

  const init = async () => {
    const token = Taro.getStorageSync("token");
    if (!token) return setState({ data: false });
    if (state.open) return setState({ data: false });

    const res = await getMsg();
    let data = res?.list?.[0];

    if (!data?.id) return setState({ data: false });
    readMsg({ id: data?.id });

    if (![21, 22].includes(data?.type) || data?.model !== 11) return;
    setState({ data, open: true });
  };

  const onTap1 = () => {
    const data = state.data;
    setState({ open: false });

    if (data?.jumpType == 1) {
      const url = data?.jumpKey || "";
      if (url.startsWith("/pages/tabs/")) {
        Taro.switchTab({ url });
      } else {
        Taro.navigateTo({ url });
      }
    }
  };

  const onTap2 = () => {
    setState({ open: false });
  };

  return (
    <Modal open={state.open}>
      <Image
        className="block size-690"
        mode="aspectFit"
        src={state.data?.content}
      />
      <View className="flex items-center justify-center gap-60 mt-90">
        <View
          className="w-280 h-74 rounded-full bg-#B7684A text-(34/74 #fff center)"
          style={{ boxShadow: "0rpx 5rpx 8rpx 0rpx rgba(0,0,0,0.2)" }}
          onClick={onTap1}
        >
          {state.data?.jumpButtonText}
        </View>
        <View
          className="w-280 h-74 rounded-full bg-#E0834B text-(34/74 #fff center)"
          style={{ boxShadow: "0rpx 5rpx 8rpx 0rpx rgba(0,0,0,0.2)" }}
          onClick={onTap2}
        >
          立即收下
        </View>
      </View>
    </Modal>
  );
};
