import { Text, View } from "@tarojs/components";
import Style from "./index.module.scss";
import Taro from "@tarojs/taro";

const CouponRes = () => {
  return (
    <View className={Style.page}>
      <View className={Style.ico}></View>
      <View className={Style.title}>兑换成功</View>
      <View className={Style.desc}>
        <Text>请到</Text>
        <Text
          className={Style.em}
          onClick={() => {
            Taro.redirectTo({ url: `/pages/user/coupon/index` });
          }}
        >
          我的-优惠券
        </Text>
        <Text>查看</Text>
      </View>
      <View
        className={Style.btn}
        onClick={() => {
          Taro.navigateBack();
        }}
      >
        返回
      </View>
    </View>
  );
};

export default CouponRes;
