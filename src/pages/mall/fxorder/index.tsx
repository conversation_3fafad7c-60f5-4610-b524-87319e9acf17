import { Image, View } from "@tarojs/components";
import { DateFilter, datelist } from "../fxuser/DateFilter";
import { useList } from "@/stores/useList";
import { makeApi } from "@/utils/request";
import { useSetState } from "ahooks";
import { useReachBottom, useRouter } from "@tarojs/taro";
import { img_avatar } from "@/utils/images";
import dayjs from "dayjs";
import { useEffect } from "react";

const getOrders = makeApi("get", `/api/promotionCenter/promotion_order_list`);
const getStat = makeApi("get", `/api/promotionCenter/promotion_order_total`);

const CState = [
  { id: 10, name: "未入账" },
  { id: 20, name: "已入账" },
];

export default function Page() {
  const router = useRouter();
  const datename = router.params.date || "全部";
  const [state, setState] = useSetState({
    start: datelist.find((n) => n.name == datename)?.start,
    end: datelist.find((n) => n.name == datename)?.end,
    stat: null as any,
  });

  const [list, listAct] = useList({
    api: getOrders,
    params: {
      startPayDate: state.start,
      endPayDate: state.end,
    },
  });

  useEffect(() => {
    listAct.getList();
    fetchStat();
  }, [state.end, state.start]);

  useReachBottom(() => {
    listAct.getNext();
  });

  const fetchStat = async () => {
    const res = await getStat({
      startPayDate: state.start,
      endPayDate: state.end,
    });
    setState({ stat: res });
  };

  return (
    <View>
      <View className="z-30 pos-sticky top-0 b-b-(1 solid #eee)">
        <DateFilter
          defaultActive={datename}
          onChange={(e) => setState({ start: e.start, end: e.end })}
        />
      </View>

      <View className="pos-relative h-200 overflow-hidden -mb-200">
        <View className="pos-absolute bottom-0 left-1/2 -translate-x-1/2 size-3000 rounded-full overflow-hidden">
          <View className="size-full bg-#8E4E36/50"></View>
        </View>
      </View>

      <View className="pos-relative mt-30 mx-30 bg-#fff rounded-20 h-260 flex items-center justify-center flex-col">
        <View className="text-(26 #666)">总贡献佣金（元）</View>
        <View className="text-(70 #8E4E36) fw-bold mt-10">
          {state.stat?.totalCommission ?? "--"}
        </View>
      </View>

      <View className="text-(24 #999) py-20 px-30">
        共
        <View className="text-#333 fw-bold mx-5 inline">
          {state.stat?.totalOrderNum ?? "--"}
        </View>
        笔订单
      </View>

      <View className="grid cols-1 gap-30">
        {list.list.map((item) => (
          <View className="bg-#fff px-30" key={item.id}>
            <View className="h-70 flex items-center b-b-(1 solid #eee)">
              <View className="text-(24 #333 )">订单号：{item.serialNo}</View>
              <View className="ml-a text-(24 #999)">
                {dayjs(item.payDate).format("YYYY-MM-DD HH:mm:ss")}
              </View>
            </View>
            <View className="flex items-center py-20">
              <Image
                className="size-100 bg-#f5f5f5 rounded-full overflow-hidden"
                mode="aspectFill"
                src={item.userPhoto || img_avatar}
              />
              <View className="flex-1 min-w-0 mx-20">
                <View className="text-(28 #333)">{item.userName}</View>
                <View className="text-(24 #666) mt-15">&nbsp;</View>
              </View>
              <View className="">
                <View className="text-(28 #333)">
                  {CState.find((n) => n.id == item.commissionState)?.name ||
                    "--"}
                </View>
                <View className="text-(24 #666) mt-15">
                  预估佣金：
                  <View className="inline-block text-red fw-bold">
                    &yen;{item.commission}
                  </View>
                </View>
              </View>
            </View>
          </View>
        ))}
      </View>

      <View className="text-(24 #999 center) py-30">
        {list.more ? "加载中..." : "没有更多数据了"}
      </View>
    </View>
  );
}
