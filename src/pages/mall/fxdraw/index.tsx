import { Image, Input, View } from "@tarojs/components";
import Style from "./index.module.scss";
import Taro, { useDidShow } from "@tarojs/taro";
import CusApi from "@/services/CusApi";
import { useSetState } from "ahooks";
import { showToast } from "@/utils/tools";

export default () => {
  const [state, setState] = useSetState({
    user: null as any,
    value: "" as any,
  });

  useDidShow(() => {
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await CusApi.getUserInfo();
    setState({ user });
  };

  const handleSubmit = async () => {
    if (!state.value) return showToast("请输入金额");
    await CusApi.addWalletDraw({
      source: 11,
      applyId: state.user?.id,
      applyMoney: state.value,
      moneyDestination: 2,
      wxAppId: process.env.TARO_APP_ID,
    });
    Taro.showModal({
      title: "申请提现成功",
      // content: "请等待管理员审核",
      showCancel: false,
      confirmText: "返回",
      success: (res) => {
        if (res.confirm) {
          Taro.navigateBack();
        }
      },
    });
  };

  return (
    <View className={Style.page}>
      <View className={Style.box}>
        <View className={Style.tit}>提现佣金</View>
        <View className={Style.num}>
          <Input
            className={Style.ipt}
            placeholder="请输入提现金额"
            value={state.value}
            onInput={(e) => setState({ value: e.detail.value })}
          />
        </View>
        <View className={Style.desc}>
          <View className={Style.txt}>
            当前可提现佣金{state.user?.cashCoin ?? "--"}
          </View>
          <View
            className={Style.em}
            onClick={() => {
              const all = state.user?.cashCoin || "";

              setState({ value: all });
            }}
          >
            全部提现
          </View>
        </View>
        <View className={Style.foot}>
          <Image
            className={Style.wx}
            src={require("@/assets/weixin.png")}
          ></Image>
          <View className={Style.txt}>提现到微信</View>
          <Image
            className={Style.ico}
            src={require("@/assets/ck-2.png")}
          ></Image>
        </View>
      </View>

      {/* <View className={Style.tip}></View> */}

      <View className={Style.footer}>
        <View className={Style.btn} onClick={handleSubmit}>
          立即提现
        </View>
      </View>
    </View>
  );
};
