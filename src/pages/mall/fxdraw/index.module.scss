.page {
  overflow: hidden;
  line-height: 1.3;
  padding-top: 25px;
}

.box {
  background: #fff;
  padding: 60px;

  .tit {
    font-size: 47px;
    padding-bottom: 20px;
  }

  .num {
    .ipt {
      display: block;
      height: 180px;
      line-height: 180px;
      font-size: 112px;
    }
  }

  .desc {
    display: flex;
    align-items: center;
    font-size: 42px;

    .em {
      margin: 0 20px;
      color: #ad7f6d;
      padding: 20px;
    }
  }

  .foot {
    display: flex;
    align-items: center;
    padding-top: 100px;

    .wx {
      width: 54px;
      height: 46px;
      margin-right: 15px;
    }

    .txt {
      font-size: 47px;
      color: #000;
    }

    .ico {
      margin-left: auto;
      width: 35px;
      height: 35px;
    }
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  padding: 35px 50px;
  background: #fff;

  .btn {
    height: 158px;
    line-height: 158px;
    text-align: center;
    font-size: 55px;
    color: #fff;
    background: #ad7f6d;
    border-radius: 20px;
  }
}
