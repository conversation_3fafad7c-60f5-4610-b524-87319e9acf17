import { PopUp } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import { Image, ScrollView, View } from "@tarojs/components";
import { useSetState, useUpdateEffect } from "ahooks";
import CusApi from "@/services/CusApi";
import clsx from "clsx";
import { useEffect } from "react";
import Taro from "@tarojs/taro";
import { formatDis } from "@/utils/tools";

const Tags = (props: {
  list: any[];
  value: any;
  onChange: any;
  render?: any;
}) => {
  return (
    <View className="overflow-hidden -ml-30 -mt-30">
      {props.list.map((item) => (
        <View
          key={item.id}
          className={clsx(
            "float-left ml-30 mt-30 b-(2 solid #f8f7fc) max-w-7/10 p-10 text-(26 #333) bg-#f8f7fc rounded-10",
            props.value == item.id &&
              "uno-layer-z1-(bg-#8E4E36/4 b-#8E4E36 text-#8E4E36)"
          )}
          onClick={() => props.onChange(item)}
        >
          <View className="line-clamp-1">
            {props.render ? props.render(item) : item.name}
          </View>
        </View>
      ))}
    </View>
  );
};

const WeScroll = (props: { children?: any }) => {
  return (
    <View className="overflow-hidden">
      <View className="-mb-30">
        <ScrollView scrollX enhanced showScrollbar={false}>
          <View className="ws-nowrap text-0">{props.children}</View>
          <View className="h-30"></View>
        </ScrollView>
      </View>
    </View>
  );
};

export const SkuPick = (props: {
  open?: boolean;
  onClose?: any;
  data: any;
  onChange?: any;
}) => {
  const [state, setState] = useSetState({
    num: 1,

    modes: [] as any[],
    mode: null as any,

    skus: [] as any[],
    sku: null as any,

    shops: [] as any[],
    shop: null as any,

    doctors: [] as any[],
    doctor: null as any,
  });

  useUpdateEffect(() => {
    props.onChange?.(state.sku);
  }, [state.sku]);

  useEffect(() => {
    const data = props.data?.product || {};

    const modes: any[] = [];
    if (data.offlineConsumeTag) {
      modes.push({ id: 1, name: "到店服务" });
    }
    if (data.onlineDeliveryTag) {
      modes.push({ id: 2, name: "送货到家" });
    }
    const mode = modes[0]?.id;

    const tmp = props.data?.productSpecification;
    const skus = tmp ? [tmp] : [];
    const sku = skus[0];

    setState({ modes, mode, skus, sku });
  }, [props.data]);

  useEffect(() => {
    getShops();
  }, [props.data?.id, state.mode, state.sku?.id]);

  useEffect(() => {
    getDoctors();
  }, [state.shop?.id, state.mode, state.sku?.id]);

  const numInc = () => {
    setState((p) => ({ num: p.num + 1 }));
  };

  const numDec = () => {
    setState((p) => ({ num: Math.max(1, p.num - 1) }));
  };

  const getShops = async () => {
    if (!(state.sku?.id && state.mode && props.data?.product?.id)) return;

    const pos = Taro.getStorageSync("pos");
    const res = await CusApi.getGoodsSkuShop({
      productId: props.data?.product?.id,
      model: state.mode,
      longitude: pos?.longitude,
      latitude: pos?.latitude,
      productSpecificationId: state.sku?.id,
    });
    const shops = res?.list || [];
    const shop = shops[0];
    setState({ shops, shop });
  };

  const getDoctors = async () => {
    if (!(state.mode && state.sku?.id && state.shop?.id)) return;

    const res = await CusApi.getGoodsSkuCalc({
      model: state.mode,
      productSpecificationId: state.sku?.id,
      shopId: state.shop?.id,
    });

    const list = res.serviceFeeList?.map((n) => {
      return {
        id: n.doctor?.id,
        name: n.doctor?.name ?? "--",
        photo: n.doctor?.photo ?? "",
        fee: n.serviceFee,
      };
    });

    const doctors = list || [];
    const doctor = doctors[0];
    setState({ doctors, doctor });
  };

  const toBuy = async () => {
    const data = {
      data: props.data,
      mode: state.mode,
      num: state.num,
      shop: state.shop,
      doctor: state.doctor,
    };

    props.onClose?.();
    Taro.preload(data);
    Taro.navigateTo({ url: `/pages/mall/fxpay/index` });
  };

  const data = props.data;
  if (!data?.id) return null;

  return (
    <PopUp open={props.open} onClose={props.onClose}>
      <View className="bg-#fff box-border">
        <View className="flex gap-20 pos-relative px-30 pt-30">
          <Image
            className="size-150 bg-#f5f5f5"
            mode="aspectFill"
            src={data.coverImage}
          />
          <View className="flex-1 min-w-0 flex flex-col py-4">
            <View className="text-(34 #8D451F) fw-bold first-letter:(text-25 mr-5)">
              &yen;{props.data?.salePrice}
            </View>

            <View className="text-(28 #333) fw-bold line-clamp-1 mt-10">
              {state.sku?.name}
            </View>

            <View className="inline-flex mt-a">
              <View
                className="size-40 bg-#f5f4f9 flex items-center justify-center"
                onClick={numDec}
              >
                <UnoIcon className="i-icon-park-outline:minus size-30 text-#515055" />
              </View>
              <View className="text-(24 #333) min-w-60 flex items-center justify-center">
                {state.num}
              </View>
              <View
                className="size-40 bg-#f5f4f9 flex items-center justify-center"
                onClick={numInc}
              >
                <UnoIcon className="i-icon-park-outline:plus size-30 text-#515055" />
              </View>
            </View>
          </View>
          <View onClick={props.onClose}>
            <UnoIcon className="i-icon-park-outline:close size-34 text-#666" />
          </View>
        </View>

        <ScrollView scrollY className="max-h-50vh">
          <View className="px-30">
            <View className="text-(28 #333) fw-bold pt-40 pb-30">服务类型</View>

            <Tags
              value={state.mode}
              list={state.modes}
              onChange={(e) => setState({ mode: e.id })}
            />

            <View className="text-(28 #333) fw-bold pt-40 pb-30">产品规格</View>

            <Tags
              value={state.sku?.id}
              list={state.skus}
              onChange={(e) => setState({ sku: e })}
            />

            {false && state.mode != 2 && (
              <>
                {!!state.shops?.length && (
                  <>
                    <View className="text-(28 #333) fw-bold pt-40 pb-30">
                      选择门店
                    </View>

                    <WeScroll>
                      {state.shops.map((item) => {
                        const dis = formatDis(item.distance);
                        const active = state.shop?.id == item.id;

                        return (
                          <View
                            key={item.id}
                            className={clsx(
                              "ws-normal inline-flex flex-col gap-10 justify-center box-border b-(2 solid #f2f2f2) px-25 py-20 w-450 bg-#f2f2f2 ml-30 first:ml-0 rounded-10",
                              active && "!uno-layer-z1-(bg-#8E4E36/4 b-#8E4E36)"
                            )}
                            onClick={() => setState({ shop: item })}
                          >
                            <View className="flex items-center">
                              <View className="flex-1 min-w-0 text-(30 #333) line-clamp-1">
                                {item.name}
                              </View>
                              <View className="ml-20">
                                <UnoIcon className="i-icon-park-outline:send-one size-30 c-#333"></UnoIcon>
                              </View>
                            </View>
                            <View className="flex">
                              <View className="flex-1 min-w-0 text-(26 #666) line-clamp-1">
                                {item.fullAddress}
                              </View>
                              <View className="text-(26 #666) ml-20">
                                {dis}
                              </View>
                            </View>

                            {!!item.outpatientFee && (
                              <View className="flex b-t-(1 solid #eee) pt-10 mt-10">
                                <View className="flex-1 min-w-0 text-(26 #666) line-clamp-1">
                                  门诊费
                                </View>
                                <View className="text-(26 #8E4E36) ml-20">
                                  &yen;{item.outpatientFee}
                                </View>
                              </View>
                            )}
                          </View>
                        );
                      })}
                    </WeScroll>
                  </>
                )}

                {!!state.doctors?.length && (
                  <>
                    <View className="text-(28 #333) fw-bold pt-40 pb-30">
                      选择医生
                    </View>

                    <WeScroll>
                      {state.doctors.map((item) => {
                        const active = state.doctor?.id == item.id;

                        return (
                          <View
                            key={item.id}
                            className={clsx(
                              "ws-normal inline-flex items-center box-border b-(2 solid #f2f2f2) px-20 w-400 h-120 bg-#f2f2f2 ml-30 first:ml-0 rounded-10",
                              active && "!uno-layer-z1-(bg-#8E4E36/4 b-#8E4E36)"
                            )}
                            onClick={() => setState({ doctor: item })}
                          >
                            <Image
                              className="size-80 rounded-full overflow-hidden bg-#fff mr-20"
                              mode="aspectFit"
                              src={item.photo}
                            ></Image>
                            <View className="flex-1 min-w-0">
                              <View className="text-(30 #333) line-clamp-1">
                                {item.name}
                              </View>
                              <View className="text-(26 #8E4E36) line-clamp-1">
                                服务费: &yen;{item.fee}
                              </View>
                            </View>
                          </View>
                        );
                      })}
                    </WeScroll>
                  </>
                )}
              </>
            )}
          </View>
          <View className="h-80"></View>
        </ScrollView>

        <View className="p-30 !pb-0">
          <View className="flex h-80 b-(1 solid #8E4E36) rounded-full overflow-hidden">
            <View
              className="flex-1 flex items-center justify-center text-(30 #fff) bg-#8E4E36"
              onClick={toBuy}
            >
              立即购买
            </View>
          </View>
          <View
            style={{
              height: `env(safe-area-inset-bottom)`,
              minHeight: "30rpx",
            }}
          />
        </View>
      </View>
    </PopUp>
  );
};
