import RichContent from "@/components/RichContent";
import CusApi from "@/services/CusApi";
import { Image, View } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { useShare } from "@/stores/useShare";
import GoodsShare from "../fxlist/share";

export default function Page() {
  const router = useRouter();

  const [state, setState] = useSetState({
    data: null as any,
    shareOpen: false,
  });

  useMount(() => {
    fetchData();
  });

  useShare(() => {
    return {
      title: state.data?.name,
      path: `/pages/mall/fxinfo/index?id=${router.params.id}`,
      imageUrl: state.data?.coverImage,
    };
  });

  const fetchData = async () => {
    const id = router.params.id;
    const res = await CusApi.getMarketProductInfo({ id });
    setState({ data: res });
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="pos-relative">
        <Image
          className="block w-full h-750 bg-#eee"
          mode="aspectFill"
          src={state.data?.coverImage}
        />
      </View>
      <View className="px-30">
        <View className="bg-#fff rounded-20 mt-20 p-30">
          <View className="flex items-center">
            <View className="flex items-center">
              <View className="text-(66 #000) fw-bold first-letter:(text-25 mr-5)">
                &yen;{state.data?.salePrice}
              </View>
              <View className="ml-10">
                <View className="text-(24 #333)">优惠价</View>
                <View className="text-(24 #999) line-through">
                  原价{state.data?.productSpecification?.salePrice}
                </View>
              </View>
            </View>
          </View>

          <View className="text-(34 #000) pb-30 pt-20 leading-normal">
            {state.data?.name}
          </View>
        </View>
      </View>

      <View className="text-(26 #111 center) py-40">项目详情</View>
      <View className="">
        <RichContent html={state.data?.content} />
      </View>

      <View className="h-160"></View>
      <View className="pos-fixed left-0 bottom-0 z-50 h-160 w-full bg-#fff box-border flex items-center">
        <View className="flex flex-col justify-center px-30">
          <View className="text-(24 #333)">
            佣金比例：{(state.data?.preCommissionRate * 100).toFixed(2)}%
          </View>
          <View className="text-(24 #333) mt-10">
            预估收益：&yen;{state.data?.preCommission}
          </View>
        </View>
        <View className="ml-a mr-30 rounded-full overflow-hidden flex h-80 b-(1 solid #8E4E36)">
          <View
            className="flex-1 w-300 text-(30/80 #fff center) bg-#8E4E36"
            onClick={() => setState({ shareOpen: true })}
          >
            立即推广
          </View>
        </View>
      </View>

      {!!state.data?.id && (
        <GoodsShare
          open={state.shareOpen}
          data={state.data}
          onOK={() => setState({ shareOpen: false })}
        />
      )}
    </View>
  );
}
