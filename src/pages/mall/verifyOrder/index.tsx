import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { Image, Input, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useRequest, useSetState } from "ahooks";

function Page() {
  const router = useRouter();
  const { data: shop } = useRequest(CusApi.getShopInfo, {
    defaultParams: [{ id: router.params.shopId }],
  });

  if (!shop) return null;

  return (
    <View className="min-h-lvh bg-#F7F4EF">
      <View className="p-30">
        <View className="bg-#FDFCFA rounded-10 px-25 mb-25">
          <View className="flex items-center py-30">
            <View className="text-(30 #1A1A1A) flex-1 fw-bold">当前门店</View>
          </View>

          <View className="flex pb-40 items-center">
            <Image
              className="size-96 bg-#eee rounded-full mr-30"
              mode="aspectFill"
              src={shop?.logo}
            />
            <View className="flex-1 min-w-0">
              <View className="text-(28 #1A1A1A)">{shop?.name}</View>
              <View className="text-(22 #636363) mt-5">
                {shop?.fullAddress}
              </View>
            </View>
          </View>
        </View>

        {/* <View className="bg-#FDFCFA rounded-10 p-25 mb-25">
          <Input
            className="flex-1 min-w-0 h-82 rounded-14 bg-#eee px-30 text-(25 #333)"
            placeholder="请输入客户姓名"
            // value={state.name}
            // onInput={(e) => setState({ name: e.detail.value })}
          />
        </View> */}

        <View
          className="mt-200 h-80 bg-#8E4E36 text-(30/80 #fff center) rounded-10 flex items-center justify-center"
          onClick={async () => {
            const { result } = await Taro.scanCode({
              scanType: ["qrCode"],
            });

            if (!result) return showToast("无效订单");

            const res = await CusApi.getVerifyOrder({
              shopId: shop?.id,
              verifyCode: result,
            });

            Taro.navigateTo({
              url: `/pages/mall/verifyOrderInfo/index?id=${result}&shopId=${router.params.shopId}`,
            });
          }}
        >
          <View>扫码核销</View>
        </View>
      </View>
    </View>
  );
}

export default function () {
  return <Page></Page>;
}
