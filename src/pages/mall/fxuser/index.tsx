import { Image, Text, View } from "@tarojs/components";
import { Tabs } from "@/components/Custom/Tabs";
import { useMount, useSetState } from "ahooks";
import { useList } from "@/stores/useList";
import { makeApi } from "@/utils/request";
import dayjs from "dayjs";
import { img_avatar } from "@/utils/images";
import { DateFilter, datelist } from "./DateFilter";
import { useEffect } from "react";
import { useReachBottom, useRouter } from "@tarojs/taro";
import CusApi from "@/services/CusApi";

const getList = makeApi("get", `/api/promotionCenter/promotion_user_list`);

export default function Page() {
  const router = useRouter();
  const datename = router.params.date || "全部";
  const [state, setState] = useSetState({
    tabs: [
      { id: "2", name: "直推" },
      { id: "3", name: "间推" },
      // { id: "1", name: "团队" },
    ],
    tabKey: "2",

    start: datelist.find((n) => n.name == datename)?.start,
    end: datelist.find((n) => n.name == datename)?.end,

    admin: null,
  });

  const [list, listAct] = useList({
    api: getList,
    size: 20,
    params: {
      promotionType: state.tabKey,
      startCreateDate: state.start,
      endCreateDate: state.end,
    },
  });

  useMount(() => {
    fetchAdmin();
  });

  useEffect(() => {
    listAct.getList();
  }, [state.start, state.end, state.tabKey]);

  useReachBottom(() => {
    listAct.getNext();
  });

  const fetchAdmin = async () => {
    const res = await CusApi.getAdminAuth();

    if (res.promotionRole == 10) {
      const tabs = [...state.tabs];
      tabs.push({ id: "1", name: "团队" });
      setState({ tabs });
    }
  };

  return (
    <View className="h-100vh">
      <View className="z-30 pos-sticky top-0 bg-#fff b-b-(1 solid #f5f5f5)">
        <DateFilter
          defaultActive={datename}
          onChange={(e) => setState({ start: e.start, end: e.end })}
        />
        <Tabs
          tabs={state.tabs}
          value={state.tabKey}
          onChange={(e) => setState({ tabKey: e })}
        />
      </View>
      <View className="grid cols-1 gap-20">
        {list.list.map((item) => (
          <View key={item.id} className="flex bg-#fff py-20 px-30">
            <Image
              className="size-100 bg-#f5f5f5 rounded-full"
              mode="aspectFill"
              src={item.userPhoto || img_avatar}
            />
            <View className="flex-1 min-w-0 mx-20 flex flex-col justify-center">
              <View className="text-(26 #333)">{item.userName}</View>
              <View className="text-(22 #999) mt-20">
                {dayjs(item.createDate).format("YYYY-MM-DD HH:mm:ss")}
              </View>
            </View>
            <View className="flex flex-col justify-center gap-5 text-right">
              <View className="text-(22 #333)">
                总贡献佣金
                <Text className="text-(26 red) mx-8">
                  {item.totalCommission}
                </Text>
                元
              </View>
              <View className="text-(22 #333)">
                待入账佣金
                <Text className="text-(26 red) mx-8">
                  {item.tobeEntryCommission}
                </Text>
                元
              </View>
            </View>
          </View>
        ))}

        <View className="text-(24 #999 center) py-20">
          {list.more ? "加载中..." : "没有更多数据了"}
        </View>
      </View>
    </View>
  );
}
