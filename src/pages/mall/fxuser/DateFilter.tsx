import { UnoIcon } from "@/components/UnoIcon";
import { showToast } from "@/utils/tools";
import { DatetimePicker } from "@taroify/core";
import { View } from "@tarojs/components";
import { useSetState } from "ahooks";
import clsx from "clsx";
import dayjs from "dayjs";

export const datelist = [
  { name: "全部", start: "", end: "" },
  {
    name: "本月",
    start: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
    end: dayjs().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
  },
  {
    name: "今日",
    start: dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
    end: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  },
  {
    name: "近7日",
    start: dayjs()
      .subtract(7, "day")
      .startOf("day")
      .format("YYYY-MM-DD HH:mm:ss"),
    end: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  },
];

export const DateFilter = (props: {
  onChange?: any;
  defaultActive?: string;
}) => {
  const [state, setState] = useSetState({
    active: props.defaultActive || "全部",

    open: false,
    type: 1,
    start: new Date(),
    end: new Date(),
  });

  return (
    <View className="pos-relative" catchMove>
      <View className="z-46 pos-relative flex items-center h-80 gap-20 px-30 bg-#fff">
        {datelist.map((item) => (
          <View
            key={item.name}
            className={clsx(
              "text-(22 #999 center) bg-#f5f5f5 px-20 py-8 rounded-full",
              item.name == state.active &&
                "uno-layer-z1-(bg-#8E4E36/10 text-#8E4E36)"
            )}
            onClick={() => {
              setState({ active: item.name, open: false });
              props.onChange?.(item);
            }}
          >
            {item.name}
          </View>
        ))}

        <View className="flex-1"></View>

        <View
          className="flex h-full items-center"
          onClick={() => setState({ open: !state.open })}
        >
          {state.active == "custom" ? (
            <View>
              <View className="text-(20 #8E4E36)">
                {dayjs(state.start).format("YYYY-MM-DD")}
              </View>
              <View className="text-(20 #8E4E36)">
                {dayjs(state.end).format("YYYY-MM-DD")}
              </View>
            </View>
          ) : (
            <View className="text-(22 #999 center)">自定义时间</View>
          )}

          <UnoIcon className="i-icon-park-outline:down text-(20 #999) ml-5"></UnoIcon>
        </View>
      </View>

      {state.open && (
        <>
          <View
            className="z-45 pos-fixed top-0 bottom-0 size-full bg-#000/70 "
            onClick={() => setState({ open: false })}
          ></View>

          <View className="z-50 pos-absolute top-1/1 left-0 w-full bg-#fff">
            <View className="flex items-center">
              <View
                className="flex-1 px-30"
                onClick={() => setState({ type: 1 })}
              >
                <View className="text-(20 #999) pb-5">开始时间</View>
                <View className="text-(40 #333)">
                  {state.start
                    ? dayjs(state.start).format("YYYY-MM-DD")
                    : "请选择"}
                </View>
                <View
                  className={clsx(
                    "h-2 bg-#8E4E36 mt-10",
                    state.type != 1 && "invisible"
                  )}
                ></View>
              </View>
              <View
                className="flex-1 px-30"
                onClick={() => setState({ type: 2 })}
              >
                <View className="text-(20 #999) pb-5">结束时间</View>
                <View className="text-(40 #333)">
                  {state.end ? dayjs(state.end).format("YYYY-MM-DD") : "请选择"}
                </View>
                <View
                  className={clsx(
                    "h-2 bg-#8E4E36 mt-10",
                    state.type != 2 && "invisible"
                  )}
                ></View>
              </View>
            </View>

            <DatetimePicker
              type="date"
              defaultValue={new Date()}
              value={state.type == 1 ? state.start : state.end}
              onChange={(e) => {
                if (state.type == 1) {
                  setState({ start: e });
                } else {
                  setState({ end: e });
                }
              }}
            >
              <DatetimePicker.Toolbar className="!hidden"></DatetimePicker.Toolbar>
            </DatetimePicker>

            <View
              className="h-80 bg-#8E4E36 text-(30/80 center #fff)"
              onClick={() => {
                if (!state.start) return showToast("请选择开始时间");
                if (!state.end) return showToast("请选择结束时间");

                if (dayjs(state.start).isAfter(state.end))
                  return showToast("开始时间必须小于结束时间");

                setState({ active: "custom", open: false });
                props.onChange?.({
                  start: dayjs(state.start)
                    .startOf("day")
                    .format("YYYY-MM-DD HH:mm:ss"),
                  end: dayjs(state.end)
                    .endOf("day")
                    .format("YYYY-MM-DD HH:mm:ss"),
                });
              }}
            >
              确定
            </View>
          </View>
        </>
      )}
    </View>
  );
};
