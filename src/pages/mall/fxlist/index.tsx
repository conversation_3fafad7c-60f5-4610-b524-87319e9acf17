import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { Image, View } from "@tarojs/components";
import { useMount, useSetState } from "ahooks";
import Taro, { useReachBottom } from "@tarojs/taro";
import { useShare } from "@/stores/useShare";
import GoodsShare from "./share";

export default function Page() {
  const [state, setState] = useSetState({
    data: null as any,
    share: false,
  });

  const [list, listAct] = useList({
    api: CusApi.getMarketProduct,
    params: { marketType: 40 },
  });

  useMount(() => {
    listAct.getList();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  useShare(() => {
    if (!state.data) return;

    return {
      title: state.data?.name,
      path: `/pages/mall/fxinfo/index?id=${state.data?.id}`,
      imageUrl: state.data?.coverImage,
    };
  });

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="grid cols-2 gap-40 px-40 py-40">
        {list.list.map((item) => (
          <View
            className="bg-#fff"
            key={item.id}
            onClick={() =>
              Taro.navigateTo({
                url: `/pages/mall/fxinfo2/index?id=${item.id}`,
              })
            }
          >
            <View className="pt-1/1 pos-relative">
              <Image
                className="pos-absolute top-0 left-0 size-full bg-#aaa overflow-hidden"
                style={{ borderRadius: "24rpx 24rpx 0 0" }}
                mode="aspectFill"
                src={item.coverImage}
              />
            </View>

            <View className="bg-#fff p-20">
              <View className="flex items-center pb-10">
                <View className="text-(22 #11100F) flex-1 min-w-0 pr-10">
                  <View className="line-clamp-1">{item.name}</View>
                </View>
                <View className="text-(28 #292827) first-letter:(text-20 mr-3) fw-bold">
                  &yen;{item.salePrice}
                </View>
              </View>
              <View className="flex items-center text-(16 #292827) pb-20">
                <View>预估收益</View>
                <View className="ml-5 text-(24 #8A1921) first-letter:(text-18)">
                  &yen;{item.preCommission}
                </View>
                <View className="ml-a ">
                  佣金比例
                  {(item?.preCommissionRate * 100).toFixed(2)}%
                </View>
              </View>

              <View
                className="h-60 b-(1 solid #AD7F6D) rounded-13 bg-#fff flex items-center justify-center"
                onClick={(e) => {
                  e.stopPropagation();
                  setState({ data: item, share: true });
                }}
              >
                <View className="text-(22 #905139)">我要推广</View>
                <UnoIcon className="i-icon-park-outline:share-one text-(22 #905139)" />
              </View>
            </View>
          </View>
        ))}
      </View>

      {!!state.data?.id && (
        <GoodsShare
          key={state.data?.id}
          open={state.share}
          data={state.data}
          onOK={() => setState({ share: false })}
        />
      )}
    </View>
  );
}
