import { PopUp } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import Cus<PERSON>pi from "@/services/CusApi";
// import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { Image, ScrollView, View } from "@tarojs/components";
import { useMount, useSetState } from "ahooks";

export const ShopPicker = (props: {
  open?: boolean;
  onClose?: any;
  onOK?: any;
  loadFirst?: boolean;
}) => {
  const [state, setState] = useSetState({
    shops: [] as any[],
    shop: null as any,
  });

  useMount(() => {
    fetchShops();
  });

  const fetchShops = async () => {
    // const res = await CusApi.getShops({ pageSize: 9999 });
    // const shops = res?.list || [];
    const res = await CusApi.getAdminShops();
    const shops = res?.list || [];
    setState({ shops });

    if (props.loadFirst) {
      props.onOK?.(shops?.[0] || null);
    }
  };

  const onClose = () => {
    props.onClose();
  };

  const onOk = async () => {
    if (!state.shop) return showToast("请选择门店");

    props.onClose?.();
    props.onOK?.(state.shop);
  };

  return (
    <PopUp open={props.open} onClose={onClose}>
      <View className="h-800 bg-#fff flex flex-col">
        <View className="h-80 bg-#fff flex b-b-(1 solid #eee)">
          <View className="px-25 text-(26/80 #999)" onClick={onClose}>
            取消
          </View>
          <View className="flex-1 text-(30/80 #333 center) fw-bold">
            选择门店
          </View>
          <View className="px-25 text-(26/80 #333)" onClick={onOk}>
            确认
          </View>
        </View>
        <ScrollView scrollY className="flex-1 min-h-0">
          <View className="px-30">
            {state.shops.map((item) => (
              <View
                key={item.id}
                className="flex items-center py-30 b-b-(1 solid #eee)"
                onClick={() => setState({ shop: item })}
              >
                <Image
                  className="size-96 bg-#eee rounded-full mr-30"
                  mode="aspectFill"
                  src={item.logo}
                />
                <View className="flex-1 min-w-0">
                  <View className="text-(28 #1A1A1A)">{item.name}</View>
                  <View className="text-(22 #636363) mt-5">
                    {item.fullAddress}
                  </View>
                </View>

                <View className="size-36">
                  {state.shop?.id == item.id && (
                    <UnoIcon className="i-icon-park-outline:check size-full c-#AD7F6D" />
                  )}
                </View>
              </View>
            ))}

            <View className="text-(24 #999 center) py-30">没有更多门店了</View>
          </View>
        </ScrollView>
      </View>
    </PopUp>
  );

  // return (
  //   <Modal open>
  //     <View className="w-680 bg-#fff rounded-20">123123123</View>
  //   </Modal>
  // );
};
