import { UnoIcon } from "@/components/UnoIcon";
import { Image, Input, ScrollView, Text, View } from "@tarojs/components";
import { CusRadio, CusRadio2 } from "./unit";
import { useMount, useSetState } from "ahooks";
import { Calendar } from "@taroify/core";
import dayjs from "dayjs";
import Taro from "@tarojs/taro";
import { ShopPicker } from "./ShopPicker";
import CusApi from "@/services/CusApi";
import { useEffect, useMemo } from "react";
import { showToast } from "@/utils/tools";

const OFFSET_DAY = 30 * 3;

export default function Page() {
  const [state, setState] = useSetState({
    shop: null as any,
    type: "",
    date: dayjs().format("YYYY-MM-DD"),
    time: "",
    remark: "",
    name: "",
    mobile: "",

    openDate: false,
    shopOpen: false,
    types: [] as any[],
    timeData: [] as any[],

    users: [] as any[],
  });

  const init = useMemo(() => {
    const start = dayjs().format("YYYY-MM-DD");
    const end = dayjs().add(OFFSET_DAY, "day").format("YYYY-MM-DD");
    const range = Object.keys(Array(OFFSET_DAY + 1).fill(null))
      .map((n) => dayjs().add(+n, "day"))
      .map((n) => {
        const week = "周" + "日一二三四五六"[n.day()];
        const md = n.format("MM/DD");
        const ydm = n.format("YYYY-MM-DD");
        return { week, md, ydm };
      });

    return { start, end, range };
  }, []);

  useMount(() => {
    getTypes();
  });

  // 获取可选排班
  useEffect(() => {
    if (state.shop?.id && state.type && init.start && init.end) {
      getDateTimes();
    }
  }, [state.shop, state.type, init.start, init.end]);

  // 获取选中排班
  const crtTimes: any[] = useMemo(() => {
    const crt = state.timeData?.find((n) => n.preDate == state.date);
    return crt?.timeList || [];
  }, [state.timeData, state.date]);

  const getTypes = async () => {
    const res = await CusApi.getDict({ typeEnCode: "reservationType" });
    const types = res || [];
    const type = types?.[0]?.id;
    setState({ types, type });
  };

  const getDateTimes = async () => {
    const res = await CusApi.getDueTimes({
      shopId: state.shop?.id,
      typeId: state.type,
      startPreDate: init.start,
      endPreDate: init.end,
    });
    let data = res?.list || [];
    data = data.map((item) => {
      if (!item.preTag) {
        return { ...item, timeList: [] };
      }
      return item;
    });

    setState({ timeData: res?.list || [] });
  };

  const onDatePick = (e) => {
    const date = dayjs(e).format("YYYY-MM-DD");
    setState({ date, time: "", openDate: false });
  };

  const onSubmit = async () => {
    Taro.showLoading({ title: "预约中..." });

    if (!state.shop?.id) return showToast("请选择门店");
    if (!state.type) return showToast("请选择项目");
    if (!state.date) return showToast("请选择日期");
    if (!state.time) return showToast("请选择时间");
    if (!state.name && !state.mobile) return showToast("缺少客户姓名或手机");

    const data = {
      shopId: state.shop?.id,
      typeId: state.type,
      preTime: state.date + " " + state.time + ":00",
      content: state.remark,
      preName: state.name,
      preMobile: state.mobile,
    };

    await CusApi.addAdminDue(data);
    Taro.hideLoading();
    Taro.navigateBack();
  };

  useEffect(() => {
    fetchUser();
  }, [state.time, state.shop?.id, state.type, state.date]);

  const fetchUser = async () => {
    if (!state.shop?.id || !state.type || !state.date || !state.time) {
      setState({ users: [] });
      return;
    }

    const res = await CusApi.getAdminDueList({
      shopId: state.shop?.id,
      typeId: state.type,
      startPreTime: state.date + " " + state.time + ":00",
      endPreTime: state.date + " " + state.time + ":00",
      pageSize: 9999,
    });
    console.log(res);
    setState({ users: res?.list || [] });
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF px-30">
      <View className="z-30 pos-sticky -mx-30 px-30 pb-25 top-0 bg-#F7F4EF">
        <View className="bg-#FDFCFA rounded-10 px-25">
          <View className="flex items-center py-30">
            <View className="text-(30 #1A1A1A) flex-1 fw-bold">选择门店</View>
            {!!state.shop && (
              <View
                className="flex items-center"
                onClick={() => setState({ shopOpen: true })}
              >
                <View className="text-(21 #636363)">更换门店</View>
                <UnoIcon className="i-icon-park-outline:right size-20 text-#636363 ml-15" />
              </View>
            )}
          </View>
          {state.shop ? (
            <View
              className="flex pb-40 items-center"
              onClick={() => setState({ shopOpen: true })}
            >
              <Image
                className="size-96 bg-#eee rounded-full mr-30"
                mode="aspectFill"
                src={state.shop?.logo}
              />
              <View className="flex-1 min-w-0">
                <View className="text-(28 #1A1A1A)">{state.shop?.name}</View>
                <View className="text-(22 #636363) mt-5">
                  {state.shop?.fullAddress}
                </View>
              </View>
            </View>
          ) : (
            <View
              className="flex items-center justify-center"
              onClick={() => setState({ shopOpen: true })}
            >
              <View className="pt-20 pb-50 text-(26 #999 center)">
                请选择门店
              </View>
            </View>
          )}
        </View>
      </View>

      <View className="bg-#FDFCFA rounded-10 px-25 mb-25">
        <View className="flex items-center py-30">
          <View className="text-(30 #1A1A1A) flex-1 fw-bold">选择项目</View>
        </View>
        <View className="grid cols-3 gap-40 pb-30">
          {state.types.map((item) => (
            <CusRadio
              className="h-80"
              key={item.id}
              active={item.id == state.type}
              onClick={() => setState({ type: item.id, time: "" })}
            >
              <View className="line-clamp-1">{item.name}</View>
            </CusRadio>
          ))}
        </View>
      </View>

      <View className="bg-#FDFCFA rounded-10 px-25 mb-25">
        <View className="flex items-center py-30">
          <View className="text-(30 #1A1A1A) flex-1 fw-bold">选择日期</View>
        </View>

        <View className="flex pb-25">
          <View className="flex-1 min-w-0 h-140 overflow-hidden">
            <View className="h-2/1">
              <ScrollView
                scrollX
                className="size-full"
                scrollIntoView={`date-${state.date}`}
                aaa-bbb={200}
                scrollWithAnimation={true}
              >
                <View className="ws-nowrap text-0">
                  {init.range.map((item) => (
                    <View
                      className="pos-relative inline-block ws-normal size-138 mr-25"
                      key={item.ydm}
                    >
                      <View
                        id={`date-${item.ydm}`}
                        className="pos-absolute top-0 -left-100 size-0"
                      />
                      <CusRadio
                        className="size-full flex-col"
                        active={state.date == item.ydm}
                        onClick={() => setState({ date: item.ydm, time: "" })}
                      >
                        <View className="text-24">{item.md}</View>
                        <View className="text-20 mt-5">{item.week}</View>
                      </CusRadio>
                    </View>
                  ))}
                </View>
              </ScrollView>
            </View>
          </View>

          <View
            className="w-80 flex flex-col items-center justify-center bg-#fff"
            onClick={() => setState({ openDate: true })}
          >
            <UnoIcon className="i-icon-park-outline:calendar-thirty size-36 text-#666" />
            <View className="text-(20 #999) mt-8">日历</View>
          </View>
        </View>

        <View className="h-1 bg-#eee"></View>

        {!crtTimes.length ? (
          <View className="py-60 flex flex-col items-center justify-center text-(24 #999 center)">
            <UnoIcon className="i-icon-park-outline:inbox size-36 text-#999 mb-15" />
            <View className="mt-8">当前日期暂无可预约时段</View>
            <View className="mt-8">请切换日期预约</View>
          </View>
        ) : (
          <View className="grid cols-4 gap-20 pt-30 pb-50">
            {crtTimes.map((item) => {
              const isDisabled =
                !item.preTag || item.reservedNum >= item.preNum;

              let num = Math.max(0, item.preNum - item.reservedNum);

              return (
                <View key={item.preDate}>
                  <CusRadio2
                    className="h-70"
                    // disabled={isDisabled}
                    active={state.time == item.preTime}
                    onClick={() => setState({ time: item.preTime })}
                  >
                    <View>
                      {item.preTime}

                      {isDisabled && (
                        <Text className="text-(20 #999)">(约满)</Text>
                      )}

                      {!isDisabled && num > 0 && num < item.preNum && (
                        <Text className="text-(20 #999)">(仅剩{num})</Text>
                      )}
                    </View>
                  </CusRadio2>
                </View>
              );
            })}
          </View>
        )}
      </View>

      <View className="bg-#FDFCFA rounded-10 px-25 mb-25 grid space-y-30 py-30">
        <View className="flex items-center">
          <View className="text-(30 #1A1A1A) w-200 fw-bold">客户姓名</View>
          <Input
            className="flex-1 min-w-0 h-82 rounded-14 bg-#eee px-30 text-(25 #333)"
            placeholder="请输入客户姓名"
            value={state.name}
            onInput={(e) => setState({ name: e.detail.value })}
          />
        </View>

        <View className="flex items-center">
          <View className="text-(30 #1A1A1A) w-200 fw-bold">客户手机</View>
          <Input
            className="flex-1 min-w-0 h-82 rounded-14 bg-#eee px-30 text-(25 #333)"
            placeholder="请输入客户手机"
            value={state.mobile}
            onInput={(e) => setState({ mobile: e.detail.value })}
          />
        </View>

        <View className="flex items-center">
          <View className="text-(30 #1A1A1A) w-200 fw-bold">特殊备注</View>
          <Input
            className="flex-1 min-w-0 h-82 rounded-14 bg-#eee px-30 text-(25 #333)"
            placeholder="请输入备注"
            value={state.remark}
            onInput={(e) => setState({ remark: e.detail.value })}
          />
        </View>
      </View>

      <View>
        <View className="pos-relative text-center">
          <View className="pos-absolute top-1/2 h-1 w-full bg-#eee"></View>
          <View className="pos-relative px-10 bg-#F7F4EF inline-block mx-auto text-(23 #666 center) py-20">
            当前时间段已预约
          </View>
        </View>

        <View className="grid cols-1 gap-20">
          {state.users.map((item) => (
            <View className="bg-#fff rounded-10 p-30" key={item.id}>
              <View className="flex items-center py-8">
                <View className="text-(26 #636363) w-150">客户姓名</View>
                <View className="text-(29 #11100F) flex-1 min-w-0">
                  {item.preName}
                </View>
              </View>
              <View className="flex items-center py-8">
                <View className="text-(26 #636363) w-150">客户手机</View>
                <View className="text-(29 #11100F) flex-1 min-w-0">
                  {item.preMobile}
                </View>
              </View>
              <View className="flex items-center py-8">
                <View className="text-(26 #636363) w-150">客户备注</View>
                <View className="text-(29 #11100F) flex-1 min-w-0">
                  {item.content}
                </View>
              </View>
            </View>
          ))}
          {!state.users?.length && (
            <View className="flex items-center justify-center py-20">
              <UnoIcon className="i-icon-park-outline:inbox size-30 text-#999" />
              <View className="text-(23 #666 center) ml-5">暂无预约</View>
            </View>
          )}
        </View>
      </View>

      <View className="h-170"></View>
      <View className="z-55 pos-fixed left-0 bottom-0 h-170 w-full bg-#F7F4EF">
        <View className="h-full px-30 flex items-center">
          <View
            className="w-full h-92 bg-#8E4E36 rounded-20 text-(30/92 #fff center) ml-a"
            onClick={onSubmit}
          >
            立即预约
          </View>
        </View>
      </View>

      <Calendar
        poppable
        showPopup={state.openDate}
        onClose={() => setState({ openDate: false })}
        min={new Date(init.start)}
        max={new Date(init.end)}
        onConfirm={onDatePick}
        className="[--calendar-active-color:#8E4E36]"
      />

      <ShopPicker
        open={state.shopOpen}
        onClose={() => setState({ shopOpen: false })}
        onOK={(shop) => setState({ shop, time: "" })}
        loadFirst
      />
    </View>
  );
}
