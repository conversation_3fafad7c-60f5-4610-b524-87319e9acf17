import { Modal } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useRequest } from "ahooks";
import axios from "axios";

export default function AdminShopPick() {
  const router = useRouter();
  const req = useRequest(CusApi.getAdminShops, {
    defaultParams: [{ pageSize: 9999 }],
    onSuccess: (data) => {
      // console.log(111, data);
      if (data?.list?.length == 1) {
        const id = data?.list?.[0]?.id;
        jump(id);
      }
    },
  });

  const jump = (id: string) => {
    const url = axios.getUri({
      url: decodeURIComponent(router.params.url || ""),
      params: { shopId: id },
    });

    Taro.redirectTo({ url });
  };

  return (
    <Modal open={true}>
      <View className="rounded-20 bg-#fff">
        <View className="w-600 box-border p-30">
          <View className="text-(34 #333 center) fw-bold pb-20">选择门店</View>
          <View className="border-(1 solid #eee) rounded-20">
            {req.loading && (
              <View className="p-40 text-(24 #999 center)">loading...</View>
            )}
            {!req.loading && (
              <>
                {!req.data?.list?.length && (
                  <View className="p-40 text-(24 #999 center)">empty...</View>
                )}
                {req.data?.list?.map((item) => (
                  <View
                    key={item.id}
                    className="flex items-center p-25 gap-10 border-t-(1 solid #eee) first:border-none"
                    onClick={() => jump(item.id)}
                  >
                    <UnoIcon className="i-icon-park-outline:local-two text-(35 #333)" />
                    <View className="flex-1 min-w-0">
                      <View className="text-(30 #333) line-clamp-1">
                        {item.name}
                      </View>
                    </View>
                    <View className="px-15 py-8 border-(1 solid #eee) rounded-10 text-(30 #333)">
                      进入
                    </View>
                  </View>
                ))}
              </>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
}
