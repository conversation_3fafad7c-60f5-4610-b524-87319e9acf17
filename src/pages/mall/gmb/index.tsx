import { Picker, View } from "@tarojs/components";
import { UnoIcon } from "@/components/UnoIcon";
import { PopUp } from "@/components/Custom/PopUp2";
import RichContent from "@/components/RichContent";
import Style from "./index.module.scss";
import { useSetState, useUpdateEffect } from "ahooks";
import dayjs from "dayjs";
import CusApi from "@/services/CusApi";
import { makeApi } from "@/utils/request";
import Taro, { useDidShow } from "@tarojs/taro";
import clsx from "clsx";

const getRule = makeApi("get", `/api/mallShopVipLevel/query_rule`);

export default () => {
  const [state, setState] = useSetState({
    date: dayjs().format("YYYY-MM"),

    user: null as any,
    stat: null as any,
    list: [] as any[],

    // 规则弹窗相关
    rule: null as any,
    ruleOpen: false,
  });

  useDidShow(() => {
    fetchUser();
    fetchList();
    fetchStat();
    fetchRule();
  });

  useUpdateEffect(() => {
    fetchList();
    fetchStat();
  }, [state.date]);

  const fetchUser = async () => {
    const user = await CusApi.getUserInfo();
    setState({ user });
  };

  const fetchList = async () => {
    const start = dayjs(state.date)
      .startOf("month")
      .format("YYYY-MM-DD HH:mm:ss");
    const end = dayjs(state.date).endOf("month").format("YYYY-MM-DD HH:mm:ss");

    const res = await CusApi.getWalletList({
      source: 2,
      startCreateDate: start,
      endCreateDate: end,
      pageSize: 9999,
    });
    const list = res?.list || [];

    setState({ list });
  };

  const fetchStat = async () => {
    const start = dayjs(state.date)
      .startOf("month")
      .format("YYYY-MM-DD HH:mm:ss");
    const end = dayjs(state.date).endOf("month").format("YYYY-MM-DD HH:mm:ss");
    const res = await CusApi.getWalletStat({
      source: 2,
      startCreateDate: start,
      endCreateDate: end,
    });
    setState({ stat: res || {} });
  };

  const fetchRule = async () => {
    try {
      const rule = await getRule();
      setState({ rule });
    } catch (error) {
      console.error("获取规则失败", error);
    }
  };

  return (
    <View className={Style.page}>
      <View className="pos-relative mx-30 mt-30 p-40 bg-#e2b794 rounded-t-20">
        {/* M币余额标题和详细规则按钮 */}
        <View className="flex items-center justify-between mb-16">
          <View className="text-(28 #fff) fw-medium">M币余额</View>
          <View
            className="px-16 py-8 bg-#fff/20 backdrop-blur-sm rounded-full text-(26 #fff) flex items-center border border-#fff/30 active:scale-95 transition-transform cursor-pointer"
            onClick={() => {
              console.log("点击详细规则按钮", state.ruleOpen);
              setState({ ruleOpen: true });
            }}
          >
            详细规则
            <UnoIcon className="i-icon-park-outline:right size-28 ml-4 text-#fff" />
          </View>
        </View>

        {/* M币余额数值 */}
        <View className="text-(56 #fff) fw-bold mb-40 font-mono">
          {state.user?.coin ?? "0"}
        </View>

        {/* 可提现M币和提现按钮 */}
        <View className="flex items-center justify-between">
          <View className="flex-1">
            <View className="text-(24 #fff) mb-8">可提现M币</View>
            <View className="text-(40 #fff) fw-bold font-mono">
              {state.user?.cashCoin ?? "0"}
            </View>
          </View>

          <View
            className="ml-30 px-32 py-16 bg-gradient-to-r from-#8d6e63 to-#5d4037 rounded-full text-(28 #fff) fw-bold active:scale-95 transition-transform border border-#fff/20"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/gmbDraw/index` });
            }}
          >
            提现
          </View>
        </View>
      </View>

      <View className={Style.filter}>
        <Picker
          mode="date"
          fields="month"
          value={state.date}
          end={dayjs().format("YYYY-MM-DD")}
          onChange={(e) => {
            setState({ date: e.detail.value });
          }}
        >
          <View className={Style.date}>
            <View className={Style.txt}>
              {dayjs(state.date).format("YYYY年MM月")}
            </View>
            <View className={Style.arr}></View>
          </View>
        </Picker>
        <View className={Style.extra}>
          收益：{state.stat?.revenueCoin ?? "--"}M币 支出：
          {state.stat?.expenditureCoin ?? "--"}M币
        </View>
      </View>

      <View className={Style.list}>
        {state.list.map((item) => {
          return (
            <View className={Style.item} key={item.id}>
              <View className={Style.left}>
                <View className={Style.name}>{item.moneyTypeName}</View>
                <View className={Style.date}>
                  {dayjs(item.createDate).format("YYYY-MM-DD HH:mm:ss")}
                </View>
              </View>
              <View
                className={clsx(
                  Style.right,
                  item.incomeExpenseType == 1 && Style.em
                )}
              >
                {item.coin}
              </View>
            </View>
          );
        })}
      </View>
      {!state.list.length && <View className={Style.empty}>暂无数据...</View>}

      {/* 临时测试按钮 */}
      <View
        className="fixed bottom-100 right-30 z-100 px-20 py-10 bg-red-500 text-white rounded"
        onClick={() => {
          console.log("测试按钮点击", state.ruleOpen);
          setState({ ruleOpen: !state.ruleOpen });
        }}
      >
        测试弹窗 ({state.ruleOpen ? "开" : "关"})
      </View>

      {/* 详细规则弹窗 */}
      <PopUp
        open={state.ruleOpen}
        onClose={() => {
          console.log("关闭弹窗");
          setState({ ruleOpen: false });
        }}
      >
        <View
          className="px-30 bg-#fff bg-[linear-gradient(0deg,rgba(226,199,171,0.7),rgba(255,255,255,0.7))]"
          style={{ borderRadius: "40rpx 40rpx 0 0" }}
        >
          <View className="flex items-center py-40">
            <View className="flex-1 text-(34 #333) fw-bold">
              详细规则 (状态: {state.ruleOpen ? "开启" : "关闭"})
            </View>
            <View
              onClick={() => {
                console.log("关闭弹窗按钮点击");
                setState({ ruleOpen: false });
              }}
              className="cursor-pointer"
            >
              <UnoIcon className="i-icon-park-outline:close size-40 text-#333" />
            </View>
          </View>
          <View className="pb-30 max-h-60vh overflow-y-auto">
            {state.rule ? (
              <RichContent html={state.rule?.integralRuleContent} />
            ) : (
              <View className="py-30 text-center text-#999">加载中...</View>
            )}
          </View>
        </View>
      </PopUp>
    </View>
  );
};
