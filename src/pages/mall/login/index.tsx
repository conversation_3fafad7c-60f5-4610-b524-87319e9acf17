import { CheckBox } from "@/components/CheckBox";
import { CusModal } from "@/components/Custom/Modal";
import {
  createToken,
  getOpenId,
  getUserByOpenid,
  registUser,
} from "@/services/UserApi";
import { showToast } from "@/utils/tools";
import { Button, Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useSetState } from "ahooks";

export default function Login() {
  const [state, setState] = useSetState({
    open: false,
    check: false,
    openId: "",
  });

  const onLogin = async () => {
    if (!state.check) return showToast("请先同意用户协议");
    const { code } = await Taro.login();
    const { openid: openId } = await getOpenId(
      { js_code: code, wxAppId: process.env.TARO_APP_ID },
      { _loading: true }
    );

    const user = await getUserByOpenid({ openId }, { _loading: true });

    if (user?.id) {
      loginByUser(user);
    } else {
      setState({ open: true, openId });
    }
  };

  const onRegist = async (e) => {
    setState({ open: false });
    const phonecode = e.detail.code;
    if (!phonecode) return;

    const refer = Taro.getStorageSync("promotionUserCode") || "";
    const user = await registUser(
      {
        phonecode,
        wxAppId: process.env.TARO_APP_ID,
        openId: state.openId,
        promotionUserCode: refer,
      },
      { _loading: true }
    );
    Taro.removeStorageSync("promotionUserCode");

    loginByUser(user);
  };

  const loginByUser = async (user) => {
    const { token } = await createToken(
      { userId: user.id, password: user.p },
      { _loading: true }
    );
    Taro.setStorageSync("token", token);
    Taro.setStorageSync("user", user);

    Taro.navigateBack({
      fail: () => Taro.reLaunch({ url: `/pages/tabs/mall/index` }),
    });
    // Taro.reLaunch({ url: `/pages/tabs/mall/index` });
  };

  return (
    <View className="">
      <Image
        className="block w-336 h-87 mx-a mt-236"
        mode="aspectFit"
        src="https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20240921/1287032909961367552.png"
      />
      <Image
        className="block w-265 h-19 mx-a mt-20"
        mode="aspectFit"
        src="https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20240921/1287032909927813120.png"
      />
      <View
        className="mt-70 h-90 mx-90 text-(30/90 #fff center) rounded-full bg-#8E4E36"
        onClick={onLogin}
      >
        手机号一键登录
      </View>
      <View
        className="mt-50 flex items-center justify-center text-(24 #666)"
        onClick={() => setState({ check: !state.check })}
      >
        <CheckBox className="size-30 mr-10" checked={state.check} />
        <View className="">我已阅读并同意</View>
        <View
          className="mx-5 text-#8E4E36"
          onClick={(e) => {
            e.stopPropagation();
            Taro.navigateTo({ url: `/pages/mall/agreement/index` });
          }}
        >
          《用户协议与隐私》
        </View>
      </View>

      <CusModal open={state.open}>
        <View className="bg-#fff rounded-20 overflow-hidden w-500">
          <View className="text-(30 #333 center) pt-30">授权手机号</View>
          <View className="text-(24 #666 center) py-20">
            授权手机号用于注册账号
          </View>
          <View className="flex items-center">
            <View
              className="pos-relative flex-1 h-80 text-(30/80 center red)"
              onClick={() => setState({ open: false })}
            >
              取消
            </View>
            <View className="pos-relative flex-1 h-80 text-(30/80 center #333)">
              <Button
                className="pos-absolute top-0 left-0 size-full opacity-0"
                openType="getPhoneNumber"
                onGetPhoneNumber={onRegist}
              />
              确定
            </View>
          </View>
        </View>
      </CusModal>
    </View>
  );
}
