import { Image, Input, View } from "@tarojs/components";
import Style from "./index.module.scss";
import Taro, { useDidShow } from "@tarojs/taro";
import CusApi from "@/services/CusApi";
import { useSetState } from "ahooks";
import { showToast } from "@/utils/tools";

export default () => {
  const [state, setState] = useSetState({
    user: null as any,
    value: "" as any,
  });

  useDidShow(() => {
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await CusApi.getUserInfo();
    setState({ user });
  };

  const handleSubmit = async () => {
    if (!state.value) return showToast("请输入M币数量");
    const res = await CusApi.addWalletDraw(
      {
        source: 11,
        applyId: state.user?.id,
        applyMoney: state.value,
        moneyDestination: 2,
        wxAppId: process.env.TARO_APP_ID,
      },
      { _loading: true }
    );

    await (Taro as any).requestMerchantTransfer({
      mchId: res?.wxMchId,
      appId: process.env.TARO_APP_ID,
      package: res?.wxPackageInfo,
      success: () => {
        Taro.navigateBack();
      },
      fail: (err) => {
        console.log(err);
        showToast("发生错误，请重试");
      },
    });
  };

  return (
    <View className={Style.page}>
      <View className={Style.box}>
        <View className={Style.tit}>提现M币</View>
        <View className={Style.num}>
          <Input
            className={Style.ipt}
            placeholder="请输入提现数量"
            value={state.value}
            onInput={(e) => setState({ value: e.detail.value })}
          />
        </View>
        <View className={Style.desc}>
          <View className={Style.txt}>
            当前M币{state.user?.cashCoin ?? "--"}
          </View>
          <View
            className={Style.em}
            onClick={() => {
              const all = state.user?.cashCoin || "";
              console.log(111111, all);
              setState({ value: all });
            }}
          >
            全部提现
          </View>
        </View>
        <View className={Style.foot}>
          <Image
            className={Style.wx}
            src={require("@/assets/weixin.png")}
          ></Image>
          <View className={Style.txt}>提现到微信</View>
          <Image
            className={Style.ico}
            src={require("@/assets/ck-2.png")}
          ></Image>
        </View>
      </View>

      <View className="p-30 text-24/40 text-gray-500 mt-4">
        提现说明：
        <View>1、1M币=1元</View>
        <View>2、单笔限额200元，单日提现累计限额2000元</View>
        <View>3、提现时间为每月25日-月未</View>
      </View>

      <View className={Style.footer}>
        <View className={Style.btn} onClick={handleSubmit}>
          立即提现
        </View>
      </View>
    </View>
  );
};
