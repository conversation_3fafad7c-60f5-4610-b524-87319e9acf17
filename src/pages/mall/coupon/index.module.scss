.list {
  padding: 40px;

  .item {
    position: relative;
    background: #fff;
    border-radius: 40px;
    height: 267px;
    overflow: hidden;
    margin-bottom: 40px;
    display: flex;
  }

  .left {
    position: relative;
    width: 260px;
    background: #d0ab6b;
    color: #fff;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;

    &::before,
    &::after {
      position: absolute;
      right: 0;
      content: "";
      width: 40px;
      height: 40px;
      background: #f5f5f5;
      border-radius: 50%;
    }

    &::before {
      top: 0;
      transform: translate(50%, -50%);
    }

    &::after {
      bottom: 0;
      transform: translate(50%, 50%);
    }

    .name {
      font-size: 70px;
      font-weight: bold;
    }

    .sm {
      font-size: 30px;
      font-weight: bold;
      margin-right: 10px;
    }

    .desc {
      margin-top: 20px;
      font-size: 28px;
    }
  }

  .right {
    min-width: 0;
    flex: 1;
    padding: 0 40px;
    display: flex;
    // flex-flow: column;
    align-items: center;

    .in {
      min-width: 0;
    }

    .name {
      font-size: 42px;
      color: #000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .desc {
      margin-top: 15px;
      font-size: 26px;
      color: #aaa;
    }
  }

  .extra {
    align-self: center;
    width: 148px;
    height: 63px;
    line-height: 63px;
    text-align: center;
    color: #d0ab6b;
    border: 1px solid #d0ab6b;
    border-radius: 14px;
    font-size: 30px;
    margin: 0 45px 0 0;
  }
}
