import { Text, View } from "@tarojs/components";
import Style from "./index.module.scss";
import { useMount, useSetState } from "ahooks";
import CusApi from "@/services/CusApi";
import Taro, { usePullDownRefresh } from "@tarojs/taro";
import dayjs from "dayjs";

const Coupon = () => {
  const [state, setState] = useSetState({
    user: null as any,
    list: [] as any[],
  });

  useMount(() => {
    fetchUser();
    fetchList();
  });

  usePullDownRefresh(async () => {
    await fetchList();
    fetchUser();
    Taro.stopPullDownRefresh();
  });

  const fetchUser = async () => {
    const user = await CusApi.getUserInfo();
    setState({ user });
    console.log(user);
  };

  const fetchList = async () => {
    const res = await CusApi.getMallCoupon({ obtainStyle: 30, pageSize: 9999 });
    let list: any[] = res?.list || [];
    list.forEach((item) => {
      if (item.effectiveStyle == 20) {
        const start = dayjs(item.effectiveStartDate).format("YYYY-MM-DD");
        const end = dayjs(item.effectiveEndDate).format("YYYY-MM-DD");
        item._date = start + "至" + end;
      } else if (item.effectiveStyle == 10) {
        const start = dayjs()
          .add(item.effectiveDeferredDays, "day")
          .format("YYYY-MM-DD");
        const end = dayjs()
          .add(item.effectiveDeferredDays, "day")
          .add(item.effectiveDays, "day")
          .format("YYYY-MM-DD");
        item._date = start + "至" + end;
      } else {
        return "--";
      }
    });

    setState({ list: res?.list || [] });
  };

  const handlePick = async (item: any) => {
    const modal = await Taro.showModal({
      title: "确定兑换当前优惠券吗？",
    });
    if (modal.cancel) return;
    await CusApi.obtainMallCoupon({ couponId: item.id }, { _loading: true });
    Taro.navigateTo({ url: `/pages/mall/couponRes/index` });
  };

  return (
    <View>
      <View className="pos-relative top-0 h-60 px-30 flex items-center text-(26 #8E4E36) bg-#8E4E36/5">
        当前M币：{state.user?.coin ?? "--"}
      </View>

      <View className={Style.list}>
        {state.list.map((item) => (
          <View
            className={Style.item}
            key={item.id}
            onClick={() => handlePick(item)}
          >
            <View className={Style.left}>
              {item.type == 10 && (
                <View className={Style.name}>
                  <Text className={Style.sm}>&yen;</Text>
                  <Text>{item.preferentialMoney}</Text>
                </View>
              )}
              {item.type == 20 && (
                <View className={Style.name}>
                  <Text>{item.discount}</Text>
                  <Text className={Style.sm}>折</Text>
                </View>
              )}
              <View className={Style.desc}>{item.exchangeCoin}M币兑换</View>
            </View>
            <View className={Style.right}>
              <View className={Style.in}>
                <View className={Style.name}>{item.name}</View>
                <View className={Style.desc}>
                  {item.minConsumeMoney
                    ? `满${item.minConsumeMoney}元可用`
                    : "无门槛"}
                </View>
                <View className={Style.desc}>
                  {/* {item.effectiveStyle} */}
                  有效期：{item._date}
                </View>
              </View>
            </View>
            <View className={Style.extra}>立即兑换</View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default Coupon;
