import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { getUserByToken } from "@/services/UserApi";
import { img_avatar } from "@/utils/images";
import { makeApi } from "@/utils/request";
import { openConcat } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";

const ApiGetStat = makeApi(`get`, `/api/promotionCenter/main_panel`);

export default function Page() {
  const [state, setState] = useSetState({
    user1: null as any,
    user2: null as any,
    stat: null as any,
  });

  useMount(() => {
    fetchUser1();
    fetchUser2();
    fetchStat();
  });

  const fetchUser1 = async () => {
    const user1 = await getUserByToken();
    setState({ user1 });
  };

  const fetchUser2 = async () => {
    const user2 = await CusApi.getUserInfo();
    setState({ user2 });
  };

  const fetchStat = async () => {
    const res = await ApiGetStat();
    setState({ stat: res });
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="h-300 -mb-300 bg-[linear-gradient(180deg,#E3C9AE,#F7F4EF)]" />
      <View className="px-30">
        <Image
          className="z-3 pos-relative box-border block size-160 -mb-80 ml-70 bg-#fff b-(6 solid #fff) rounded-full"
          mode="aspectFill"
          src={state.user1?.photo || img_avatar}
        />
        <View className="bg-#fff rounded-30 pos-relative">
          <Image
            className="pos-absolute top-30 right-20 w-437 h-116"
            mode="scaleToFill"
            src="https://oss.gaomei168.com/file-release/20250118/1330221721157644288.png"
          />

          <View className="pt-100 pb-60 px-50">
            <View className="text-(32 #333)">{state.user1?.name}</View>
            <View className="mt-6 text-(23 #666)">
              {state.user2?.vipLevelName}
            </View>

            <View className="flex pt-40 items-end">
              <View
                className="flex-1"
                onClick={() =>
                  Taro.navigateTo({ url: `/pages/mall/fxlog/index` })
                }
              >
                <View className="text-(70/80 #8D5034) fw-bold">
                  {state.user2?.cashCoin}
                </View>
                <View className="text-(25 #333)">可提现佣金&gt;</View>
              </View>
              <View
                className="text-(30 #5A5A5A) b-(1 solid #8E4E36) bg-#F7F4EF rounded-16 px-40 py-10"
                onClick={() => {
                  Taro.navigateTo({ url: `/pages/mall/fxdraw/index` });
                }}
              >
                去提现
              </View>
            </View>
          </View>
        </View>

        <View className="bg-#fff rounded-30 flex mt-30 py-80">
          <View
            className="flex-1 flex flex-col justify-center gap-10 pl-40"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/fxlog/index` });
            }}
          >
            <View className="text-(23 #11100F)">今日收益</View>
            <View className="text-(30 #11100F) fw-bold">
              {state.stat?.todayIncome ?? "--"}
            </View>
            <View className="text-(22 #666)">
              累计{state.stat?.totalIncome ?? "--"}
            </View>
          </View>

          <View
            className="flex-1 flex flex-col justify-center gap-10 pl-40"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/fxorder/index?date=今日` });
            }}
          >
            <View className="text-(23 #11100F)">今日订单</View>

            <View className="text-(30 #11100F) fw-bold">
              {state.stat?.todayOrderNum ?? "--"}
            </View>
            <View className="text-(22 #666)">
              累计{state.stat?.totalOrderNum ?? "--"}
            </View>
          </View>

          <View
            className="flex-1 flex flex-col justify-center gap-10 pl-40"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/fxuser/index?date=今日` });
            }}
          >
            <View className="text-(23 #11100F)">今日用户</View>

            <View className="text-(30 #11100F) fw-bold">
              {state.stat?.todayUserNum ?? "--"}
            </View>
            <View className="text-(22 #666)">
              累计{state.stat?.totalUserNum ?? "--"}
            </View>
          </View>
        </View>

        <View className="flex gap-30 mt-30">
          <View
            className="flex items-center flex-1 px-40 h-180 rounded-30 bg-[linear-gradient(180deg,#E3CDB6,#F7F4EF)] !bg-#E3CDB6"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/fxlist/index` });
            }}
          >
            <View className="flex-1">
              <View className="text-(34 #11100F)">推广商品</View>
              <View className="text-(18 #666) mt-8">好物C位 佣金到位</View>
            </View>
            <Image
              className="size-72"
              mode="scaleToFill"
              src="https://oss.gaomei168.com/file-release/20250117/1329854380511080448.png"
            />
          </View>

          <View
            className="flex items-center flex-1 px-40 h-180 rounded-30 bg-[linear-gradient(180deg,#E3CDB6,#F7F4EF)] !bg-#E3CDB6"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/fxrank/index` });
            }}
          >
            <View className="flex-1">
              <View className="text-(34 #11100F)">佣金排行</View>
              <View className="text-(18 #666) mt-8">佣金榜单 快来围观</View>
            </View>
            <Image
              className="size-72"
              mode="scaleToFill"
              src="https://oss.gaomei168.com/file-release/20250117/1329854380473331712.png"
            />
          </View>
        </View>

        <View className="bg-#F1E7DD rounded-30 h-250 mt-30 flex items-center">
          <View
            className="flex-1 flex flex-col items-center"
            onClick={() => Taro.navigateTo({ url: `/pages/mall/fxlog/index` })}
          >
            <Image
              className="size-46"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250117/1329854380477526016.png"
            />
            <View className="text-(22 #11100F) mt-20">佣金明细</View>
          </View>

          <View
            className="flex-1 flex flex-col items-center"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/fxorder/index` });
            }}
          >
            <Image
              className="size-46"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250117/1329854380511080449.png"
            />
            <View className="text-(22 #11100F) mt-20">推广订单</View>
          </View>

          <View
            className="flex-1 flex flex-col items-center"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/fxuser/index` });
            }}
          >
            <Image
              className="size-46"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250117/1329854380511080450.png"
            />
            <View className="text-(22 #11100F) mt-20">推广人</View>
          </View>

          <View
            className="flex-1 flex flex-col items-center"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/fxposter/index` });
            }}
          >
            <Image
              className="size-46"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250117/1329854380473331713.png"
            />
            <View className="text-(22 #11100F) mt-20">推广海报</View>
          </View>
        </View>

        <View className="h-100"></View>

        <View
          className="pos-fixed right-50 bottom-80 size-100 rounded-full b-(1 solid #8E4E36) bg-#F7F4EF flex justify-center"
          onClick={() => openConcat()}
        >
          <UnoIcon className="i-icon-park-outline:message-one text-(56 #8E4E36) mt-15" />
          <View className="pos-absolute left-1/2 -bottom-10 -translate-x-1/2 text-(20 #fff) bg-#8E4E36 rounded-full ws-nowrap px-10 py-5">
            在线咨询
          </View>
        </View>
      </View>
    </View>
  );
}
