import { img_avatar } from "@/utils/images";
import { makeApi } from "@/utils/request";
import { Image, View } from "@tarojs/components";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";

const top3 = [
  {
    order: 2,
    icon: "https://oss.gaomei168.com/file-release/20250117/1329859634719895552.png",
  },
  {
    order: 1,
    icon: "https://oss.gaomei168.com/file-release/20250117/1329859634698924033.png",
  },
  {
    order: 3,
    icon: "https://oss.gaomei168.com/file-release/20250117/1329859634711506945.png",
  },
];

const getRank = makeApi("get", `/api/promotionCenter/commission_rank`);
const getMyRank = makeApi("get", `/api/promotionCenter/my_commission_rank`);

export default function Page() {
  const [state, setState] = useSetState({
    list: [] as any[],
    self: null as any,
  });

  useMount(() => {
    fetchList();
    fetchMy();
  });

  const fetchList = async () => {
    const res = await getRank();
    setState({ list: res?.list || [] });
  };

  const fetchMy = async () => {
    const res = await getMyRank();
    setState({ self: res });
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="h-300 -mb-300 bg-[linear-gradient(180deg,#E3C9AE,#F7F4EF)]" />

      <View className="flex gap-10 justify-center pt-100">
        {top3.map((n, idx) => {
          const item: any = state.list?.[idx];

          return (
            <View
              key={n.order}
              className={clsx(
                "pos-relative w-222 h-282 bg-no-repeat bg-contain bg-[url(https://oss.gaomei168.com/file-release/20250117/1329859634711506944.png)]",
                n.order != 2 && "mt-50"
              )}
              style={{ order: n.order }}
            >
              <View className="pos-absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 size-100">
                <Image
                  className="block box-border size-full rounded-full b-(2 solid #8E4E36) overflow-hidden"
                  mode="aspectFill"
                  src={item?.userPhoto || img_avatar}
                />
                <Image
                  className="pos-absolute top-0 -left-23 size-46"
                  mode="scaleToFill"
                  src={n.icon}
                />
              </View>

              <View className="h-full px-20">
                <View className="text-(30 #666 center) mt-74 line-clamp-1">
                  {item?.userName}
                </View>
                <View className="text-(24 #8D4F36 center) mt-30">累计收益</View>
                <View className="text-(32/46 #8D4F36 center) fw-bold first-letter:(text-24 mr-5)">
                  &yen;{item?.totalIncome}
                </View>
              </View>
            </View>
          );
        })}
      </View>

      {/* <View className="cols-[1px]"></View> */}

      <View className="px-30 py-30 grid gap-20">
        {state.list.slice(3).map((item, idx) => (
          <View
            key={idx}
            className={clsx(
              "h-106 rounded-10",
              "flex items-center",
              "bg-#fff bg-[radial-gradient(circle_at_100%,#E2C7AB,#fff_22%,#fff)]"
            )}
          >
            <View className="text-(29 #646464 center) w-90">{idx + 4}</View>
            <Image
              className="size-54 rounded-full bg-#BFBFBF"
              mode="aspectFill"
              src={item.userPhoto || img_avatar}
            />
            <View className="text-(24 #3D3D3D) mx-15 w-180 line-clamp-1">
              {item.userName}
            </View>
            <View className="text-(24 #955942)">累计收益</View>
            <View className="flex-1 pr-35 text-(37 #8E4E36 right) first-letter:(text-21 mr-5)">
              &yen;{item.totalIncome}
            </View>
          </View>
        ))}
      </View>

      <View className="h-140"></View>
      <View
        className="z-50 pos-fixed left-0 bottom-0 h-140 w-full overflow-hidden b-t-(1 solid #eee) bg-#fff"
        style={{ borderRadius: "20rpx 20rpx 0 0" }}
      >
        <View
          className={clsx("h-full rounded-10", "flex items-center", "bg-#fff")}
        >
          <View className="pos-absolute top-20 left-10 text-(20 #999 center)">
            我的排名
          </View>

          <View className="text-(32 #8A1921 center) w-98">
            {!!state.self?.totalIncome ? state.self?.rankNo : "-"}
          </View>
          <Image
            className="size-60 rounded-full bg-#BFBFBF"
            mode="aspectFill"
            src={state.self?.userPhoto || img_avatar}
          />
          <View className="text-(27 #955942) mx-20 w-190 line-clamp-1">
            {state.self?.userName}
          </View>
          <View className="text-(26 #955942)">累计收益</View>
          <View className="flex-1 pr-35 text-(42 #8E4E36 right) first-letter:(text-21 mr-5)">
            &yen;{state.self?.totalIncome}
          </View>
        </View>
      </View>
    </View>
  );
}
