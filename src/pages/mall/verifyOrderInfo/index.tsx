import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { Image, Text, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useRequest } from "ahooks";
import dayjs from "dayjs";

export default function Page() {
  const router = useRouter();
  const { data: shop } = useRequest(CusApi.getShopInfo, {
    defaultParams: [{ id: router.params.shopId }],
  });
  const { data: info } = useRequest(CusApi.getVerifyOrder, {
    defaultParams: [
      { shopId: router.params.shopId, verifyCode: router.params.id },
    ],
  });

  return (
    <View className="min-h-lvh bg-#F7F4EF">
      <View className="p-30">
        <View className="bg-#FDFCFA rounded-10 px-25 mb-25">
          <View className="flex items-center py-30">
            <View className="text-(30 #1A1A1A) flex-1 fw-bold">当前门店</View>
          </View>

          <View className="flex pb-40 items-center">
            <Image
              className="size-96 bg-#eee rounded-full mr-30"
              mode="aspectFill"
              src={shop?.logo}
            />
            <View className="flex-1 min-w-0">
              <View className="text-(28 #1A1A1A)">{shop?.name}</View>
              <View className="text-(22 #636363) mt-5">
                {shop?.fullAddress}
              </View>
            </View>
          </View>
        </View>

        <View className="bg-#FDFCFA rounded-10 p-25 mb-25">
          <View className="grid gap-25">
            {info?.mallOrderItemList?.map((item) => (
              <View className="flex items-center" key={item.id}>
                <Image
                  className="size-110 mr-30"
                  src={item.productCoverImage}
                ></Image>
                <View className="flex-1 min-w-0">
                  <View className="text-(30 #333) line-clamp-1">
                    {item.productName}
                  </View>
                  <View className="flex justify-between text-(28 #999) mt-30">
                    <View>{item.productPrice}</View>
                    <View>x{item.totalCount}</View>
                  </View>
                </View>
              </View>
            ))}
          </View>
          <View className="b-t-(1 solid #eee) py-20 px-20 mt-20">
            <View className="flex py-10">
              <View className="flex-1 text-(24 #000)">商品总额</View>
              <View className="text-(24 #000)">&yen;{info?.totalMoney}</View>
            </View>
            <View className="flex py-10">
              <View className="flex-1 text-(24 #000)">会员折扣</View>
              <View className="text-(24 #000)">
                -&yen;{info?.discountPreferentialMoney}
              </View>
            </View>
            <View className="flex py-10">
              <View className="flex-1 text-(24 #000)">使用优惠券</View>
              <View className="text-(24 #000)">
                -&yen;{info?.couponConvertMoney}
              </View>
            </View>
            <View className="flex py-10">
              <View className="flex-1 text-(24 #000)">使用M币</View>
              <View className="text-(24 #000)">
                -&yen;{info?.integralConvertMoney}
              </View>
            </View>
            <View className="text-(28 #000 right) py-10">
              共计：&yen;
              <Text className="text-35 fw-bold">{info?.payMoney}</Text>
            </View>
          </View>
        </View>

        <View className="bg-#FDFCFA rounded-10 p-25 mb-25">
          <View
            className="text-(24 #000) py-15"
            onClick={() => {
              Taro.setClipboardData({
                data: info?.serialNo,
                success: () => showToast("订单号已复制"),
              });
            }}
          >
            订单编号：{info?.serialNo || "--"}{" "}
            <Text className="text-(22 #aaa)">复制</Text>
          </View>
          <View className="text-(24 #000) py-15">
            下单时间：
            {info?.createDate
              ? dayjs(info?.createDate).format("YYYY-MM-DD HH:mm:ss")
              : "--"}
          </View>
          <View className="text-(24 #000) py-15">
            下单备注：
            {info?.orderRemark || "--"}
          </View>
        </View>
      </View>

      <View className="h-90 p-30"></View>
      <View
        className="z-50 pos-fixed left-0 bottom-0 w-full box-border p-30 bg-#fff"
        onClick={async () => {
          const m1 = await Taro.showModal({
            title: "确定要核销这个订单吗？",
          });
          if (!m1.confirm) return;
          await CusApi.postVerifyOrder({
            shopId: router.params.shopId,
            verifyCode: router.params.id,
          });
          const m2 = await Taro.showModal({
            title: "核销成功",
            showCancel: false,
          });
          if (!m2.confirm) return;
          Taro.navigateBack();
        }}
      >
        <View className="h-90 text-(30/90 #fff center) bg-#8E4E36 rounded-10">
          确认核销
        </View>
      </View>
    </View>
  );
}
