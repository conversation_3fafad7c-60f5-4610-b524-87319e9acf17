import CusApi from "@/services/CusApi";
import { getUserByToken } from "@/services/UserApi";
import { img_share } from "@/utils/images";
import { Canvas, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { useMemo } from "react";

export default function Page() {
  const router = useRouter();
  const [state, setState] = useSetState({
    cvs: null as any,
    path: "",
  });

  const config = useMemo(() => {
    const type = router.params.type;
    let img = img_share;
    let url = `/pages/tabs/mall/index`;

    if (type == "dazi") {
      img = `https://oss.gaomei168.com/file-release/20250306/1347168720113111040_650_556.jpg`;
      url = `/pages/user/invite2/index`;
    }

    return { img, url };
  }, [router.params.type]);

  useMount(() => {
    Taro.createSelectorQuery()
      .select("#cvs")
      .fields({ node: true, size: true })
      .exec(async (res) => {
        const cvs: HTMLCanvasElement = res[0].node;
        const ctx: CanvasRenderingContext2D = cvs.getContext("2d")!;
        const dpr = Taro.getWindowInfo().pixelRatio;

        const cw = 650;
        const ch = 840;

        cvs.width = cw * dpr;
        cvs.height = ch * dpr;
        ctx.scale(dpr, dpr);

        const data = await fetchData();

        // clean
        ctx.fillStyle = "#fff";
        ctx.fillRect(0, 0, cw, ch);

        // cover
        drawCover(cvs, ctx, config.img, 0, 0, cw, 556);

        // qrcode
        drawImg(cvs, ctx, data.qr, 26, 584, 176, 176);

        // nick
        ctx.textBaseline = "top";
        ctx.font = `34px OPPOSans, sans-serif`;
        ctx.fillStyle = `#212121`;
        ctx.fillText(data.nick, 225, 632);

        // desc
        ctx.font = `24px OPPOSans, sans-serif`;
        ctx.fillStyle = `#757575`;
        ctx.fillText(`邀请您加入「重庆搞美医疗美容诊所」`, 225, 693);

        // remark
        ctx.font = `20px OPPOSans, sans-serif`;
        ctx.fillStyle = `#757575`;
        ctx.fillText(`长按识别或扫码进入小程序`, 206, 797);

        // bottom line
        ctx.lineWidth = 1;
        ctx.strokeStyle = "#E5E5E5";
        ctx.beginPath();
        ctx.moveTo(6, 779);
        ctx.lineTo(cw - 6, 779);
        ctx.stroke();

        // other
        setState({ cvs });
      });
  });

  const fetchData = async () => {
    const user = await getUserByToken();
    const code = user?.serialNo || "";
    const nick = user?.name || "";

    const qr = await CusApi.getQrcode(
      {
        wxAppId: process.env.TARO_APP_ID,
        path: `${config.url}?promotionUserCode=${code}`,
      },
      { _loading: true }
    ).then((res) => res?.base64Image || "");

    return { qr, nick };
  };

  const drawImg = (cvs, ctx, src, ...args) => {
    const img = cvs.createImage();
    img.onload = () => ctx.drawImage(img, ...args);
    img.src = src || "";
  };

  const drawCover = (cvs, ctx, src, x, y, dw, dh) => {
    const img = cvs.createImage();
    img.onload = () => {
      const iw = img.width;
      const ih = img.height;
      const sr = iw / ih;
      const dr = dw / dh;
      let sx, sy, sw, sh;

      if (sr < dr) {
        sw = iw;
        sh = sw / dr;
        sx = 0;
        sy = (ih - sh) / 2;
      } else {
        sh = ih;
        sw = sh * dr;
        sx = (iw - sw) / 2;
        sy = 0;
      }

      ctx.drawImage(img, sx, sy, sw, sh, x, y, dw, dh);
    };
    img.src = src || "";
  };

  const saveToAlbum = () => {
    Taro.canvasToTempFilePath({
      canvas: state.cvs,
      fileType: "jpg",
      quality: 0.8,
      success: (res) => {
        const path = res.tempFilePath;
        Taro.saveImageToPhotosAlbum({ filePath: path });
      },
    });
  };

  const shareImg = () => {
    Taro.canvasToTempFilePath({
      canvas: state.cvs,
      fileType: "jpg",
      quality: 0.8,
      success: (res) => {
        const path = res.tempFilePath;
        Taro.showShareImageMenu({ path });
      },
    });
  };

  return (
    <View className="h-100vh bg-#F7F4EF px-50 flex flex-col items-center justify-center">
      <View
        className="w-650 h-840 bg-#fff rounded-30 overflow-hidden"
        style={{ boxShadow: "0 3rpx 3rpx 0 rgb(173, 127, 109, .4)" }}
      >
        <Canvas className="block size-full" type="2d" id="cvs" />
      </View>
      <View className="mt-80 flex gap-25">
        <View
          className="pos-relative w-300 h-70 rounded-full bg-#AD7F6D text-(34/70 #fff center) b-(1 solid #AD7F6D)"
          onClick={saveToAlbum}
        >
          保存海报到手机
        </View>
        <View
          className="pos-relative w-246 h-70 rounded-full bg-#fff text-(34/70 #AD7F6D center) b-(1 solid #AD7F6D)"
          onClick={shareImg}
        >
          <View className="pos-absolute -top-25 right-10 w-138 h-36 text-(15/30 #AD7F6D center) bg-contain bg-[url(https://oss.gaomei168.com/file-release/20250121/1331316002895179776.png)]">
            点击分享给好友
          </View>
          立即分享
        </View>
      </View>
    </View>
  );
}
