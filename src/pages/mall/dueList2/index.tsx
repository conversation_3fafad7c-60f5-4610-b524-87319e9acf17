import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { fdate, showToast } from "@/utils/tools";
import { View } from "@tarojs/components";
import Taro, { useDidShow, useReachBottom } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";
import { Tabs } from "@taroify/core";
import { useEffect, useMemo } from "react";
import { UnoIcon } from "@/components/UnoIcon";

const StateMap = [
  { id: 10, name: "待确认" },
  { id: 20, name: "待到店" },
  { id: 30, name: "已到店" },
  { id: 40, name: "已超时" },
  { id: 99, name: "已取消" },
];

export default function Page() {
  const [state, setState] = useSetState({
    shops: [] as any[],
    shopIdx: 0,
  });

  const shop = useMemo(() => {
    return state.shops?.[state.shopIdx];
  }, [state.shops, state.shopIdx]);

  const [list, listAct] = useList({
    api: CusApi.getAdminDueList,
    params: { shopId: shop?.id },
  });

  useMount(() => {
    getShops();
  });

  useEffect(() => {
    if (!shop?.id) return;
    listAct.reload();
  }, [shop?.id]);

  useDidShow(() => {
    if (!shop?.id) return;
    listAct.reload();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  const getShops = async () => {
    const res = await CusApi.getAdminShops();
    const list = res?.list || [];
    setState({ shops: list });
  };

  const handleCancel = async (item: any) => {
    const md = await Taro.showModal({ title: "确定要取消这条预约吗？" });
    if (!md.confirm) return;

    await CusApi.cancelDue({ id: item.id });
    showToast("取消成功");
    listAct.reload();
  };

  return (
    <View className="min-h-100vh bg-#F7F4EF">
      <View className="pos-sticky top-0 z-50 w-full bg-#F7F4EF">
        <Tabs
          style={
            {
              "--tab-active-color": "#8E4E36",
              "--tabs-nav-background-color": "#F7F4EF",
              "--tabs-line-background-color": "#8E4E36",
              "--tabs-line-width": "30rpx",
            } as any
          }
          value={state.shopIdx}
          onChange={(e: any) => {
            setState({ shopIdx: e });
          }}
        >
          {state.shops.map((item) => (
            <Tabs.TabPane title={item.name} key={item.id}></Tabs.TabPane>
          ))}
        </Tabs>
      </View>
      <View className="py-40 px-30">
        {list.list.map((item) => (
          <View
            className="bg-#FDFCFA rounded-10 mt-20 first:mt-0 px-35 pt-30"
            key={item.id}
          >
            <View className="flex py-8 items-center">
              <View className="text-(26 #636363) w-150">预约时间</View>
              <View className="text-(29 #11100F) flex-1 min-w-0">
                {fdate(item.preTime, "YYYY-MM-DD HH:mm") || "--"}
              </View>
            </View>

            <View className="flex py-8 items-center">
              <View className="text-(26 #636363) w-150">预约项目</View>
              <View className="text-(29 #11100F) flex-1 min-w-0">
                {item.typeName || "--"}
              </View>
            </View>

            <View className="flex py-8 items-center">
              <View className="text-(26 #636363) w-150">客户姓名</View>
              <View className="text-(29 #11100F) flex-1 min-w-0">
                {item.preName || "--"}
              </View>
            </View>

            <View className="flex py-8 items-center">
              <View className="text-(26 #636363) w-150">客户手机</View>
              <View className="text-(29 #11100F) flex-1 min-w-0">
                {item.preMobile || "--"}
              </View>
            </View>

            <View className="flex py-8 items-center">
              <View className="text-(26 #636363) w-150">预约备注</View>
              <View className="text-(29 #11100F) flex-1 min-w-0">
                {item.content || "--"}
              </View>
            </View>

            <View className="flex py-8 items-center">
              <View className="text-(26 #636363) w-150">创建时间</View>
              <View className="text-(29 #11100F) flex-1 min-w-0">
                {fdate(item.createDate, "YYYY-MM-DD HH:mm") || "--"}
              </View>
            </View>

            <View className="flex justify-end pb-40 pt-30">
              {[10, 20, 40].includes(item.state) && (
                <View
                  className="w-174 h-64 bg-#EFEFEF rounded-16 text-(26/64 center #636363) ml-15"
                  onClick={() => handleCancel(item)}
                >
                  取消
                </View>
              )}

              <View
                className={clsx(
                  "w-174 h-64 bg-#EFEFEF rounded-16 text-(26/64 center #636363) ml-15",
                  [10, 20].includes(item.state) && "!bg-#8E4E36 !text-#fff"
                )}
              >
                {StateMap.find((n) => n.id == item.state)?.name}
              </View>
            </View>
          </View>
        ))}
        <View className="text-(24 #999 center) py-30">
          {list.more && shop?.id ? "加载中..." : "没有更多数据了"}
        </View>
      </View>

      <View
        className="text-(22 #fff) flex flex-col items-center justify-center size-100 bg-#8E4E36 pos-fixed bottom-80 right-40 z-50 rounded-full"
        onClick={() => {
          Taro.navigateTo({ url: `/pages/mall/due2/index` });
        }}
      >
        <UnoIcon className="i-icon-park-outline:plus text-#fff size-50"></UnoIcon>
      </View>
    </View>
  );
}
