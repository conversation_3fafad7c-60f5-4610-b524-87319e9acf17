import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { Image, Input, Text, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { useEffect } from "react";

export default function Page() {
  const [state, setState] = useSetState({
    data: null as any,
    total: null as any,
    addr: Taro.getStorageSync("addr"),
    remark: "",
    order: null as any,
  });

  useMount(() => {
    init();
  });

  useEffect(() => {
    checkOrder();
  }, [state.data]);

  const init = async () => {
    const data = Taro.getCurrentInstance().preloadData;
    console.log("data", data);
    setState({ data });
  };

  const checkOrder = async () => {
    if (!state.data) return;

    const product = {
      marketId: state.data.data?.id,
      totalCount: state.data.num,
      serviceUserId: state.data.doctor?.id,
      shopId: state.data.shop?.id,
    };

    const res = await CusApi.checkGroupOrder({ productList: [product] });
    setState({ total: res });
  };

  const makeOrder = async () => {
    if (state.order) return state.order;

    const product = {
      marketId: state.data?.data?.id,
      totalCount: state.data?.num,
      serviceUserId: state.data?.doctor?.id,
      shopId: state.data?.shop?.id,
    };

    const params: any = {
      model: state.data?.mode,
      productList: [product],
      orderRemark: state.remark,
    };

    if (params.model == 2) {
      if (!state.addr) {
        showToast("请选择收货地址");
        throw new Error("addr empty");
      }

      params.contactName = state.addr?.userName;
      params.contactMobile = state.addr?.telNumber;
      params.contactAddress = [
        state.addr?.provinceName || "",
        state.addr?.cityName || "",
        state.addr?.countyName || "",
        state.addr?.detailInfo || "",
      ].join(" ");
    }

    const res = await CusApi.createGroupOrder(params);
    setState({ order: res });
    return res;
  };

  const onPay = async () => {
    await Taro.requestSubscribeMessage({
      tmplIds: ["TBu2utZph8Ay1ApWUct2Hr8NvgLqDHqAS7hswj75Zs4"], //  团购结果
    } as any);
    Taro.showLoading({ title: "下单中..." });

    const order = await makeOrder();

    const res = await CusApi.preOrder({
      wxAppId: process.env.TARO_APP_ID,
      orderId: order?.id,
    });

    Taro.hideLoading();

    try {
      await Taro.requestPayment({
        ...res,
        package: res.packgeStr,
      });
    } catch (err) {
      // showToast(err?.errMsg || "操作失败，请重试");
      showToast("操作失败，请重试");
      throw err;
    }

    // setState({ resOpen: true });
    const md = await Taro.showModal({
      title: "下单成功",
      showCancel: false,
      // cancelText: '返回首页',
      confirmText: "确定",
    });

    if (md.confirm) {
      Taro.navigateBack();
    }
  };

  return (
    <View className="p-30">
      {state.data?.mode == 2 && (
        <View
          className="flex items-center bg-#fff rounded-20 px-20 py-30 mb-30"
          onClick={async () => {
            const res = await Taro.chooseAddress();
            setState({ addr: res });
            Taro.setStorageSync("addr", res);
          }}
        >
          <UnoIcon className="i-icon-park-outline:local-two size-40 bg-#8E4E36 mr-20" />
          <View className="flex-1 min-w-0">
            {state.addr ? (
              <>
                <View className="text-(26 #333)">
                  {state.addr.userName} {state.addr.telNumber}
                </View>
                <View className="text-(24 #999) mt-8">
                  {state.addr.provinceName} {state.addr.cityName}{" "}
                  {state.addr.countyName} {state.addr.detailInfo}
                </View>
              </>
            ) : (
              <>
                <View className="text-(26 #999)">请选择收货地址</View>
              </>
            )}
          </View>
        </View>
      )}

      <View className="grid gap-30">
        <View className="bg-#fff rounded-20 px-20">
          <View className="flex py-20">
            <Image
              className="size-140 bg-#eee rounded-10 mr-20"
              mode="aspectFill"
              src={state.data?.data?.coverImage}
            />
            <View className="flex-1 min-w-0 flex flex-col py-8 justify-between">
              <View className="text-(28 #333) font-bold line-clamp-1">
                {state.data?.data?.name}
              </View>
              <View className="text-(24 #8E4E36) mt-5 flex items-center gap-20">
                <View>
                  {state.data?.shop?.id ? state.data?.shop?.name : ""}
                </View>
                <View>
                  {state.data?.doctor?.id ? state.data?.doctor?.name : ""}
                </View>
              </View>
              <View className="flex items-center text-(24 #666) mt-5">
                <View className="">单价：</View>
                <View className="text-(28 #8E4E36) font-bold">
                  &yen;{state.data?.data?.salePrice}
                </View>
                <View className="ml-a">x{state.data?.num}</View>
              </View>
            </View>
          </View>

          {/* <View className="b-t-(1 solid #eee)">123123</View> */}
        </View>

        <View className="bg-#fff rounded-20 px-20 flex">
          <Input
            className="flex-1 h-100 px-20 text-(26 #333)"
            placeholder="订单备注"
            value={state.remark}
            onInput={(e) => setState({ remark: e.detail.value })}
          />
        </View>
      </View>

      <View className="pos-fixed left-0 bottom-0 box-border w-full h-140 bg-#fff px-30 flex items-center">
        <View className="">
          <View className="text-(34 #333)">
            合计：&yen;{state.total?.payMoney}
          </View>
          <View className="text-(24 #8D451F) mt-5">
            <Text>共优惠：</Text>
            <Text className="line-through text-#999">
              &yen;{state.total?.preferentialMoney}
            </Text>
          </View>
        </View>
        <View
          className="ml-a h-80 w-250 rounded-10 bg-#8E4E36 text-(30/80 center #fff)"
          onClick={() => onPay()}
        >
          去支付
        </View>
      </View>

      {/* <ResModal open={state.resOpen} oid={state.order?.id} /> */}
    </View>
  );
}
