import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { Image, View } from "@tarojs/components";
import Taro, { useReachBottom } from "@tarojs/taro";
import { useMount } from "ahooks";

export default function newUser() {
  const [list, listAct] = useList({
    api: CusApi.getMarketProduct,
    params: { marketType: 50 },
  });

  useMount(() => {
    listAct.reload();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  return (
    <View className="min-h-100vh bg-#F5F1EE">
      <Image
        className="block w-full h-324"
        mode="scaleToFill"
        src="https://oss.gaomei168.com/file-release/20250301/1345342836397838336_1500_648.png"
      />
      <Image
        className="pos-relative w-299 h-96 block mx-a -mt-10"
        mode="aspectFill"
        src="https://oss.gaomei168.com/file-release/20250301/1345342835949047808_602_196.png"
      />
      <View className="-mt-56 mx-20 rounded-50 bg-#fff px-30 pt-90">
        {/* <View className="flex items-center h-60 rounded-14 b-(1 solid #7D5F49/40) text-(20 #333) bg-#EEEEEE px-30">
          <Image
            className="size-30 mr-10"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250301/1345342835743526912_66_66.png"
          />
          <Input placeholder="请输入关键字" />
        </View> */}

        <View className="grid cols-2 gap-30 pt-30">
          {list.list.map((item) => (
            <View
              key={item.id}
              style={{ borderRadius: "20rpx 20rpx 0 0" }}
              className="overflow-hidden bg-#fff"
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/mall/fxinfo/index?id=${item.id}`,
                })
              }
            >
              <View className="pt-1/1 pos-relative">
                <Image
                  className="pos-absolute top-0 left-0 size-full bg-#f5f5f5"
                  mode="aspectFill"
                  src={item.coverImage}
                />
              </View>
              <View className="px-25">
                <View className="text-(25 #11100F) py-20">
                  <View className="line-clamp-1">{item.name}</View>
                </View>
                <View className="flex items-center pb-20">
                  <View className="text-(27 #11100F) first-letter:text-(18) mr-3 font-bold">
                    &yen;{item.salePrice}
                  </View>
                  <View className="text-(14 #5F5F5F) px-5">
                    {/* {item.soldCount}人已买 */}
                  </View>
                  <View className="w-88 h-36 rounded-full bg-#8E4E36 text-(26/36 #fff center) ml-a">
                    购买
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
        <View className="text-(24 #999 center) py-50">
          {list.more ? "加载中..." : "没有更多数据了"}
        </View>
      </View>

      <View className="h-50"></View>
    </View>
  );
}
