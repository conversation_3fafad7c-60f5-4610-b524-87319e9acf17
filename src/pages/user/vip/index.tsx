import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import { Image, Swiper, SwiperItem, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";
import { useMemo } from "react";

// margin = 32
// gutter = 8
// width = 670
// source = 696 * 307
// dist = 670 / 1392 * 614 = 295.5

export const config = {
  NomalUser: {
    card: `https://oss.gaomei168.com/file-release/20250304/1346524489409368064_1392_614.png`,
    color: "#11100F",
    pointer: `https://oss.gaomei168.com/file-release/20250307/1347513352328056832_750_114.png`,
    bg: "#FFE6D2",
    color2: `linear-gradient(180deg,#F7E2D6,#FFFAF4)`,
  },
  VipUser: {
    card: `https://oss.gaomei168.com/file-release/20250304/1346524489400979456_1392_614.png`,
    color: "#CFB676",
    pointer: `https://oss.gaomei168.com/file-release/20250304/1346542700389470208_750_114.png`,
    bg: "#F5E6D1",
    color2: `linear-gradient(180deg,#F9E6BC,#FBF2E3)`,
  },
};

const icons1 = {
  10: `https://oss.gaomei168.com/file-release/20250304/1346488628789645312_82_82.png`,
  20: `https://oss.gaomei168.com/file-release/20250304/1346488628743507968_84_82.png`,
  30: `https://oss.gaomei168.com/file-release/20250304/1346488628743507968_84_82.png`,
  40: `https://oss.gaomei168.com/file-release/20250304/1346488628831588352_80_72.png`,
};

export default function Page() {
  const [state, setState] = useSetState({
    sh: 44,
    mh: 40,

    initIdx: 0,
    idx: 0,
    list: [] as any[],
  });

  useMount(() => {
    initNav();
    fetchVipLv();
  });

  const initNav = async () => {
    const res1 = await Taro.getWindowInfo();
    const res2 = await Taro.getMenuButtonBoundingClientRect();
    const sh = res1.statusBarHeight || 0;
    const mh = (res2.top - sh) * 2 + res2.height;

    setState({ sh, mh });
  };

  const crt = useMemo(() => {
    return state.list[state.idx];
  }, [state.list, state.idx]);

  const fetchVipLv = async () => {
    const res = await CusApi.getVipLv({ groupType: 1 });
    const list: any[] = res?.list || [];
    let idx = list.findIndex((n) => n.currentLevelTag);
    idx = Math.max(0, idx);

    setState({ list, idx, initIdx: idx });
  };

  const doTask = async (item) => {
    if (item.progressState == 20) return;

    if (item.type == 10 || item.type == 11) {
      Taro.navigateTo({ url: `/pages/user/invite/index` });
      return;
    }

    if (item.type == 20 || item.type == 21) {
      Taro.switchTab({ url: `/pages/tabs/cate/index` });
      return;
    }
  };

  const doBuy = async (item) => {
    const order = await CusApi.buyCard2(
      { shopVipLevelId: item.id, totalCount: 1 },
      { _loading: true }
    );

    const res = await CusApi.preOrder(
      {
        wxAppId: process.env.TARO_APP_ID,
        orderId: order.id,
      },
      { _loading: true }
    );

    Taro.requestPayment({
      ...res,
      package: res.packgeStr,
      fail: () => showToast("操作失败，请重试"),
      success: () => {
        Taro.showModal({
          title: "购买成功",
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              Taro.navigateBack();
            }
          },
        });
      },
    });
  };

  return (
    <View
      className="min-h-100vh transition-all"
      style={{
        backgroundImage: `linear-gradient(180deg,${
          config[crt?.code]?.bg || "#FFE6D2"
        },#fff 1200rpx)`,
      }}
    >
      <View>
        <View style={{ height: state.sh }}></View>
        <View className="flex" style={{ height: state.mh }}>
          <View
            className="flex-1 flex items-center pl-20"
            onClick={() => {
              Taro.navigateBack();
            }}
          >
            <UnoIcon className="i-icon-park-outline:left size-44 text-#444" />
          </View>
          <View className="flex-1 flex items-center text-(34 #11100F) fw-b">
            会员中心
          </View>
          <View className="flex-1"></View>
        </View>
      </View>

      <View className="">
        <Swiper
          nextMargin="32rpx"
          previousMargin="32rpx"
          current={state.idx}
          onChange={(e) => setState({ idx: e.detail.current })}
        >
          {state.list.map((item, idx) => {
            const cfg = config[item.code] || {};
            return (
              <SwiperItem className="w-696 h-296" key={item.id}>
                <View className="pos-relative mx-8 h-full">
                  <Image
                    className="block size-full"
                    mode="scaleToFill"
                    src={cfg.card}
                  />
                  <View className="pos-absolute top-15 left-0 w-150 h-50 text-(24/50 #fff center)">
                    {item.currentLevelTag == 1
                      ? "当前等级"
                      : state.initIdx > idx
                      ? "已超越"
                      : "暂未达成"}
                  </View>
                  <View
                    className="pos-absolute top-86 left-30 text-(34 #11100F)"
                    style={{ color: cfg.color }}
                  >
                    {item.name}
                  </View>
                  <View
                    className="pos-absolute bottom-40 left-30 text-(24 #11100F)"
                    style={{ color: cfg.color }}
                  >
                    {item.subTitle}
                  </View>
                </View>
              </SwiperItem>
            );
          })}
        </Swiper>
      </View>

      {state.list.map((item) => {
        const cfg = config[item.code] || {};

        return (
          <View
            key={item.id}
            className={clsx("hidden", crt?.code == item.code && "!block")}
          >
            <Image
              className="block h-114 w-750 mt-20"
              mode="scaleToFill"
              src={cfg.pointer}
            />

            <View className="flex items-center px-60 py-35">
              <View className="text-(36 #11100F)">等级特权</View>
              <View className="text-(24 #A0A0A0) ml-20">
                (已解锁{item.equityList?.length}项权益)
              </View>
            </View>

            <View className="grid cols-3 px-40 gap-20">
              {item.equityList?.map((sub, idx) => (
                <View
                  key={idx}
                  className="h-137 rounded-20 flex items-center justify-center flex-col bg-[linear-gradient(180deg,#F7E2D6,#FFFAF4)]"
                  style={{ backgroundImage: cfg.color2 }}
                >
                  <Image
                    className="size-42"
                    mode="aspectFit"
                    src={icons1?.[sub?.type || 10]}
                  />
                  <View className="text-(20 #11100F) mt-10">
                    {sub.mainTitle}
                  </View>
                </View>
              ))}
            </View>

            {!!item.upgradeList?.length && (
              <>
                <View className="flex items-center px-60 py-35">
                  <View className="text-(36 #11100F)">达成条件</View>
                  <View className="text-(24 #A0A0A0) ml-20">
                    (完成任一项，即可成为{item.name})
                  </View>
                </View>
                <View className="mx-40 rounded-20 bg-#F6F5F1 overflow-hidden">
                  {item.upgradeList.map((sub, idx) => (
                    <View
                      key={idx}
                      className="h-134 b-t-(1 solid #EAE9E7) first:b-0 flex items-center mx-40"
                      onClick={() => doTask(sub)}
                    >
                      <View className="text-(30 #626262)">{sub.mainTitle}</View>
                      <View className="flex-1 text-(22 #7D8CA1) px-15">
                        {!!sub.subTitle && <>({sub.subTitle})</>}
                      </View>

                      {sub.progressState == 20 ? (
                        <View className="w-114 h-44 rounded-full bg-#8A8A8A text-(24/44 #fff center)">
                          已完成
                        </View>
                      ) : (
                        <View className="w-114 h-44 rounded-full bg-#7C664D text-(24/44 #fff center)">
                          去完成
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              </>
            )}

            {!!item.delayList?.length && (
              <>
                <View className="flex items-center px-60 py-35">
                  <View className="text-(36 #11100F)">保级条件</View>
                  <View className="text-(24 #A0A0A0) ml-20">
                    (完成任一项，即可保级{item.name})
                  </View>
                </View>
                <View className="mx-40 rounded-20 bg-#F6F5F1 overflow-hidden">
                  {item.delayList.map((sub, idx) => (
                    <View
                      key={idx}
                      className="h-134 b-t-(1 solid #EAE9E7) first:b-0 flex items-center mx-40"
                      onClick={() => doTask(sub)}
                    >
                      <View className="text-(30 #626262)">{sub.mainTitle}</View>
                      <View className="flex-1 text-(22 #7D8CA1) px-15">
                        {!!sub.subTitle && <>({sub.subTitle})</>}
                      </View>

                      {sub.progressState == 20 ? (
                        <View className="w-114 h-44 rounded-full bg-#8A8A8A text-(24/44 #fff center)">
                          已完成
                        </View>
                      ) : (
                        <View className="w-114 h-44 rounded-full bg-#7C664D text-(24/44 #fff center)">
                          去完成
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              </>
            )}

            {!!item.levelBuy && (
              <View className="z-20 pos-fixed left-0 bottom-0 w-full bg-#fff pt-30">
                <View
                  className="pos-relative w-612 h-88 mx-auto flex items-center justify-center bg-no-repeat bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250304/1346486251969843200_1224_176.png)]"
                  onClick={() => doBuy(item.levelBuy)}
                >
                  <View className="text-(38 #E5BE85) mr-30 fw-bold first-letter:mr-5">
                    &yen;{item.levelBuy.cardPrice}/年
                  </View>
                  <View className="text-(38 #E5BE85)">
                    {item.levelBuy.subTitle}
                  </View>
                  <View className="pos-absolute -top-23 right-0 w-175 h-32 text-(20/32 #11100F center) bg-no-repeat bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250304/1346487811655340032_350_64.png)]">
                    首年特价
                  </View>
                </View>
                <View
                  style={{
                    height: `env(safe-area-inset-bottom)`,
                    minHeight: "30rpx",
                  }}
                />
              </View>
            )}
          </View>
        );
      })}

      <View className="h-100"></View>
    </View>
  );
}
