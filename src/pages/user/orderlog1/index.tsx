import { Image, View } from "@tarojs/components";
import Style from "./index.module.scss";
import { getOrderList } from "@/services/MallApi";
import useFetchList from "@/utils/usefetchList";
import { useMount } from "ahooks";
import Taro, { usePullDownRefresh, useReachBottom } from "@tarojs/taro";
import dayjs from "dayjs";
import Watermark from "@/components/Watermark";
import CusApi from "@/services/CusApi";

const StateMap = [
  { id: 0, name: "待付款" },
  { id: 10, name: "已付款" },
  { id: 20, name: "已接单" },
  { id: 30, name: "待收货" },
  { id: 40, name: "已完成" },
  { id: 70, name: "退款确认中" },
  { id: 80, name: "已退款" },
  { id: 99, name: "交易关闭" },
];

const OrderList = () => {
  const [list, listAct] = useFetchList({
    api: CusApi.getOrderList,
    params: { property: 1 },
  });

  useMount(() => {
    listAct.getList();
  });

  usePullDownRefresh(async () => {
    await listAct.getList();
    Taro.stopPullDownRefresh();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  return (
    <View>
      <View className={Style.list}>
        {list.list.map((item) => (
          <View className={Style.item} key={item.id}>
            <View className={Style.head}>
              <View className={Style.tit}>订单号：{item.serialNo}</View>
              <View className={Style.status}>
                {StateMap.find((n) => n.id == item.orderState)?.name}
              </View>
            </View>

            <View className={Style.body}>
              <Image
                className={Style.cover}
                src={item.productCoverImage}
                mode="aspectFill"
              />
              <View className={Style.cont}>
                <View className={Style.title}>{item.productName}</View>
                <View className={Style.ft}>
                  <View className={Style.price}>&yen;{item.payMoney}</View>
                  <View className={Style.num}>
                    包含次数: x{item.includeNum}
                  </View>
                </View>
              </View>
            </View>
            <View className={Style.foot}>
              <View className={Style.date}>
                {dayjs(item.createDate).format("YYYY-MM-DD HH:mm:ss")}
              </View>
              {/* <View className={Style.btns}>
                <View className={Style.btn}>按钮</View>
                <View className={Style.btn}>按钮</View>
              </View> */}
            </View>
          </View>
        ))}
      </View>
      <Watermark />
    </View>
  );
};

export default OrderList;
