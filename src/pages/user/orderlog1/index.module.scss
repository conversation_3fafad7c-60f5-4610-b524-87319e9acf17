.list {
  padding: 30px;
}

.item {
  background: #fff;
  border-radius: 30px;
  margin-bottom: 30px;

  .head {
    border-bottom: 1px solid #eee;
    font-size: 46px;
    color: #000;
    display: flex;
    align-items: center;
    padding: 30px;

    .status {
      margin-left: auto;
      font-weight: bold;
      color: #d0ab6b;
    }
  }

  .body {
    display: flex;
    padding: 30px;
    align-items: center;

    .cover {
      flex-shrink: 0;
      width: 220px;
      height: 220px;
      background: #f5f5f5;
      margin-right: 30px;
      border-radius: 20px;
      overflow: hidden;
    }

    .cont {
      min-width: 0;
      flex: 1;
    }

    .title {
      font-size: 46px;
      height: 140px;
      line-height: 70px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .ft {
      display: flex;
      align-items: center;
      margin-top: 20px;
    }

    .num {
      margin-left: auto;
      font-size: 40px;
      color: #aaa;
    }
    .price {
      font-size: 50px;
      color: #d0ab6b;
      font-weight: bold;
      &::first-letter {
        font-size: 0.8em;
      }
    }
  }

  .foot {
    border-top: 1px solid #eee;
    display: flex;
    align-items: center;
    padding: 30px;

    .date {
      font-size: 40px;
      color: #aaa;
    }

    .btns {
      margin-left: auto;
      display: flex;
    }

    .btn {
      width: 220px;
      height: 80px;
      line-height: 80px;
      text-align: center;
      border: 1px solid #aaa;
      color: #aaa;
      font-size: 40px;
      margin-left: 30px;
      border-radius: 20px;
    }
  }
}
