import { CusPopup } from "@/components/Custom/PopUp";
import RichContent from "@/components/RichContent";
import { UnoIcon } from "@/components/UnoIcon";
import { usePageStat } from "@/stores/usePageStat";
import { useShare } from "@/stores/useShare";
import { makeApi } from "@/utils/request";
import { Button, Image, View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";

const getRule = makeApi("get", `/api/mallShopVipLevel/query_rule`);

export default function Page() {
  const [state, setState] = useSetState({
    ruleOpen: false,
    rule: null as any,

    isNew: false as any,
  });

  usePageStat({
    id: "",
    title: "领取老带新",
  });

  useShare(() => {
    return {
      title: "参与美力搭子计划",
      imageUrl: `https://oss.gaomei168.com/file-release/20250306/1347168720113111040_650_556.jpg`,
      path: `/pages/user/invite2/index`,
    };
  });

  useDidShow(() => {
    const isNew = !Taro.getStorageSync("token");
    setState({ isNew });
  });

  useMount(() => {
    fetchRule();
  });

  const fetchRule = async () => {
    const rule = await getRule();
    setState({ rule });
  };

  return (
    <View className="min-h-100vh bg-#F2E8DA">
      <Image
        className="w-620 h-180 block mx-a"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250307/1347516092982759424_617_176.png"
      />

      <Image
        className="w-730 h-550 block mx-a mt-20"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250222/1342913131929051136.png"
      />

      {state.isNew ? (
        <View>
          <View
            className="bg-#F6EEE4 rounded-22 mx-40 mt-40"
            style={{ boxShadow: "0 3rpx 6rpx rgba(0,0,0,.2)" }}
          >
            <View className="h-510 flex flex-col items-center justify-center">
              <View className="text-(32 #11100F) fw-bold">
                您的好友送您专属新人礼包
              </View>
              <Image
                className="w-324 h-319 mt-30 mb-15"
                mode="aspectFit"
                src="https://oss.gaomei168.com/file-release/20250306/1347201826706690048_648_638.png"
              />
              <View className="text-(22 #11100F)">*新用户专属福利</View>
            </View>
          </View>

          <View
            className="pos-relative w-430 h-80 mx-a mt-45 text-(27/80 #fff center) rounded-full bg-#B7684A"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/mall/login/index` });
            }}
          >
            立即登录领取
          </View>
        </View>
      ) : (
        <View>
          <View
            className="bg-#F6EEE3 rounded-22 mx-40 mt-30 py-45"
            style={{ boxShadow: "0 3rpx 6rpx rgba(0,0,0,.2)" }}
          >
            <View className="text-(32 #A56F53 center) fw-bold mb-20">
              老朋友 带新才有惊喜哦
            </View>
            <View className="text-(26 #706F70 center) mt-15 underline">
              新人奖励仅针对未注册用户
            </View>
            <View className="text-(26 #706F70 center) mt-15 underline">
              但邀请好友你也能得好礼，快来参与吧
            </View>
          </View>

          <View
            className="bg-#F6EEE3 rounded-22 mx-40 mt-30"
            style={{ boxShadow: "0 3rpx 6rpx rgba(0,0,0,.2)" }}
          >
            <View
              className="flex items-center px-20 py-30"
              onClick={() => setState({ ruleOpen: true })}
            >
              <View className="text-(25 #11100F) flex-1">参与步骤</View>
              <View className="text-(21 #11100F)">详细规则</View>
              <UnoIcon className="i-icon-park-outline:right text-(26 #11100F)" />
            </View>

            <View className="flex items-center pb-50 pt-10">
              <View className="flex flex-col flex-1 items-center">
                <Image
                  className="w-62 h-52 mb-15"
                  mode="aspectFit"
                  src="https://oss.gaomei168.com/file-release/20250222/1342904053785862144.png"
                />
                <View className="text-(18 #11100F center)">分享链接</View>
                <View className="text-(18 #11100F center)">给好友</View>
              </View>

              <Image
                className="size-10"
                mode="aspectFit"
                src="https://oss.gaomei168.com/file-release/20250222/1342904053513232385.png"
              />

              <View className="flex flex-col flex-1 items-center justify-center">
                <Image
                  className="w-62 h-52 mb-15"
                  mode="aspectFit"
                  src="https://oss.gaomei168.com/file-release/20250222/1342904053513232384.png"
                />
                <View className="text-(18 #11100F center)">好友注册</View>
                <View className="text-(18 #11100F center)">赠送专属福利</View>
              </View>

              <Image
                className="size-10"
                mode="aspectFit"
                src="https://oss.gaomei168.com/file-release/20250222/1342904053513232385.png"
              />

              <View className="flex flex-col flex-1 items-center">
                <Image
                  className="w-62 h-52 mb-15"
                  mode="aspectFit"
                  src="https://oss.gaomei168.com/file-release/20250222/1342904053584535552.png"
                />
                <View className="text-(18 #11100F center)">好友</View>
                <View className="text-(18 #11100F center)">到院体验</View>
              </View>

              <Image
                className="size-10"
                mode="aspectFit"
                src="https://oss.gaomei168.com/file-release/20250222/1342904053513232385.png"
              />

              <View className="flex flex-col flex-1 items-center">
                <Image
                  className="w-62 h-52 mb-15"
                  mode="aspectFit"
                  src="https://oss.gaomei168.com/file-release/20250222/1342904053571952641.png"
                />
                <View className="text-(18 #11100F center)">邀请达成</View>
                <View className="text-(18 #11100F center)">各送200现金券</View>
              </View>
            </View>
          </View>

          <View className="pos-relative w-430 h-80 mx-a mt-45 text-(27/80 #fff center) rounded-full bg-#B7684A">
            邀请好友参与
            <Button
              openType="share"
              className="pos-absolute top-0 let-0 size-full opacity-0"
            ></Button>
          </View>
        </View>
      )}

      <View className="h-80"></View>

      <CusPopup
        open={state.ruleOpen}
        onClose={() => setState({ ruleOpen: false })}
        title="详细规则"
      >
        <View className="px-60 pt-10 pb-100 max-h-60vh overflow-y-auto">
          <RichContent html={state.rule?.newbieInviteRuleContent} />
        </View>
      </CusPopup>
    </View>
  );
}
