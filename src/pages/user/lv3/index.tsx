import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { Image, View } from "@tarojs/components";
import Taro, { useReachBottom, useRouter } from "@tarojs/taro";
import { useEffect } from "react";

export default function newUser() {
  const router = useRouter();

  const [list, listAct] = useList({
    api: CusApi.getGoods,
    params: {
      property: 2,
      // shopCategoryId: 1364908321253437440,
      shopCategoryId: router.params.id,
    },
  });

  useEffect(() => {
    listAct.reload();
  }, [router.params.id]);

  useReachBottom(() => {
    listAct.getNext();
  });

  return (
    <View className="min-h-100vh bg-#F5F1EE">
      {/* <Image
        className="block w-full h-a"
        mode="widthFix"
        src={state.data?.cover?.[0]}
      /> */}

      <View className="px-30">
        <View className="grid cols-2 gap-30 pt-50">
          {list.list.map((item) => (
            <View
              key={item.id}
              style={{ borderRadius: "20rpx 20rpx 0 0" }}
              className="overflow-hidden bg-#fff"
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/mall2/info/index?id=${item.id}`,
                })
              }
            >
              <View className="pt-1/1 pos-relative">
                <Image
                  className="pos-absolute top-0 left-0 size-full bg-#f5f5f5"
                  mode="aspectFill"
                  src={item.coverImage}
                />
              </View>
              <View className="px-25">
                <View className="text-(25 #11100F) py-20">
                  <View className="line-clamp-1">{item.name}</View>
                </View>
                <View className="flex items-center pb-20">
                  <View className="text-(27 #11100F) first-letter:text-(18) mr-3 font-bold">
                    &yen;{item.priceSensitiveTag ? "???" : item.salePrice}
                  </View>
                  {item.productSpecificationNum > 1 && (
                    <View className="text-(22 #5F5F5F) px-5">起</View>
                  )}
                  <View className="w-88 h-36 rounded-full bg-#8E4E36 text-(26/36 #fff center) ml-a">
                    购买
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
        <View className="text-(24 #999 center) py-50">
          {list.more ? "加载中..." : "没有更多数据了"}
        </View>
      </View>

      <View className="h-50"></View>
    </View>
  );
}
