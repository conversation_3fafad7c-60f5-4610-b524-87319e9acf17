import { View } from "@tarojs/components";
import Style from "./index.module.scss";
import CusApi from "@/services/CusApi";
import { useSetState } from "ahooks";
import React, { useEffect } from "react";
import Taro, {
  usePullDownRefresh,
  useReachBottom,
  useRouter,
} from "@tarojs/taro";
import { privatePhone } from "@/utils/tools";

const InvestorShop = () => {
  const router = useRouter();
  const [state, setState] = useSetState({
    list: [] as any[],
    page: 1,
    size: 20,
    more: true,
    loading: true,
  });

  useEffect(() => {
    fetchList(1);
  }, []);

  usePullDownRefresh(async () => {
    await fetchList(1);
    Taro.stopPullDownRefresh();
  });

  useReachBottom(() => {
    fetchList(state.page + 1);
  });

  const fetchList = async (page) => {
    if (page == 1) {
      setState({ page: 1, more: true, list: [] });
    } else if (!state.more || state.loading) {
      return;
    }

    setState({ loading: true });

    const params = {
      pageNum: page,
      pageSize: state.size,
      shopId: router.params.id,
    };

    const res = await CusApi.getInvestorUser(params).finally(() =>
      setState({ loading: false })
    );

    setState((prev) => ({
      more: res.hasNextPage,
      page: res.pageNum,
      size: res.pageSize,
      list: [...prev.list, ...(res?.list || [])],
    }));
  };

  return (
    <View className={Style.page}>
      <View className="grid cols-3 gap-1">
        <View className="text-(30 #333 center) p-20 fw-bold bg-#fff">姓名</View>
        <View className="text-(30 #333 center) p-20 fw-bold bg-#fff">电话</View>
        <View className="text-(30 #333 center) p-20 fw-bold bg-#fff">占比</View>
        {state.list.map((item) => (
          <React.Fragment key={item.id}>
            <View className="text-(26 #333 center) p-15 bg-#fff">
              {item.investorUserName}
            </View>
            <View className="text-(26 #333 center) p-15 bg-#fff">
              {privatePhone(item.investorUserMobile)}
            </View>
            <View className="text-(26 #333 center) p-15 bg-#fff">
              {Math.round((item.investorRate || 0) * 10000) / 100 + "%"}
            </View>
          </React.Fragment>
        ))}
      </View>

      <View className={Style.footer}>
        {state.more ? "加载中..." : "没有更多数据了..."}
      </View>
    </View>
  );
};

export default InvestorShop;
