import { CusPopup } from "@/components/Custom/PopUp";
import RichContent from "@/components/RichContent";
import { UnoIcon } from "@/components/UnoIcon";
import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { usePageStat } from "@/stores/usePageStat";
import { useShare } from "@/stores/useShare";
import { img_avatar } from "@/utils/images";
import { makeApi } from "@/utils/request";
import { fdate } from "@/utils/tools";
import Button from "@taroify/core/button/button";
import { Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";

const getRule = makeApi("get", `/api/mallShopVipLevel/query_rule`);

export default function Page() {
  const [state, setState] = useSetState({
    ruleOpen: false,
    rule: null as any,
  });

  const [list, listAct] = useList({
    api: CusApi.getRecommend,
    params: { recommendType: 1 },
  });

  usePageStat({
    id: "",
    title: "分享老带新",
  });

  useMount(() => {
    listAct.reload();
    fetchRule();
  });

  useShare(() => {
    return {
      title: "参与美力搭子计划",
      imageUrl: `https://oss.gaomei168.com/file-release/20250306/1347168720113111040_650_556.jpg`,
      path: `/pages/user/invite2/index`,
    };
  });

  const fetchRule = async () => {
    const rule = await getRule();
    setState({ rule });
  };

  return (
    <View className="min-h-100vh bg-#F5EDE2">
      <Image
        className="w-620 h-180 block mx-a"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250307/1347516092982759424_617_176.png"
      />

      <Image
        className="w-730 h-550 block mx-a mt-20"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250222/1342913131929051136.png"
      />
      <View className="flex items-end justify-between mt-20 px-60">
        <Image
          className="w-295 h-88"
          mode="aspectFit"
          src="https://oss.gaomei168.com/file-release/20250222/1342913131635449856.png"
          onClick={() =>
            Taro.navigateTo({ url: `/pages/mall/fxposter/index?type=dazi` })
          }
        />
        <View className="pos-relative w-295 h-104">
          <Image
            className="pos-absolute top-0 left-0 w-295 h-104"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250222/1342913131635449857.png"
          />
          <Button
            openType="share"
            className="pos-absolute top-0 left-0 size-full opacity-0"
          ></Button>
        </View>
      </View>

      <View
        className="bg-#F6EEE3 rounded-22 mx-40 mt-20"
        style={{ boxShadow: "0 3rpx 6rpx rgba(0,0,0,.2)" }}
      >
        <View
          className="flex items-center px-20 py-30"
          onClick={() => setState({ ruleOpen: true })}
        >
          <View className="text-(25 #11100F) flex-1">参与步骤</View>
          <View className="text-(21 #11100F)">详细规则</View>
          <UnoIcon className="i-icon-park-outline:right text-(26 #11100F)" />
        </View>

        <View className="flex items-center pb-50 pt-10">
          <View className="flex flex-col flex-1 items-center">
            <Image
              className="w-62 h-52 mb-15"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250222/1342904053785862144.png"
            />
            <View className="text-(18 #11100F center)">分享链接</View>
            <View className="text-(18 #11100F center)">给好友</View>
          </View>

          <Image
            className="size-10"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250222/1342904053513232385.png"
          />

          <View className="flex flex-col flex-1 items-center justify-center">
            <Image
              className="w-62 h-52 mb-15"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250222/1342904053513232384.png"
            />
            <View className="text-(18 #11100F center)">好友注册</View>
            <View className="text-(18 #11100F center)">赠送专属福利</View>
          </View>

          <Image
            className="size-10"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250222/1342904053513232385.png"
          />

          <View className="flex flex-col flex-1 items-center">
            <Image
              className="w-62 h-52 mb-15"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250222/1342904053584535552.png"
            />
            <View className="text-(18 #11100F center)">好友</View>
            <View className="text-(18 #11100F center)">到院体验</View>
          </View>

          <Image
            className="size-10"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250222/1342904053513232385.png"
          />

          <View className="flex flex-col flex-1 items-center">
            <Image
              className="w-62 h-52 mb-15"
              mode="aspectFit"
              src="https://oss.gaomei168.com/file-release/20250222/1342904053571952641.png"
            />
            <View className="text-(18 #11100F center)">邀请达成</View>
            <View className="text-(18 #11100F center)">各送200现金券</View>
          </View>
        </View>
      </View>

      <View
        className="bg-#F6EEE3 rounded-22 mx-40 mt-20"
        style={{ boxShadow: "0 3rpx 6rpx rgba(0,0,0,.2)" }}
      >
        <View className="flex items-center px-20 py-30">
          <View className="text-(25 #11100F)">邀请记录</View>
          <View className="text-(20 #5E5E5E)">（{list.total}人）</View>
          <View
            className="text-(21 #11100F) ml-a"
            onClick={() => Taro.navigateTo({ url: `/pages/user/coupon/index` })}
          >
            我的奖励
          </View>
          <UnoIcon className="i-icon-park-outline:right text-(26 #11100F)" />
        </View>
        <View>
          {list.list.map((item) => (
            <View className="flex items-center px-30 py-20" key={item.id}>
              <Image
                className="size-50 bg-#BFBFBF rounded-full overflow-hidden"
                mode="aspectFill"
                src={item.photo || img_avatar}
              />
              <View className="flex-1 mx-10 text-(23 #3D3D3D) line-clamp-1">
                {item.name}
              </View>
              <View className="px-30 text-(23 #955942)">
                {item.lastServiceTime ? "已到院" : "未到院"}
              </View>
              <View className="text-(21 #5E5E5E)">
                邀请时间：{fdate(item.createDate, "YYYY-MM-DD")}
              </View>
            </View>
          ))}
          {list.total > 20 && (
            <View
              className="text-(21 #999 center) py-20"
              onClick={() => {
                Taro.navigateTo({ url: `/pages/user/presenter/index` });
              }}
            >
              查看更多&gt;
            </View>
          )}
        </View>
      </View>

      <View className="h-30"></View>

      <CusPopup
        open={state.ruleOpen}
        onClose={() => setState({ ruleOpen: false })}
        title="详细规则"
      >
        <View className="px-60 pt-10 pb-100 max-h-60vh overflow-y-auto">
          <RichContent html={state.rule?.newbieInviteRuleContent} />
        </View>
      </CusPopup>
    </View>
  );
}
