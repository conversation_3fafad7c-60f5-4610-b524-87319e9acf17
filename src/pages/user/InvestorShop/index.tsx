import { View } from "@tarojs/components";
import Style from "./index.module.scss";
import clsx from "clsx";
import { useMount, useSetState } from "ahooks";
import CusApi from "@/services/CusApi";
import Taro from "@tarojs/taro";

const InvestorShop = () => {
  const [state, setState] = useSetState({
    shops: [] as any[],
  });

  useMount(() => {
    fetchShops();
  });

  const fetchShops = async () => {
    const res = await CusApi.getInvestorShop();
    setState({ shops: res });
  };

  return (
    <View className={Style.page}>
      {state.shops.map((item) => (
        <View className={Style.item} key={item.shopId}>
          <View className={Style.head}>
            <View className={Style.ico}></View>
            <View className={Style.name}>{item.shopName}</View>
          </View>
          <View className={Style.body}>
            <View
              className={Style.unit}
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/user/InvestorUser/index?id=${item.shopId}`,
                })
              }
            >
              <View className={clsx(Style.ico, Style.ico1)}></View>
              <View className={Style.name}>门店股东</View>
            </View>
            <View
              className={Style.unit}
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/user/InvestorIncome/index?id=${item.shopId}`,
                })
              }
            >
              <View className={clsx(Style.ico, Style.ico2)}></View>
              <View className={Style.name}>门店收益</View>
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

export default InvestorShop;
