import { useMount } from "ahooks";
import { Image, View } from "@tarojs/components";
import Style from "./index.module.scss";
import CusApi from "@/services/CusApi";
import { useList } from "@/stores/useList";
import { useReachBottom } from "@tarojs/taro";
import { fdate } from "@/utils/tools";
import { img_avatar } from "@/utils/images";

const Presenter = () => {
  // const [tab, setTab] = useState<number>(1);
  // const [list, setList] = useState([]);
  // const { loading, runAsync } = useRequest(CusApi.getRecommend, {
  //   manual: true,
  // });

  // useEffect(() => {
  //   runAsync({ recommendType: tab }).then((e) => {
  //     setList(e?.list);
  //   });
  // }, [tab]);

  const [list, listAct] = useList({
    api: CusApi.getRecommend,
    params: { recommendType: 1 },
  });

  useMount(() => {
    listAct.reload();
  });

  useReachBottom(() => {
    listAct.getList();
  });

  return (
    <View>
      {/* <View className={Style.tabs}>
        <View
          onClick={() => {
            setTab(1);
          }}
          className={`${tab == 1 ? Style.tabsname2 : Style.tabsname}`}
        >
          直推
        </View>
        <View
          onClick={() => {
            setTab(2);
          }}
          className={`${tab == 2 ? Style.tabsname2 : Style.tabsname}`}
        >
          间推
        </View>
      </View> */}

      <View className="py-20 text-(24 #999) px-30 bg-#f5f5f5 pos-sticky top-0 z-50">
        总推荐: {list.total}人
      </View>

      <View className={Style.box}>
        {list.list?.map((item: any) => (
          <View className="flex items-center px-30 py-20" key={item.id}>
            <Image
              className="size-50 bg-#BFBFBF rounded-full overflow-hidden"
              mode="aspectFill"
              src={item.photo || img_avatar}
            />
            <View className="flex-1 mx-10 text-(23 #3D3D3D) line-clamp-1">
              {item.name}
            </View>
            <View className="px-30 text-(23 #955942)">
              {item.lastServiceTime ? "已到院" : "未到院"}
            </View>
            <View className="text-(21 #5E5E5E)">
              邀请时间：{fdate(item.createDate, "YYYY-MM-DD")}
            </View>
          </View>
        ))}
      </View>

      <View className={Style.footer}>
        {list.more ? "加载中..." : "没有更多数据了"}
      </View>
    </View>
  );
};

export default Presenter;
