.tabs {
  width: 100%;
  display: flex;
  flex-direction: row;

  .tabsname {
    flex: 1;
    height: 137px;
    font-size: 45px;
    text-align: center;
    line-height: 137px;
    background-color: #ffffff;

  }

  .tabsname2 {
    flex: 1;
    height: 137px;
    font-size: 45px;
    text-align: center;
    line-height: 137px;
    background-color: #ffffff;
    position: relative;
    color: rgba(213, 178, 128, 1);

    &::after {
      content: " ";
      width: 87px;
      height: 5px;
      background: rgba(213, 178, 128, 1);
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }

  }
}

.catalogue {
  background-color: #ffffff;
  padding: 70px;
  margin-top: 1px;

  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 45px;
  }
}

.catalogue:nth-child(1) {
  margin-top: 28px;
}

.footer {
  padding: 60px 0;
  text-align: center;
  font-size: 34px;
  color: #999;
}
