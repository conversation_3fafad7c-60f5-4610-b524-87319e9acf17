import { UnoIcon } from "@/components/UnoIcon";
import { getUserByToken } from "@/services/UserApi";
import { img_avatar } from "@/utils/images";
import { Image, View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useSetState } from "ahooks";
import clsx from "clsx";

import { Likes, Updates } from "./comp";
import { Poster } from "./poster";

const tabs = [
  { id: 0, name: "动态" },
  { id: 1, name: "赞过" },
];

export default function Page() {
  const [state, setState] = useSetState({
    user: null as any,
    idx: 0,

    postOpen: false,
    postKey: 1,
  });

  useDidShow(() => {
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await getUserByToken();
    setState({ user });
  };

  return (
    <View className="min-h-100vh bg-#F8F6F5">
      <View className="pt-30 bg-#F5F1EE">
        <View className="flex items-center px-30">
          <Image
            className="size-84 bg-#eee rounded-full overflow-hidden mr-30"
            mode="aspectFill"
            src={state.user?.photo || img_avatar}
          />
          <View className="text-(26 #1A1A1A) fw-bold">{state.user?.name}</View>
        </View>
        <View className="px-30 py-35 text-(25 #1A1A1A)">
          {state.user?.signature || "这个人很神秘，什么都没留下"}
        </View>

        <View className="flex py-70 pr-30">
          <View
            className="text-center px-35"
            onClick={() => {
              const uid = state.user?.id;
              Taro.navigateTo({
                url: `/pages/user/userRelation/index?name=${state.user?.name}&id=${uid}&idx=0`,
              });
            }}
          >
            <View className="text-(28 #11100F) fw-bold">
              {state.user?.followCount}
            </View>
            <View className="text-(22 #11100F) mt-5">关注</View>
          </View>
          <View
            className="text-center px-35"
            onClick={() => {
              const uid = state.user?.id;
              Taro.navigateTo({
                url: `/pages/user/userRelation/index?name=${state.user?.name}&id=${uid}&idx=1`,
              });
            }}
          >
            <View className="text-(28 #11100F) fw-bold">
              {state.user?.fansCount}
            </View>
            <View className="text-(22 #11100F) mt-5">粉丝</View>
          </View>
          <View className="text-center px-35">
            <View className="text-(28 #11100F) fw-bold">
              {state.user?.likeCount}
            </View>
            <View className="text-(22 #11100F) mt-5">获赞</View>
          </View>

          <View
            className="h-54 w-170 rounded-10 b-(1 solid #5A5A5A) ml-a text-(24/54 #5A5A5A center)"
            onClick={() => {
              Taro.navigateTo({ url: `/pages/user/profile/index` });
            }}
          >
            编辑资料
          </View>
        </View>

        <View className="h-40 -mb-40"></View>
      </View>

      <View
        className="bg-#F8F6F5 overflow-hidden"
        style={{ borderRadius: "30rpx 30rpx 0 0" }}
      >
        <View className="flex items-center justify-center pt-60 pb-35">
          {tabs.map((item) => (
            <View
              className="px-65"
              key={item.id}
              onClick={() => setState({ idx: item.id })}
            >
              <View
                className={clsx(
                  "text-(28 #11100F) fw-bold",
                  state.idx == item.id && "!text-#8E4E36"
                )}
              >
                {item.name}
              </View>
              <View
                className={clsx(
                  "w-25 h-5 bg-#8E4E36 mx-a mt-5",
                  state.idx !== item.id && "invisible"
                )}
              ></View>
            </View>
          ))}
        </View>

        {state.idx == 0 && <Updates key={state.postKey} />}
        {state.idx == 1 && !!state.user?.id && <Likes uid={state.user?.id} />}
      </View>

      {/* <View
        className="pos-fixed right-35 bottom-70 size-99 rounded-full bg-#8E4E36 flex items-center justify-center"
        onClick={() => setState({ postOpen: true })}
      >
        <UnoIcon className="i-icon-park-outline:plus text-(70 #fff)" />
      </View> */}

      <Poster
        open={state.postOpen}
        onClose={() => setState({ postOpen: false })}
        onOk={() => setState({ postKey: state.postKey + 1 })}
      />
    </View>
  );
}
