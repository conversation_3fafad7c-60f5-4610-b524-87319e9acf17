import { UnoIcon } from "@/components/UnoIcon";
import { useList } from "@/stores/useList";
import { img_avatar } from "@/utils/images";
import { makeApi } from "@/utils/request";
import { showToast, str2arr } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import Taro, { useReachBottom } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { Poster } from "./poster";
import CusApi from "@/services/CusApi";

const getMylist = makeApi("get", `/api/appInformationArticle/query_list`);

const UpdateState = [
  { id: 0, name: "草稿" },
  { id: 10, name: "待审核" },
  { id: 20, name: "已通过" },
  { id: 21, name: "未通过" },
];

export const Updates = () => {
  const [state, setState] = useSetState({
    open: false,
    data: null as any,
  });

  const [list, listAct] = useList({
    api: getMylist,
    params: { type: 10 },
  });

  useMount(() => {
    listAct.getList();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  const onDel = async (item) => {
    const md = await Taro.showModal({
      title: "确定要删除这条数据吗？",
      content: item.title,
    });
    if (!md.confirm) return;
    Taro.showLoading({ title: "加载中...", mask: true });
    await CusApi.delNote({ ids: item.id }).finally(() => Taro.hideLoading());
    listAct.reload();
    showToast("删除成功");
  };

  return (
    <View>
      <View className="grid cols-2 gap-20 px-20">
        {list.list.map((item) => {
          let _imgs = str2arr(item.images);
          let _cover = _imgs?.[0] || item.videoCoverImage;
          _cover += "?x-oss-process=image/resize,w_750,limit_0";
          return (
            <View
              className=""
              key={item.id}
              onClick={() => {
                if (item.model == 2) {
                  Taro.navigateTo({
                    url: `/pages/mall2/noteinfo2/index?id=${item.id}`,
                  });
                } else {
                  Taro.navigateTo({
                    url: `/pages/mall2/noteinfo/index?id=${item.id}`,
                  });
                }
              }}
            >
              <View className="pos-relative">
                <Image
                  className="block h-480 w-full bg-#fff"
                  mode="aspectFit"
                  src={_cover}
                />

                <View
                  className="pos-absolute top-20 left-0 w-full box-border px-20 flex items-center gap-20"
                  onClick={(e) => e.stopPropagation()}
                >
                  <View
                    className="p-10 rounded-full bg-#000/30"
                    onClick={() => setState({ open: true, data: item })}
                  >
                    <UnoIcon className="i-icon-park-outline:edit-two text-(30 #fff)" />
                  </View>
                  <View
                    className="p-10 rounded-full bg-#000/30"
                    onClick={() => onDel(item)}
                  >
                    <UnoIcon className="i-icon-park-outline:delete-four text-(30 #fff)" />
                  </View>
                  <View className="ml-a rounded-10 bg-#000/30  px-14 py-5 text-(#fff 20 center)">
                    {UpdateState.find((n) => n.id == item.state)?.name}
                  </View>
                </View>
              </View>

              <View className="bg-#fff px-20">
                <View className="py-20">
                  <View className="text-(30/48 #11100F) h-96 line-clamp-2">
                    {item.title}
                  </View>
                </View>
                <View className="flex items-center pb-25">
                  <Image
                    className="size-40 rounded-full overflow-hidden bg-#eee mr-10"
                    mode="aspectFill"
                    src={item.appUser?.photo || img_avatar}
                  />
                  <View className="flex-1 text-(22 #666)">
                    {item.appUser?.name}
                  </View>
                  {/* <UnoIcon className="i-icon-park-outline:like size-40 c-#666 mr-5" /> */}

                  {item.likesTag ? (
                    <UnoIcon className="i-icon-park-solid:like size-40 c-#8E4E36 mr-5" />
                  ) : (
                    <UnoIcon className="i-icon-park-outline:like size-40 c-#666 mr-5" />
                  )}
                  <View className="text-(26 #666)">{item.likesNum}</View>
                </View>
              </View>
            </View>
          );
        })}
      </View>

      <View className="text-(24 #999 center) pt-30 pb-100">没有更多数据了</View>

      <Poster
        open={state.open}
        onClose={() => setState({ open: false })}
        data={state.data}
        onOk={() => listAct.reload()}
      />
    </View>
  );
};

const getList = makeApi("get", `/api/appInformationArticle/web/query_list`);

export const Likes = (props: { uid: any }) => {
  const [list, listAct] = useList({
    api: getList,
    params: { type: 10, likesUserId: props.uid },
  });

  useMount(() => {
    listAct.getList();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  return (
    <View>
      <View className="grid cols-2 gap-20 px-20">
        {list.list.map((item) => {
          let _imgs = str2arr(item.images);
          let _cover = _imgs?.[0] || item.videoCoverImage;
          _cover += "?x-oss-process=image/resize,w_750,limit_0";
          return (
            <View
              className=""
              key={item.id}
              onClick={() => {
                if (item.model == 2) {
                  Taro.navigateTo({
                    url: `/pages/mall2/noteinfo2/index?id=${item.id}`,
                  });
                } else {
                  Taro.navigateTo({
                    url: `/pages/mall2/noteinfo/index?id=${item.id}`,
                  });
                }
              }}
            >
              <Image
                className="block h-480 w-full bg-#fff"
                mode="aspectFit"
                src={_cover}
              />
              <View className="bg-#fff px-20">
                <View className="py-20">
                  <View className="text-(30/48 #11100F) h-96 line-clamp-2">
                    {item.title}
                  </View>
                </View>
                <View className="flex items-center pb-25">
                  <Image
                    className="size-40 rounded-full overflow-hidden bg-#eee mr-10"
                    mode="aspectFill"
                    src={item.appUser?.photo || img_avatar}
                  />
                  <View className="flex-1 text-(22 #666)">
                    {item.appUser?.name}
                  </View>
                  {/* <UnoIcon className="i-icon-park-outline:like size-40 c-#666 mr-5" /> */}

                  {item.likesTag ? (
                    <UnoIcon className="i-icon-park-solid:like size-40 c-#8E4E36 mr-5" />
                  ) : (
                    <UnoIcon className="i-icon-park-outline:like size-40 c-#666 mr-5" />
                  )}
                  <View className="text-(26 #666)">{item.likesNum}</View>
                </View>
              </View>
            </View>
          );
        })}
      </View>

      <View className="text-(24 #999 center) pt-30 pb-100">没有更多数据了</View>
    </View>
  );
};
