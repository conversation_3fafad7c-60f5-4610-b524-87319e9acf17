import { PopUp } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import { Input, ScrollView, Textarea, View } from "@tarojs/components";
import { useSetState } from "ahooks";
import clsx from "clsx";
import { UploadImg } from "./uploadImg";
import { UploadVideo } from "./uploadVideo";
import { useEffect } from "react";
import { showToast } from "@/utils/tools";
import CusApi from "@/services/CusApi";
import Taro from "@tarojs/taro";

const tabs = [
  { id: 1, name: "图片" },
  { id: 2, name: "视频" },
];

const maxlen = 500;

const INIT_STATE = {
  mode: 1,
  title: "",
  cont: "",
  imgs: "",
  video: "",
  cover: "",
};

export const Poster = (props: {
  open?: boolean;
  onClose?: any;
  onOk?: any;
  data?: any;
}) => {
  const [state, setState] = useSetState({
    ...INIT_STATE,
    key: 1,
    isEdit: !!props.data,
  });

  useEffect(() => {
    if (props.open) {
      if (!props.data) {
        setState({ ...INIT_STATE, key: state.key + 1, isEdit: false });
      } else {
        console.log(props.data);
        const data = props.data;

        setState({
          mode: data.model,
          title: data.title,
          cont: data.content,
          imgs: data.images,
          video: data.videoUrl,
          cover: data.videoCoverImage,

          key: state.key + 1,
          isEdit: true,
        });
      }
    }
  }, [props.open]);

  const onSubmit = async () => {
    const data = {
      id: props.data?.id,
      type: 10,
      title: state.title,
      content: state.cont,
      model: state.mode,
      images: state.imgs,
      videoCoverImage: state.cover,
      videoUrl: state.video,
    };

    if (!data.title) return showToast("请填写标题");
    if (!data.content) return showToast("请填写内容");
    if (data.model == 1 && !data.images) return showToast("请上传图片");
    if (data.model == 2 && !data.videoUrl) return showToast("请上传视频");
    if (data.model == 2 && !data.videoCoverImage)
      return showToast("请上传视频封面");

    try {
      Taro.showLoading({ title: "处理中...", mask: true });
      if (data.id) {
        await CusApi.putNote(data);
      } else {
        await CusApi.addNote(data);
      }

      props.onClose?.();
      props.onOk?.();
      showToast("操作成功");
    } finally {
      Taro.hideLoading();
    }
  };

  return (
    <PopUp open={props.open} onClose={() => props.onClose?.()}>
      <View
        className="bg-#F4F4F4 px-20 pt-20"
        style={{ borderRadius: "70rpx 70rpx 0 0" }}
      >
        <View className="flex items-center py-20 px-30">
          <View className="flex-1 min-w-0 text-(37 #11100F)">
            {state.isEdit ? "编辑日常" : "发布日常"}
          </View>
          <View onClick={() => props.onClose?.()}>
            <UnoIcon className="i-icon-park-outline:close-one size-36 c-#333" />
          </View>
        </View>
        <View
          className="min-h-100 bg-#fff px-35 pt-30"
          style={{ borderRadius: "20rpx 20rpx 0 0" }}
        >
          <View className="flex b-(1 solid #AD7F6D) rounded-10 mb-20 overflow-hidden">
            {tabs.map((item) => (
              <View
                key={item.id}
                className={clsx(
                  "flex-1 h-60 text-(22/60 center #666)",
                  state.mode == item.id && "!bg-#AD7F6D !text-#fff"
                )}
                onClick={() => setState({ mode: item.id })}
              >
                {item.name}
              </View>
            ))}
          </View>

          {state.mode == 1 && (
            <View>
              <UploadImg
                key={state.key}
                defaultValue={state.imgs}
                max={3}
                onChange={(s) => setState({ imgs: s })}
              />
            </View>
          )}

          {state.mode == 2 && (
            <View className="grid cols-3 gap-20">
              <UploadVideo
                key={"v" + state.key}
                defaultValue={state.video}
                className="!cols-1"
                max={1}
                onChange={(s) => setState({ video: s })}
              />
              <UploadImg
                key={"i" + state.key}
                defaultValue={state.cover}
                className="!cols-1"
                max={1}
                onChange={(s) => setState({ cover: s })}
                holder="视频封面"
              />
            </View>
          )}

          <Input
            className="h-100 text-(38/100 #333)  b-b-(1 solid #eee)"
            placeholder="点击输入标题"
            value={state.title}
            onInput={(e) => setState({ title: e.detail.value })}
          />
          <ScrollView scrollY className="h-200 mt-30">
            <Textarea
              placeholder="点击输入正文内容~"
              className="block w-full text-(26/40 #333) h-200 min-h-200"
              value={state.cont}
              onInput={(e) => setState({ cont: e.detail.value })}
              maxlength={maxlen}
              autoHeight
            />
          </ScrollView>
          <View className="flex justify-end text-(22 #b2b2b2) py-10">
            {state.cont.length} / {maxlen}
          </View>

          <View className="flex items-center py-30">
            <View
              className="ml-auto w-408 h-90 bg-#AD7F6D rounded-18 text-(30/90 center #fff)"
              onClick={onSubmit}
            >
              {state.isEdit ? "编辑日常" : "发布日常"}
            </View>
          </View>
        </View>
      </View>
    </PopUp>
  );
};
