import { UnoIcon } from "@/components/UnoIcon";
import { str2arr } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useSetState, useUpdateEffect } from "ahooks";
import clsx from "clsx";
import { useMemo } from "react";

export const UploadImg = (props: {
  holder?: string;
  max?: number;
  defaultValue?: string;
  onChange?: any;
  className?: string;
}) => {
  const max = props.max ?? 1;

  const [state, setState] = useSetState({
    list: str2arr(props.defaultValue)
      .splice(0, max)
      .map((n, i) => ({
        uid: -i,
        url: n,
        path: "",
        status: "success",
      })) as any[],
  });

  const value = useMemo(() => {
    return state.list
      .filter((n) => n.status == "success" && n.url)
      .map((n) => n.url)
      .join(",");
  }, [state.list]);

  useUpdateEffect(() => {
    props.onChange?.(value);
  }, [value]);

  const onUpload = async () => {
    const count = max - state.list.length;
    if (count < 1) return;

    const res = await Taro.chooseMedia({
      count,
      mediaType: ["image"],
      sizeType: ["compressed"],
    });

    const list = res.tempFiles.map((n, idx) => ({
      uid: Date.now() + "-" + idx,
      url: "",
      path: n.tempFilePath,
      status: "uploading",
    }));

    setState((p) => ({ list: [...p.list, ...list] }));

    list.forEach(async (item) => {
      try {
        const res = await Taro.uploadFile({
          url: process.env.TARO_APP_UPLOAD ?? "",
          name: "file",
          filePath: item.path,
          header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
        });

        // await sleep(1e3);
        const data = JSON.parse(res.data);
        if (data?.code == 0) {
          const url = data.data?.path || "";
          setState((p) => ({
            list: p.list.map((n) =>
              n.uid == item.uid ? { ...n, url, status: "success" } : n
            ),
          }));
        } else {
          throw data;
        }
      } catch (err) {
        setState((p) => ({
          list: p.list.map((n) =>
            n.uid == item.uid ? { ...n, status: "failed" } : n
          ),
        }));
      }
    });
  };

  const onDel = (uid: string) => {
    setState((p) => ({ list: p.list.filter((n) => n.uid != uid) }));
  };

  return (
    <View className={clsx("grid cols-3 gap-20", props.className)}>
      {state.list.map((item) => (
        <View className="pt-1/1 pos-relative" key={item.uid}>
          <View className="pos-absolute top-0 left-0 size-full">
            <Image
              className="size-full block"
              mode="aspectFill"
              src={item.path || item.url}
            />

            {item.status == "uploading" && (
              <View className="pos-absolute top-0 left-0 size-full bg-#000/80 flex flex-col items-center justify-center transition-all">
                <UnoIcon className="i-icon-park-outline:loading-four size-48 text-#fff animate-spin" />
                <View className="text-(26 #fff) mt-10">上传中...</View>
              </View>
            )}

            {item.status == "failed" && (
              <View className="pos-absolute top-0 left-0 size-full bg-#000/80 flex flex-col items-center justify-center transition-all">
                <UnoIcon className="i-icon-park-outline:close-one size-48 text-#fff" />
                <View className="text-(26 #fff) mt-10">上传失败</View>
              </View>
            )}

            <View
              className="pos-absolute top-0 right-0 bg-#000 size-36 flex items-center justify-center"
              onClick={() => onDel(item.uid)}
            >
              <UnoIcon className="i-icon-park-outline:close-small size-30 text-#fff" />
            </View>
          </View>
        </View>
      ))}

      {state.list.length < max && (
        <View className="pt-1/1 pos-relative" onClick={onUpload}>
          <View className="pos-absolute top-0 left-0 size-full">
            <View className="size-full flex flex-col items-center justify-center bg-#eee">
              <UnoIcon className="i-icon-park-outline:pic size-48 text-#666" />
              <View className="text-(22 #666) mt-5">
                {props.holder || "上传图片"}
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};
