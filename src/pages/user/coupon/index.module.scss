.tabs {
  z-index: 100;
  position: sticky;
  top: 0;
  background: #fff;
}

.list {
  padding: 30px;
}

.item {
  display: flex;
  height: 280px;
  background: #fff;
  border-radius: 20px;
  margin-bottom: 30px;

  $radius: 20px;
  -webkit-mask-image: radial-gradient(
    circle at $radius,
    transparent $radius,
    red $radius
  );
  -webkit-mask-position: -$radius;
  -webkit-mask-size: 100% calc($radius + 50px);

  &.t-30 .side {
    background: #fca136;
  }

  &.t-20 .side {
    background: #2388f0;
  }

  &.t-10 .side {
    background: #fc495f;
  }

  &.s-20 .side,
  &.s-3- .side {
    background: #ccc;
  }

  .side {
    width: 350px;
    background: #eee;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    font-size: 40px;
    color: #fff;

    .yen {
      font-size: 80px;
      font-weight: bold;
      display: flex;
      align-items: baseline;
    }
    .ful {
      margin-top: 10px;
      font-size: 30px;
    }
    .sm {
      margin: 0 8px;
      font-size: 0.6em;
    }
  }

  .cont {
    min-width: 0;
    flex: 1;
    display: flex;
    // align-items: center;
    justify-content: center;
    flex-flow: column;
    padding: 0 80px;

    .txt {
      color: #666;
      font-size: 36px;
      margin: 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
