import { Text, View } from "@tarojs/components";
import Style from "./index.module.scss";
import Watermark from "@/components/Watermark";
import { useSetState } from "ahooks";
import CusApi from "@/services/CusApi";
import useFetchList from "@/utils/usefetchList";
import dayjs from "dayjs";
import Taro, { usePullDownRefresh, useReachBottom } from "@tarojs/taro";
import clsx from "clsx";
import { useEffect, useState } from "react";
import { Tabs, Dialog, Button } from "@taroify/core"; // 引入 Taroify 的 Button 组件
import { UnoIcon } from "@/components/UnoIcon";

const Coupon = () => {
  const [state, setState] = useSetState({
    cstate: 10,
  });
  const [list, listAct] = useFetchList({
    api: CusApi.getCoupon,
    params: { state: state.cstate },
  });

  const fdate = (n) => (n ? dayjs(n).format("YYYY-MM-DD") : "");

  const [isDialogOpen, setDialogOpen] = useState(false); // 控制 Dialog 状态
  const [dialogContent, setDialogContent] = useState(""); // 控制 Dialog 内容

  useEffect(() => {
    listAct.getList();
  }, [state.cstate]);

  useReachBottom(() => {
    listAct.getNext();
  });

  usePullDownRefresh(async () => {
    await listAct.getList();
    Taro.stopPullDownRefresh();
  });

  return (
    <View>
      <View className={Style.tabs}>
        <Tabs
          onChange={(e) => {
            setState({ cstate: e ? 20 : 10 });
          }}
          style={{ "--tabs-active-color": "#8E4E36" } as any}
        >
          <Tabs.TabPane title={"待使用"}></Tabs.TabPane>
          <Tabs.TabPane title={"已使用"}></Tabs.TabPane>
        </Tabs>
      </View>
      <View className={Style.list}>
        {list.list.map((item) => (
          <View
            className={clsx(
              Style.item,
              Style[`t-` + item.type],
              Style["s-" + item.state]
            )}
            key={item.id}
          >
            <View className={Style.side}>
              {item.type == 10 && (
                <View className={Style.yen}>
                  <Text className={Style.sm}>&yen;</Text>
                  <Text>{item.preferentialMoney}</Text>
                </View>
              )}
              {item.type == 20 && (
                <View className={Style.yen}>
                  <Text>{item.discount}</Text>
                  <Text className={Style.sm}>折</Text>
                </View>
              )}
              {item.minConsumeMoney ? (
                <View className={Style.ful}>
                  满{item.minConsumeMoney}元可用
                </View>
              ) : (
                <View className={Style.ful}>无门槛</View>
              )}
            </View>
            <View className="flex-1 flex flex-col justify-center pl-30 pr-50 gap-20">
              <View className="text-(30 #333) fw-bold">{item.name}</View>
              <View className="flex items-center">
                <View className="text-(24 #666)">
                  有效期至：{fdate(item.effectiveEndDate)}
                </View>
                {item.state == 10 && (
                  <View
                    className="ml-a text-(24 #8E4E36)"
                    onClick={() => {
                      Taro.switchTab({ url: "/pages/tabs/cate/index" });
                    }}
                  >
                    去使用
                  </View>
                )}
              </View>
              {item.buyNotices && ( // 只有 buyNotices 有值时才显示按钮
                <View
                  className="flex items-center mt-10 cursor-pointer"
                  onClick={() => {
                    setDialogContent(item.buyNotices); // 设置弹窗内容为 buyNotices
                    setDialogOpen(true);
                  }}
                >
                  <Text className="text-(24 #8E4E36)">使用规则</Text>
                  <UnoIcon className="ml-5 text-(24 #8E4E36) i-icon-park-outline:right" />
                </View>
              )}
            </View>
          </View>
        ))}
      </View>

      {/* 使用规则弹窗 */}
      <Dialog open={isDialogOpen} onClose={() => setDialogOpen(false)}>
        <Dialog.Header>使用规则</Dialog.Header>
        <Dialog.Content>
          <View>{dialogContent}</View>
        </Dialog.Content>
        <Dialog.Actions>
          <Button color="primary" onClick={() => setDialogOpen(false)}>
            确定
          </Button>
        </Dialog.Actions>
      </Dialog>

      <Watermark></Watermark>
    </View>
  );
};

export default Coupon;
