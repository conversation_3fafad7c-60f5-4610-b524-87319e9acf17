import { PopUpTop } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import { getUserByToken, updateUser } from "@/services/UserApi";
import { img_avatar } from "@/utils/images";
import { Button, Image, Input, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";

export default function Page() {
  const [state, setState] = useSetState({
    user: null as any,
    nick: "",
    open: false,

    signature: "",
    open2: false,
  });

  useMount(() => {
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await getUserByToken();
    setState({ user });
  };

  const update = async (data: any) => {
    await updateUser(data);
    fetchUser();
  };

  const setNick = async () => {
    await update({ name: state.nick });
    setState({ open: false });
  };

  const setSign = async () => {
    await update({ signature: state.signature });
    setState({ open2: false });
  };

  const setAvatar = async (e) => {
    const path = e.detail.avatarUrl;
    Taro.showLoading({ title: "上传中..." });
    const res = await Taro.uploadFile({
      url: process.env.TARO_APP_UPLOAD || "",
      filePath: path,
      name: "file",
      header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
    });
    Taro.hideLoading();
    const data = JSON.parse(res.data);
    const url = data?.data?.allFullPath || "";
    console.log(url);
    await update({ photo: url });
  };

  const logout = async () => {
    const m = await Taro.showModal({ title: "确定要退出登录吗？" });
    if (!m.confirm) return;
    Taro.removeStorageSync("token");
    Taro.removeStorageSync("user");
    Taro.reLaunch({ url: "/pages/tabs/mall/index" });
  };

  return (
    <View className="p-25">
      <View className="py-20 text-(24 #666)">我的信息</View>
      <View className="bg-#fff rounded-20 px-20">
        <View className="pos-relative flex items-center p-25 b-t-(1 solid #eee) first:b-0">
          <View className="flex-1 text-(26 #333)">头像</View>
          <View className="ml-auto">
            <Image
              className="size-100 bg-#eee rounded-full"
              mode="aspectFill"
              src={state.user?.photo || img_avatar}
            />
          </View>
          <UnoIcon className="i-icon-park-outline:right size-30 text-#999 ml-5 -mr-25" />

          <Button
            openType="chooseAvatar"
            className="pos-absolute top-0 left-0 size-full opacity-0"
            onChooseAvatar={setAvatar}
          ></Button>
        </View>

        <View
          className="flex items-center p-25 b-t-(1 solid #eee) first:b-0"
          onClick={() => {
            setState({ open: true, nick: state.user?.name || "" });
          }}
        >
          <View className="flex-1 text-(26 #333)">昵称</View>
          <View className="flex-1 text-(26 #666 right)">
            {state.user?.name}
          </View>
          <UnoIcon className="i-icon-park-outline:right size-30 text-#999 ml-5 -mr-25" />
        </View>

        <View
          className="flex items-center p-25 b-t-(1 solid #eee) first:b-0"
          onClick={() => {
            setState({ open2: true, signature: state.user?.signature || "" });
          }}
        >
          <View className="flex-1 text-(26 #333)">签名</View>
          <View className="flex-1 text-(26 #666 right)">
            {state.user?.signature}
          </View>
          <UnoIcon className="i-icon-park-outline:right size-30 text-#999 ml-5 -mr-25" />
        </View>

        <View className="flex items-center p-25 b-t-(1 solid #eee) first:b-0">
          <View className="flex-1 text-(26 #333)">手机号</View>
          <View className="flex-1 text-(26 #666 right)">
            {state.user?.mobile}
          </View>
          <UnoIcon className="invisible i-icon-park-outline:right size-30 text-#999 ml-5 -mr-25" />
        </View>

        <View className="flex items-center p-25 b-t-(1 solid #eee) first:b-0">
          <View className="flex-1 text-(26 #333)">会员号</View>
          <View className="flex-1 text-(26 #666 right)">
            {state.user?.serialNo}
          </View>
          <UnoIcon className="invisible i-icon-park-outline:right size-30 text-#999 ml-5 -mr-25" />
        </View>
      </View>

      <View
        className="mt-100 bg-#AD7F6D h-90 rounded-20 text-(30/90 #fff center)"
        onClick={logout}
      >
        退出登录
      </View>

      <PopUpTop open={state.open} onClose={() => setState({ open: false })}>
        <View className="bg-#fff px-20">
          <View className="h-90 flex items-center">
            <Input
              className="flex-1 text-(30 #333)"
              type="nickname"
              placeholder="请输入"
              onConfirm={setNick}
              onInput={(e) => setState({ nick: e.detail.value })}
            />
            <View
              className="ml-20 w-140 h-60 bg-#AD7F6D rounded-10 text-(26/60 #fff center)"
              onClick={setNick}
            >
              提交
            </View>
          </View>
        </View>
      </PopUpTop>

      <PopUpTop open={state.open2} onClose={() => setState({ open2: false })}>
        <View className="bg-#fff px-20">
          <View className="h-90 flex items-center">
            <Input
              className="flex-1 text-(30 #333)"
              placeholder="请输入签名"
              onConfirm={setSign}
              onInput={(e) => setState({ signature: e.detail.value })}
              value={state.signature}
            />
            <View
              className="ml-20 w-140 h-60 bg-#AD7F6D rounded-10 text-(26/60 #fff center)"
              onClick={setSign}
            >
              提交
            </View>
          </View>
        </View>
      </PopUpTop>
    </View>
  );
}
