import { Input, View } from "@tarojs/components";
import Style from "./index.module.scss";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { useSetState } from "ahooks";
import { getUserByToken } from "@/services/UserApi";
import { useMemo } from "react";
import { showToast } from "@/utils/tools";
import CusApi from "@/services/CusApi";

const WithDrawPage = () => {
  const router = useRouter();
  const isAgent = router.params.agent == "1";
  const [state, setState] = useSetState({
    user: null as any,

    money: "",
  });

  useDidShow(() => {
    fetchUser();
  });

  const bank = useMemo(() => {
    if (state.user?.bankInfo) {
      return state.user.bankInfo;
    } else {
      return null;
    }
  }, [state.user]);

  const fetchUser = async () => {
    const user = await getUserByToken();
    setState({ user });
    console.log(user);
  };

  const handleSubmit = async () => {
    const data = {
      source: 30,
      applyId: isAgent
        ? state.user?.agentCompanyId
        : state.user?.distributionCompanyId,
      applyMoney: state.money,
      moneyDestination: 11,
      bankInfo: state.user?.bankInfo,
    };

    if (!state.user) return showToast("未找到用户信息");
    if (!state.user?.bankInfo) return showToast("未绑定银行卡");
    if (!state.money) return showToast("请输入提现金额");

    await CusApi.addWithdraw(data, { _loading: true });
    Taro.showModal({
      title: "提现申请已提交成功",
      content: `请耐心等待审核`,
      showCancel: false,
      confirmText: "返回",
      success: (res) => {
        if (res.confirm) Taro.navigateBack();
      },
    });
  };

  return (
    <View className={Style.page}>
      <View className={Style.pane}>
        <View className={Style.label}>提现金额：</View>
        <View className={Style.money}>
          <View className={Style.yen}>&yen;</View>
          <Input
            type="digit"
            className={Style.ipt}
            placeholder="请输入提现金额"
            placeholderClass={Style.holder}
            value={state.money}
            onInput={(e) => setState({ money: e.detail.value })}
          />
        </View>
        <View className={Style.tip}>
          可提现金额: &nbsp; &yen;
          {(isAgent
            ? state.user?.companyAgentBalance
            : state.user?.companyDistributionBalance) ?? "--"}
        </View>
        <View className={Style.label}>提现至银行卡：</View>

        {/* <View className={Style.bank}>6222 6212 3456 7890 125</View> */}
        <View
          className={Style.bank2}
          onClick={() =>
            Taro.navigateTo({ url: `/pages/user/withdrawBank/index` })
          }
        >
          {bank ? (
            <>
              <View>{bank.bankCardNo}</View>
              <View className={Style.em}>去修改&gt;</View>
            </>
          ) : (
            <>
              <View>未绑定银行卡</View>
              <View className={Style.em}>去绑定&gt;</View>
            </>
          )}
        </View>
      </View>
      <View className={Style.btn} onClick={() => handleSubmit()}>
        确认提现
      </View>
      <View className={Style.tips}>
        <View className={Style.title}>温馨提示：</View>
        <View className={Style.cont}>
          <View>1、申请提现前，请先添加绑定您的结算银行卡;</View>
          <View>2、每个月仅限提现一次，提现最低金额1000元;</View>
          <View>3、申请提现后，5个工作日内到账至您的结算银行卡;</View>
          <View>4、节假日申请提现、到帐顺延;</View>
        </View>
      </View>
    </View>
  );
};

export default WithDrawPage;
