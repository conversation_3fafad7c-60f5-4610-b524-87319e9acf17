import { View, Input } from "@tarojs/components";
import Style from "./index.module.scss";
import { useSetState } from "ahooks";
import { getUserByToken, updateUser } from "@/services/UserApi";
import Taro, { useDidShow } from "@tarojs/taro";
import { useMemo } from "react";
import { showToast } from "@/utils/tools";

const WithdrawBank = () => {
  const [state, setState] = useSetState({
    user: null as any,

    bankName: "",
    bankCardNo: "",
    bankHolder: "",
  });

  useDidShow(() => {
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await getUserByToken();
    const bank = user?.bankInfo;
    setState({ user, ...bank });
  };

  const handleSubmit = async () => {
    const data = {
      bankName: state.bankName,
      bankCardNo: state.bankCardNo,
      bankHolder: state.bankHolder,
    };

    if (!data.bankName) return showToast("请输入银行名");
    if (!data.bankCardNo) return showToast("请输入卡号");
    if (!data.bankHolder) return showToast("请输入持卡人");

    await updateUser({ bankInfo: data }, { _loading: true });
    Taro.showModal({
      title: "绑卡成功",
      content: data.bankName + "/" + data.bankCardNo,
      showCancel: false,
      confirmText: "返回提现",
      success: async (res) => {
        if (res.confirm) {
          Taro.navigateBack();
        }
      },
    });
  };

  return (
    <View>
      <View className={Style.mtitle}>
        {state.user?.bankInfo ? "已绑定银行卡" : "未绑定银行卡"}
      </View>
      <View className={Style.stitle}>请绑定持卡人本人的银行卡</View>
      <View className={Style.pane}>
        <View className={Style.row}>
          <View className={Style.label}>银行名：</View>
          <View className={Style.cont}>
            <Input
              className={Style.ipt}
              placeholder="例如 “中国农业银行”"
              value={state.bankName}
              onInput={(e) => setState({ bankName: e.detail.value })}
            />
          </View>
        </View>

        <View className={Style.row}>
          <View className={Style.label}>卡号：</View>
          <View className={Style.cont}>
            <Input
              className={Style.ipt}
              type="number"
              placeholder="请输入卡片正面的卡号"
              value={state.bankCardNo}
              onInput={(e) => setState({ bankCardNo: e.detail.value })}
            />
          </View>
        </View>
        <View className={Style.row}>
          <View className={Style.label}>持卡人：</View>
          <View className={Style.cont}>
            <Input
              className={Style.ipt}
              placeholder="请输入真实的持卡人姓名"
              value={state.bankHolder}
              onInput={(e) => setState({ bankHolder: e.detail.value })}
            />
          </View>
        </View>
      </View>

      <View className={Style.btn} onClick={() => handleSubmit()}>
        绑定银行卡
      </View>

      <View className={Style.tips}>
        <View className={Style.title}>温馨提示：</View>
        <View className={Style.cont}>
          <View>
            1、请填写您的身份信息开户的储蓄卡作为您的结算卡。若结算卡为对公账户，请填写您公司对公账户收款信息;
          </View>
          <View>2、为保障您的资金到账及时，请绑定一类银行卡.</View>
        </View>
      </View>
    </View>
  );
};

export default WithdrawBank;
