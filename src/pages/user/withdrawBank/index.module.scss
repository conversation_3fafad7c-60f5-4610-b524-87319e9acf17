.mtitle {
  padding: 120px 0;
  text-align: center;
  font-size: 50px;
  // font-weight: bold;
  color: #000;
}

.stitle {
  padding-bottom: 40px;
  font-size: 40px;
  color: #666;
  text-align: center;
}

.pane {
  margin: 0 40px;
  background: #fff;
  padding: 20px 60px;
  border-radius: 30px;
  font-size: 46px;

  .row {
    display: flex;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f5f5f5;
    &:last-child {
      border-bottom: none;
    }
  }

  .label {
    width: 200px;
  }

  .cont {
    min-width: 0;
    flex: 1;
  }

  .ipt {
    padding: 30px 0;
  }
}

.btn {
  margin: 80px 40px;
  height: 120px;
  line-height: 120px;
  text-align: center;
  color: #fff;
  background: #d0ab6b;
  border-radius: 30px;
  font-size: 48px;
}

.tips {
  padding: 40px;
  font-size: 36px;

  .title {
    color: #666;
    padding-bottom: 30px;
  }

  .cont {
    white-space: pre-wrap;
    color: #666;
    line-height: 2;
  }
}
