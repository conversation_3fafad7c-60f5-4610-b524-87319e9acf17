import { UnoIcon } from "@/components/UnoIcon";
import { useList } from "@/stores/useList";

import { img_avatar } from "@/utils/images";
import { makeApi } from "@/utils/request";
import { str2arr } from "@/utils/tools";
import { Image, View } from "@tarojs/components";
import Taro, { useReachBottom, useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";
import { useEffect } from "react";

const getUser = makeApi("get", `/api/appUser/query_by_userid`);
const getList = makeApi("get", `/api/appInformationArticle/web/query_list`);
const doFollow = makeApi("post", `/api/appUserRelation/follow`);
const unFollow = makeApi("post", `/api/appUserRelation/cancel_follow`);

const tabs = [
  { id: 0, name: "动态" },
  { id: 1, name: "赞过" },
];

export default function Page() {
  const router = useRouter();
  const uid = router.params.id;
  const [state, setState] = useSetState({
    user: null as any,
    idx: 0,
  });

  useMount(() => {
    fetchUser();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  useEffect(() => {
    listAct.reload();
  }, [state.idx]);

  const fetchUser = async () => {
    const user = await getUser({ appUserId: uid });
    setState({ user });
  };

  const [list, listAct] = useList({
    api: getList,
    params:
      state.idx == 0
        ? { type: 10, publicUserId: uid }
        : { type: 10, likesUserId: uid },
  });

  const onFollow = async (follow = false) => {
    if (follow) {
      const md = await Taro.showModal({ title: "确定要取消关注吗？" });
      if (!md.confirm) return;
      await unFollow({ targetUserId: uid });
    } else {
      await doFollow({ targetUserId: uid });
    }
    // showToast("操作成功");
    fetchUser();
  };

  return (
    <View className="min-h-100vh bg-#F8F6F5">
      <View className="pt-30 bg-#F5F1EE">
        <View className="flex items-center px-30">
          <Image
            className="size-84 bg-#eee rounded-full overflow-hidden mr-30"
            mode="aspectFill"
            src={state.user?.photo || img_avatar}
          />
          <View className="text-(26 #1A1A1A) fw-bold">{state.user?.name}</View>
        </View>
        <View className="px-30 py-35 text-(25 #1A1A1A)">
          {state.user?.signature || "这个人很神秘，什么都没留下"}
        </View>

        <View className="flex py-70 pr-30">
          <View
            className="text-center px-35"
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/user/userRelation/index?name=${state.user?.name}&id=${uid}&idx=0`,
              });
            }}
          >
            <View className="text-(28 #11100F) fw-bold">
              {state.user?.followCount}
            </View>
            <View className="text-(22 #11100F) mt-5">关注</View>
          </View>
          <View
            className="text-center px-35"
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/user/userRelation/index?name=${state.user?.name}&id=${uid}&idx=1`,
              });
            }}
          >
            <View className="text-(28 #11100F) fw-bold">
              {state.user?.fansCount}
            </View>
            <View className="text-(22 #11100F) mt-5">粉丝</View>
          </View>
          <View className="text-center px-35">
            <View className="text-(28 #11100F) fw-bold">
              {state.user?.likeCount}
            </View>
            <View className="text-(22 #11100F) mt-5">获赞</View>
          </View>

          {state.user?.followTag ? (
            <View
              className="h-54 w-170 rounded-10 b-(1 solid #5A5A5A) ml-a text-(24/54 #5A5A5A center)"
              onClick={() => onFollow(true)}
            >
              已关注
            </View>
          ) : (
            <View
              className="h-54 w-170 rounded-10 b-(1 solid #8E4E36) ml-a text-(24/54 #fff center) bg-#8E4E36"
              onClick={() => onFollow(false)}
            >
              关注
            </View>
          )}
        </View>

        <View className="h-40 -mb-40"></View>
      </View>

      <View
        className="bg-#F8F6F5 overflow-hidden"
        style={{ borderRadius: "30rpx 30rpx 0 0" }}
      >
        <View className="flex items-center justify-center pt-60 pb-35">
          {tabs.map((item) => (
            <View
              className="px-65"
              key={item.id}
              onClick={() => setState({ idx: item.id })}
            >
              <View
                className={clsx(
                  "text-(28 #11100F) fw-bold",
                  state.idx == item.id && "!text-#8E4E36"
                )}
              >
                {item.name}
              </View>
              <View
                className={clsx(
                  "w-25 h-5 bg-#8E4E36 mx-a mt-5",
                  state.idx !== item.id && "invisible"
                )}
              ></View>
            </View>
          ))}
        </View>

        <View className="grid cols-2 gap-20 px-20">
          {list.list.map((item) => {
            let _imgs = str2arr(item.images);
            let _cover = _imgs?.[0] || item.videoCoverImage;
            _cover += "?x-oss-process=image/resize,w_750,limit_0";
            return (
              <View
                className=""
                key={item.id}
                onClick={() => {
                  if (item.model == 2) {
                    Taro.navigateTo({
                      url: `/pages/mall2/noteinfo2/index?id=${item.id}`,
                    });
                  } else {
                    Taro.navigateTo({
                      url: `/pages/mall2/noteinfo/index?id=${item.id}`,
                    });
                  }
                }}
              >
                <Image
                  className="block h-480 w-full bg-#fff"
                  mode="aspectFit"
                  src={_cover}
                />
                <View className="bg-#fff px-20">
                  <View className="py-20">
                    <View className="text-(30/48 #11100F) h-96 line-clamp-2">
                      {item.title}
                    </View>
                  </View>
                  <View className="flex items-center pb-25">
                    <Image
                      className="size-40 rounded-full overflow-hidden bg-#eee mr-10"
                      mode="aspectFill"
                      src={item.appUser?.photo || img_avatar}
                    />
                    <View className="flex-1 text-(22 #666)">
                      {item.appUser?.name}
                    </View>
                    {/* <UnoIcon className="i-icon-park-outline:like size-40 c-#666 mr-5" /> */}

                    {item.likesTag ? (
                      <UnoIcon className="i-icon-park-solid:like size-40 c-#8E4E36 mr-5" />
                    ) : (
                      <UnoIcon className="i-icon-park-outline:like size-40 c-#666 mr-5" />
                    )}
                    <View className="text-(26 #666)">{item.likesNum}</View>
                  </View>
                </View>
              </View>
            );
          })}
        </View>

        <View className="text-(24 #999 center) pt-30 pb-100">
          没有更多数据了
        </View>
      </View>
    </View>
  );
}
