import { CusModal } from "@/components/Custom/Modal";
import { Image, View } from "@tarojs/components";

export const ResModal = (props: { text: any; open: boolean; onClose: any }) => {
  return (
    <CusModal open={props.open} onClose={props.onClose}>
      <View className="flex flex-col items-center justify-center">
        <View className="text-(40 #fff) mb-50">恭喜获得</View>
        <View
          className="w-488 h-422 rounded-30 bg-#fff flex flex-col items-center justify-center"
          style={{
            backgroundImage:
              "linear-gradient(0deg, rgba(226,199,171,0.7), rgba(255,255,255,0.7))",
            boxShadow: "0rpx 0rpx 10rpx 0rpx #191919",
          }}
        >
          <Image
            className="block size-200"
            mode="aspectFit"
            src="https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241010/1293976507759988736.png"
          />
          <View className="text-(40 #8E4E36) mt-20 line-clamp-1 fw-bold">
            {props.text}
          </View>
        </View>

        <View className="flex items-center justify-center pt-60">
          <View
            className="pos-relative w-350 h-74 rounded-full text-(36/74 center #fff) bg-#B7684A"
            style={{
              boxShadow: "0 0 5rpx rgba(255,255,255,.5)",
              backgroundImage:
                "linear-gradient(-23deg, rgba(255,253,176,0.37), rgba(255,253,155,0))",
            }}
            onClick={props.onClose}
          >
            开心收下
          </View>
        </View>
      </View>
    </CusModal>
  );
};
