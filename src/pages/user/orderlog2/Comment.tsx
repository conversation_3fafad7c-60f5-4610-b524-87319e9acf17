import { Image, PageContainer, Textarea, View } from "@tarojs/components";
import { useSetState } from "ahooks";
import { Rate } from "@/components/Rate";
import Taro from "@tarojs/taro";
import { useEffect, useMemo } from "react";
import { UnoIcon } from "@/components/UnoIcon";
import clsx from "clsx";
import { showToast } from "@/utils/tools";
import { makeApi } from "@/utils/request";

const img_close = `https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241023/1298659849017233408.png`;

const rateList = [
  { size: 50, name: "总评分", key: "evaluationScore" },
  { size: 36, name: "沟通", key: "evaluationCommunicateScore" },
  { size: 36, name: "技术", key: "evaluationTechnologyScore" },
  { size: 36, name: "审美", key: "evaluationEstheticScore" },
];

const INIT = {
  evaluationScore: 5,
  evaluationCommunicateScore: 5,
  evaluationTechnologyScore: 5,
  evaluationEstheticScore: 5,
  content: "",
  anonymousTag: 0,
};

const useUpload = (props: { max: number }) => {
  const [state, setState] = useSetState({
    list: [] as any[],
  });

  const max = props.max ?? 1;

  const ex = useMemo(() => {
    const links = state.list
      .filter((n) => n.status === "success")
      .map((n) => n.url)
      .join(",");
    const loading = state.list.some((n) => n.status === "uploading");
    const showUpload = state.list.length < max;
    return { links, loading, showUpload };
  }, [state.list]);

  const upload = async () => {
    const count = max - state.list.length;
    if (count < 1) return;

    const res = await Taro.chooseMedia({
      mediaType: ["image"],
      sizeType: ["compressed"],
      count,
    });
    console.log(res);

    const list = res.tempFiles.map((n, idx) => ({
      uid: Date.now() + "-" + idx,
      path: n.tempFilePath,
      thumb: n.thumbTempFilePath,
      status: "uploading",
    }));

    setState((p) => ({ list: [...p.list, ...list] }));

    list.forEach(async (item) => {
      try {
        const res = await Taro.uploadFile({
          url: process.env.TARO_APP_UPLOAD ?? "",
          name: "file",
          filePath: item.path,
          header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
        });
        const obj = JSON.parse(res?.data);
        const url = obj?.data?.path ?? "";
        // await sleep(2e3);
        setState((p) => {
          const list = p.list.map((n) => {
            if (n.uid == item.uid) {
              return { ...n, url, status: "success" };
            }

            return n;
          });

          return { list };
        });
      } catch (err) {
        setState((p) => {
          const list = p.list.map((n) => {
            if (n.uid == item.uid) {
              return { ...n, status: "failed" };
            }

            return n;
          });

          return { list };
        });
        throw err;
      }
    });
  };

  const _delete = (uid: string) => {
    setState((p) => ({ list: p.list.filter((n) => n.uid != uid) }));
  };

  return [
    { ...state, ...ex },
    {
      upload,
      delete: _delete,
    },
  ] as const;
};

const postComment = makeApi(
  "post",
  `/api/mallServiceOrder/evaluation_service_order`
);

export const Comment = (props: {
  open?: boolean;
  onClose?: any;
  onOk?: any;
  data?: any;
}) => {
  const [fstate, fact] = useUpload({ max: 3 });
  const [state, setState] = useSetState({
    ...INIT,
  });

  useEffect(() => {
    if (props.open) {
      setState({ ...INIT });
    }
  }, [props.open]);

  const onSubmit = async () => {
    const data = {
      serviceOrderId: props.data?.id,
      evaluationScore: state.evaluationScore,
      evaluationCommunicateScore: state.evaluationCommunicateScore,
      evaluationTechnologyScore: state.evaluationTechnologyScore,
      evaluationEstheticScore: state.evaluationEstheticScore,
      evaluationDesc: state.content,
      evaluationImage: fstate.links,
      anonymousTag: state.anonymousTag,
    };

    if (!data.evaluationDesc) return showToast("请填写留言内容");

    Taro.showLoading({ title: "提交中...", mask: true });
    const res = await postComment(data);
    console.log(res);
    Taro.showToast({ title: "提交成功", icon: "success" });
    props.onClose?.();
    props.onOk?.(res);
  };

  return (
    <PageContainer
      show={props.open}
      onLeave={props.onClose}
      customStyle="background: transparent;"
    >
      <View className="bg-#f4f4f4" style={{ borderRadius: "70rpx 70rpx 0 0" }}>
        <View className="flex items-center text-(37 #11100F) px-50 pt-40 pb-20">
          <View className="flex-1">写留言</View>
          <Image
            className="size-36"
            mode="aspectFit"
            src={img_close}
            onClick={props.onClose}
          />
        </View>
        <View
          className="mx-20 bg-#fff px-35 pt-50 pb-50"
          style={{ borderRadius: "20rpx 20rpx 0 0" }}
        >
          {rateList.map((item) => {
            return (
              props.open && (
                <View className="flex items-center py-8" key={item.key}>
                  <View className="text-(28 #11100F) w-150">{item.name}</View>
                  <View>
                    <Rate
                      value={state[item.key]}
                      size={item.size}
                      onChange={(e) => setState({ [item.key]: e } as any)}
                    />
                  </View>
                </View>
              )
            );
          })}

          <View className="pos-relative pt-30 pb-50 h-150">
            <Textarea
              className="block size-full text-(24 #000)"
              placeholder="您的参与将决定搞美好医生的评分哦~"
              maxlength={200}
              value={state.content}
              onInput={(e) => setState({ content: e.detail.value })}
            />
            <View className="pos-absolute bottom-0 right-0 text-(22 #B2B2B2)">
              {state.content.length} / 200
            </View>
          </View>

          <View className="flex flex-wrap -ml-20 py-30">
            {fstate.list.map((item) => (
              <View
                className="size-170 bg-#eee ml-20 pos-relative overflow-hidden"
                key={item.uid}
              >
                <Image
                  className="size-full block"
                  mode="aspectFill"
                  src={item.path || item.url}
                />
                <View
                  className={clsx(
                    "pos-absolute top-0 left-0 size-full bg-#000/80 flex flex-col items-center justify-center transition-all invisible opacity-0",
                    item.status == "uploading" &&
                      "uno-layer-z1-(visible opacity-100)"
                  )}
                >
                  <UnoIcon className="i-icon-park-outline:loading-four size-48 text-#fff animate-spin" />
                  <View className="text-(26 #fff) mt-10">上传中...</View>
                </View>
                <View
                  className="pos-absolute top-0 right-0 bg-#000 size-36 flex items-center justify-center"
                  onClick={() => fact.delete(item.uid)}
                >
                  <UnoIcon className="i-icon-park-outline:close-small size-30 text-#fff" />
                </View>
              </View>
            ))}
            {fstate.showUpload && (
              <View
                className="size-170 bg-#eee ml-20 pos-relative flex flex-col items-center justify-center"
                onClick={fact.upload}
              >
                <UnoIcon className="i-icon-park-outline:camera size-48 text-#666" />
                <View className="text-(26 #666) mt-10">上传</View>
              </View>
            )}
          </View>

          <View className="flex">
            <View
              className="flex items-center"
              onClick={() =>
                setState({ anonymousTag: Number(!state.anonymousTag) })
              }
            >
              {state.anonymousTag == 1 ? (
                <UnoIcon className="i-icon-park-outline:check-one size-32 text-#999 mr-15" />
              ) : (
                <UnoIcon className="i-icon-park-outline:round size-32 text-#999 mr-15" />
              )}

              <View className="text-(26 #666)">匿名留言</View>
            </View>
            <View
              className="w-407 h-90 bg-#AD7F6D rounded-18 text-(30/90 center #fefefe) ml-auto"
              onClick={onSubmit}
            >
              确认发布
            </View>
          </View>
        </View>
      </View>
    </PageContainer>
  );
};
