import { View } from "@tarojs/components";
import Style from "./index.module.scss";
import { getOrderList2 } from "@/services/MallApi";
import useFetchList from "@/utils/usefetchList";
// import { useMount, useSetState } from "ahooks";
import Taro, {
  useDidShow,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import dayjs from "dayjs";
import Watermark from "@/components/Watermark";
import { useShare } from "@/stores/useShare";
// import { Comment } from "./Comment";
// import { ResModal } from "./ResModal";

const OrderList = () => {
  // const [state, setState] = useSetState({
  //   open: false,
  //   open2: false,
  //   open2Txt: "",
  //   item: null as any,
  // });

  const [list, listAct] = useFetchList({
    api: getOrderList2,
    params: { serviceState: 20 },
  });

  useShare(() => {
    return {
      title: "治疗记录",
      path: `/pages/user/orderlog2/index`,
    };
  });

  useDidShow(() => {
    listAct.getList();
  });

  usePullDownRefresh(async () => {
    await listAct.getList();
    Taro.stopPullDownRefresh();
  });

  useReachBottom(() => {
    listAct.getNext();
  });

  return (
    <View>
      <View className={Style.list}>
        {list.list.map((item) => (
          <View className={Style.item} key={item.id}>
            <View className={Style.head}>
              <View className={Style.tit}>治疗单号：{item.serialNo}</View>
              {/* <View className={Style.status}>
                {SrviceState.find((n) => n.id == item.srviceState)?.name}
              </View> */}
            </View>

            <View className="text-(28 #666) p-20 grid gap-10">
              <View>门店：{item.shop?.name}</View>
              <View>医生：{item.doctorName}</View>
              <View>
                状态：
                {
                  {
                    10: "待治疗",
                    20: "已治疗",
                  }[item.serviceState]
                }
              </View>
              {/* <View>
                评价状态：
                <Text>
                  {
                    {
                      0: "未开启",
                      1: "待评价",
                      2: "已评价",
                    }[item.evaluationState]
                  }
                </Text>
              </View> */}
            </View>
            <View className={Style.foot}>
              <View className={Style.date}>
                到院时间：{dayjs(item.createDate).format("YYYY-MM-DD HH:mm:ss")}
              </View>
              <View className={Style.btns}>
                {/* <View className={Style.btn}>按钮</View> */}
                {item.evaluationState == 1 && (
                  <View
                    // className={Style.btn}
                    className="w-180 h-60 text-(28/60 center #fff) rounded-full bg-#AD7F6D"
                    // onClick={() => setState({ open: true, item })}
                    onClick={() =>
                      Taro.navigateTo({
                        url: `/pages/user/orderLogComment/index?id=${item.id}`,
                      })
                    }
                  >
                    评价有礼
                  </View>
                )}
              </View>
            </View>
          </View>
        ))}
      </View>
      <Watermark />

      {/* <Comment
        open={state.open}
        onClose={() => setState({ open: false })}
        data={state.item}
        onOk={(res) => {
          setState({ open2: true, open2Txt: res.giftTip });
          listAct.getList();
        }}
      ></Comment>

      <ResModal
        text={state.open2Txt}
        open={state.open2}
        onClose={() => setState({ open2: false })}
      /> */}
    </View>
  );
};

export default OrderList;
