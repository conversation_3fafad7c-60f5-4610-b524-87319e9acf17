import { Picker, View } from "@tarojs/components";
import Style from "./index.module.scss";
import { useSetState } from "ahooks";
import React, { useEffect } from "react";
import Taro, {
  usePullDownRefresh,
  useReachBottom,
  useRouter,
} from "@tarojs/taro";
import CusApi from "@/services/CusApi";
import dayjs from "dayjs";
import { privatePhone } from "@/utils/tools";

const InvestorShop = () => {
  const router = useRouter();
  const [state, setState] = useSetState({
    stat: {} as any,

    list: [] as any[],
    page: 1,
    size: 20,
    more: true,
    loading: true,

    dateCrt: dayjs().format("YYYY-MM"),
    dateStat: 0,
  });

  useEffect(() => {
    fetchStat();
  }, []);

  useEffect(() => {
    fetchList(1);
    fetchListStat();
  }, [state.dateCrt]);

  usePullDownRefresh(async () => {
    await fetchList(1);
    fetchListStat();
    Taro.stopPullDownRefresh();
  });

  useReachBottom(() => {
    fetchList(state.page + 1);
  });

  const fetchStat = async () => {
    const res = await CusApi.getInvestorStat({
      shopId: router.params.id,
    });
    // const stat = list.find((n) => n.shopId === router.params.id) ?? {};
    setState({ stat: res?.[0] || {} });
    console.log(res);
  };

  const fetchList = async (page) => {
    if (page == 1) {
      setState({ page: 1, more: true, list: [] });
    } else if (!state.more || state.loading) {
      return;
    }

    setState({ loading: true });

    const params = {
      pageNum: page,
      pageSize: state.size,
      shopId: router.params.id,
      startCreateDate: dayjs(state.dateCrt, "YYYY-MM")
        .startOf("month")
        .format("YYYY-MM-DD"),
      endCreateDate: dayjs(state.dateCrt, "YYYY-MM")
        .endOf("month")
        .format("YYYY-MM-DD"),
    };

    const res = await CusApi.getInvestorOrder(params).finally(() =>
      setState({ loading: false })
    );

    setState((prev) => ({
      more: res.hasNextPage,
      page: res.pageNum,
      size: res.pageSize,
      list: [...prev.list, ...(res?.list || [])],
    }));
  };

  const fetchListStat = async () => {
    const data = {
      shopId: router.params.id,
      startCreateDate: dayjs(state.dateCrt, "YYYY-MM")
        .startOf("month")
        .format("YYYY-MM-DD"),
      endCreateDate: dayjs(state.dateCrt, "YYYY-MM")
        .endOf("month")
        .format("YYYY-MM-DD"),
    };
    const res = await CusApi.getInvestorOrderStat(data);
    setState({ dateStat: res?.totalRevenue || 0 });
    // console.log(res);
  };

  return (
    <View className={Style.page}>
      <View className={Style.card}>
        <View className={Style.item}>
          <View className={Style.name}>今日营业额（元）</View>
          <View className={Style.num}>{state.stat?.todayRevenue ?? "--"}</View>
        </View>
        <View className={Style.item}>
          <View className={Style.name}>本月营业额（元）</View>
          <View className={Style.num}>
            {state.stat?.nowMonthRevenue ?? "--"}
          </View>
        </View>
      </View>

      <View className={Style.filterBox}>
        <Picker
          mode="date"
          fields="month"
          end={dayjs().format("YYYY-MM-DD")}
          value={state.dateCrt}
          onChange={(e) => {
            setState({ dateCrt: e.detail.value });
          }}
        >
          <View className={Style.left}>
            {dayjs(state.dateCrt, "YYYY-MM").format("YYYY年M月")}
          </View>
        </Picker>

        <View className={Style.right}>营业额&nbsp;&yen;{state.dateStat}</View>
      </View>

      <View className="grid cols-3 gap-1">
        <View className="text-(30 #333 center) p-20 fw-bold bg-#fff">日期</View>
        <View className="text-(30 #333 center) p-20 fw-bold bg-#fff">金额</View>
        <View className="text-(30 #333 center) p-20 fw-bold bg-#fff">
          来源用户
        </View>
        {state.list.map((item) => (
          <React.Fragment key={item.id}>
            <View className="text-(26 #333 center) p-15 bg-#fff">
              {dayjs(item.payDate).format("YYYY-MM-DD")}
            </View>
            <View className="text-(26 #333 center) p-15 bg-#fff">
              {item.payMoney}
            </View>
            <View className="text-(26 #333 center) p-15 bg-#fff">
              {privatePhone(item.shopVipUserMobile)}
            </View>
          </React.Fragment>
        ))}
      </View>

      <View className={Style.footer}>
        {state.more ? "加载中..." : "没有更多数据了..."}
      </View>
    </View>
  );
};

export default InvestorShop;
