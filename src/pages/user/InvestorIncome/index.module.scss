.page {
  padding: 50px;
}

.card {
  height: 347px;
  background: linear-gradient(114.1054deg, #dabc8a 0%, #d3ac6e 100%);
  border-radius: 40px;
  margin-bottom: 50px;
  display: flex;
  align-items: center;
  color: #fff;

  .item {
    flex: 1;
    padding-left: 66px;
  }

  .name {
    font-size: 37px;
  }
  .num {
    margin-top: 40px;
    font-size: 62px;
    font-weight: bold;
  }
}

.table {
  font-size: 34px;

  .link {
    color: #d0ab6b;
  }

  :global {
    .nut-table__summary {
      display: none;
    }

    .nut-table__main__head__tr__th,
    .nut-table__main__body__tr__td {
      border: 0;
      padding: 50px 30px;
      text-align: center;
    }

    .nut-table__main__head__tr__th {
      background: #d0ab6b;
      color: #fff;
    }
  }
}

.footer {
  font-size: 34px;
  color: #999;
  text-align: center;
  padding: 40px;
}

.filterBox {
  display: flex;
  align-items: center;
  height: 120px;

  .left {
    font-size: 44px;
    color: #000;
    padding: 0 40px;
    height: 80px;
    line-height: 80px;
    text-align: center;
    border-radius: 40px;
    background: #eee;
  }

  .right {
    font-size: 36px;
    color: #999;
    margin-left: auto;
  }
}
