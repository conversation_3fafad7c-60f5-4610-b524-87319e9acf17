import { useList } from "@/stores/useList";
import { img_avatar } from "@/utils/images";
import { makeApi } from "@/utils/request";
import { Image, View } from "@tarojs/components";
import Taro, { useReachBottom, useRouter } from "@tarojs/taro";
import { useMount } from "ahooks";
import clsx from "clsx";
import { useEffect, useMemo, useState } from "react";

const tabs = [
  { id: 0, name: "关注" },
  { id: 1, name: "粉丝" },
];

const getFans = makeApi("get", `/api/appUserRelation/query_fans_list`);
const getFollows = makeApi("get", `/api/appUserRelation/query_follow_list`);
const doFollow = makeApi("post", `/api/appUserRelation/follow`);
const unFollow = makeApi("post", `/api/appUserRelation/cancel_follow`);

export default function Page() {
  const router = useRouter();
  const [state, setState] = useState({
    idx: Number(router.params.idx ?? 0),
  });

  useMount(() => {
    const name = router.params.name;
    if (name) {
      Taro.setNavigationBarTitle({ title: name });
    }
  });

  useEffect(() => {
    listAct?.reload();
  }, [state.idx]);

  useReachBottom(() => {
    listAct?.getNext();
  });

  const [fans, fansAct] = useList({
    api: getFans,
    params: { appUserId: router.params.id },
  });

  const [follows, followsAct] = useList({
    api: getFollows,
    params: { appUserId: router.params.id },
  });

  const [list, listAct] = useMemo(() => {
    if (state.idx == 0) return [follows, followsAct];
    return [fans, fansAct];
  }, [state.idx, fans, fansAct, follows, followsAct]);

  const onFollow = async (uid, follow = false) => {
    if (follow) {
      const md = await Taro.showModal({ title: "确定要取消关注吗？" });
      if (!md.confirm) return;
      await unFollow({ targetUserId: uid });
    } else {
      await doFollow({ targetUserId: uid });
    }
    // showToast("操作成功");
    listAct.reload();
  };

  return (
    <View className="min-h-100vh bg-#fff">
      <View className="flex items-center justify-center py-20">
        {tabs.map((item) => (
          <View
            className="px-65"
            key={item.id}
            onClick={() => setState({ idx: item.id })}
          >
            <View
              className={clsx(
                "text-(28 #11100F) fw-bold",
                state.idx == item.id && "!text-#8E4E36"
              )}
            >
              {item.name}
            </View>
            <View
              className={clsx(
                "w-25 h-5 bg-#8E4E36 mx-a mt-5",
                state.idx !== item.id && "invisible"
              )}
            ></View>
          </View>
        ))}
      </View>
      <View className="">
        {list?.list?.map((item) => (
          <View
            className="flex items-center py-20 px-30"
            onClick={() =>
              Taro.navigateTo({
                url: `/pages/user/user/index?id=${item.userData?.id}`,
              })
            }
          >
            <Image
              className="size-80 rounded-full bg-#eee overflow-hidden"
              mode="aspectFill"
              src={item.userData?.photo || img_avatar}
            />
            <View className="flex-1 min-w-0 px-20">
              <View className="text-(24 #11100F)">{item.userData?.name}</View>
              <View className="text-(20 #999) line-clamp-1 mt-3">
                {item.userData?.signature || "这个人很神秘，什么都没留下"}
              </View>
            </View>
            {item.followTag ? (
              <View
                className="text-(23 #5A5A5A) b-(1 solid #5A5A5A) rounded-full px-26 py-8"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  onFollow(item.userData?.id, true);
                }}
              >
                已关注
              </View>
            ) : (
              <View
                className="text-(23 #8E4E36) b-(1 solid #8E4E36) rounded-full px-26 py-8"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  onFollow(item.userData?.id, false);
                }}
              >
                关注
              </View>
            )}
          </View>
        ))}
      </View>
      <View className="py-30 text-(20 #999 center)">
        {list.more ? "加载中..." : "没有更多数据了"}
      </View>
    </View>
  );
}
