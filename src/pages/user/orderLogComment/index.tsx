import { CusNoticeBar } from "@/components/Custom/NoticeBar";
import { makeApi } from "@/utils/request";
import { Image, Textarea, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { Rate } from "@taroify/core";
import CusApi from "@/services/CusApi";
import clsx from "clsx";
import { fdate, showToast } from "@/utils/tools";
import { CusUpload } from "@/components/Custom/Upload";
import { CheckBox } from "@/components/CheckBox";

const getInfo = makeApi("get", `/api/mallServiceOrder/query_one`);
const addComment = makeApi(
  "post",
  `/api/mallServiceOrder/evaluation_service_order`
);

export default function Page() {
  const router = useRouter();
  const [state, setState] = useSetState({
    serviceOrderId: router.params.id || "",

    info: null as any,
    tags: [] as any[],
    tagIds: [] as any[],
    files: [] as any[],

    rate1: 5,
    rate2: 5,
    cont: "",
    imgs: "",
    checked: false,
  });

  useMount(() => {
    fetchInfo();
    fetchTags();
  });

  const fetchInfo = async () => {
    const res = await getInfo({ id: state.serviceOrderId });
    console.log(res);
    setState({ info: res });
  };

  const fetchTags = async () => {
    const res = await CusApi.getDict({ typeEnCode: "ServiceEvaluationLabel" });
    const tags = res || [];
    setState({ tags });
  };

  const handleSubmit = async () => {
    const data = {
      serviceOrderId: router.params.id,
      anonymousTag: Number(state.checked),
      doctorEvaluationScore: state.rate1,
      evaluationScore: state.rate2,
      evaluationDesc: state.cont,
      evaluationImage: state.imgs,
    };
    // console.log(data);

    if (!state.cont) return showToast("请输入评价内容");

    Taro.showLoading({ title: "提交中...", mask: true });
    await addComment(data).finally(() => Taro.hideLoading());
    await showToast("评价成功");
    Taro.navigateBack();
  };

  return (
    <View className="min-h-100vh bg-#fff">
      <View className="bg-#F2ECE0 pos-sticky top-0">
        <CusNoticeBar>
          提交您的评价和建议，可以帮助我们做得更好~ 评价即可获得5M币~
        </CusNoticeBar>
      </View>

      <View className="px-50 pb-300">
        <View className="flex items-center gap-20 py-50">
          <Image
            className="size-100 bg-#000 rounded-10"
            mode="aspectFill"
            src=""
          />
          <View className="flex-1 min-w-0">
            <View className="text-(28 #111) line-clamp-1 fw-500">
              {state.info?.shop?.name}
            </View>
            <View className="text-(24 #999) line-clamp-1 mt-5">
              {state.info?.shop?.address}
            </View>
          </View>
        </View>

        <View className="flex items-center py-20 pt-0">
          <View className="text-(26 #141414) w-150">治疗医生</View>
          <View className="text-(24 #79787E)">{state.info?.doctorName}</View>
        </View>

        <View className="flex items-center py-20">
          <View className="text-(26 #141414) w-150">治疗时间</View>
          <View className="text-(24 #79787E)">
            {fdate(state.info?.createDate, "YYYY.M.D HH:mm")}
          </View>
        </View>

        <View className="flex items-center py-20">
          <View className="text-(26 #141414) w-150">治疗单号</View>
          <View className="text-(24 #79787E)">{state.info?.serialNo}</View>
          <View className="text-(24 #79787E) ml-20">复制</View>
        </View>

        <View className="b-b-(1 solid #e5e5e5) my-30"></View>

        {`医疗交付 到院服务`.split(" ").map((name, idx) => {
          const key = "rate" + (idx + 1);

          const rate2txt = (n) => {
            return "非常差 差 一般 好 非常好".split(" ")[n - 1] || "";
          };

          return (
            <View className="flex items-center py-25">
              <View>{name}</View>
              <View className="ml-a">
                <Rate
                  style={
                    {
                      "--rate-icon-empty-color": "#CDCDCD",
                      "--rate-icon-full-color": "#8E4E36",
                      "--rate-icon-gutter": "35rpx",
                      "--rate-icon-size": "48rpx",
                    } as any
                  }
                  value={state[key]}
                  onChange={(e) => setState({ [key]: e } as any)}
                />
              </View>
              <View className="text-(20 #000) w-60 pl-30">
                {rate2txt(state[key])}
              </View>
            </View>
          );
        })}

        <View className="flex flex-wrap gap-20 py-30">
          {state.tags.map((item) => (
            <View
              key={item.id}
              className={clsx(
                "h-50 b-(1 solid #F5F5F5) rounded-full text-(22/50 #333 center) bg-#EEEAE8 px-40",
                state.tagIds.includes(item.id) && "!b-#AD7F6D !text-#A55E44"
              )}
              onClick={() => {
                const ids = [...state.tagIds];
                if (ids.includes(item.id)) {
                  ids.splice(ids.indexOf(item.id), 1);
                } else {
                  ids.push(item.id);
                  setState({ cont: state.cont + " # " + item.name + " " });
                }
                setState({ tagIds: ids });
              }}
            >
              # {item.name}
            </View>
          ))}
        </View>

        <View className="bg-#FAFAFA min-h-250 rounded-10 overflow-hidden p-20">
          <Textarea
            className="block w-full p-0 m-0 h-a min-h-200 !text-(26/50 #1E1C1D)"
            // placeholder="评价15字以上可获得5M币"
            placeholder="请输入评价内容"
            value={state.cont}
            onInput={(e) => setState({ cont: e.detail.value })}
          />
          <View className="pt-30 ![&_.taroify-uploader\_\_upload]:(b-(1 dashed #aaa) rounded-10)">
            <CusUpload max={3} onChange={(e) => setState({ imgs: e })} />
          </View>
        </View>
      </View>

      <View className="pos-fixed bottom-0 left-0 w-full bg-#fff">
        <View className="px-50 pb-50 pt-30 flex items-center">
          <View
            className="flex items-center"
            onClick={() => setState({ checked: !state.checked })}
          >
            <CheckBox className="size-30 text-#999" checked={state.checked} />
            <View className="text-(26 #999) ml-10">匿名评价</View>
          </View>
          <View
            className="ml-a w-400 h-100 rounded-20 bg-#8E4E36 text-(32/100 #fff center)"
            onClick={handleSubmit}
          >
            发布评价
          </View>
        </View>
      </View>
    </View>
  );
}
