import { View } from "@tarojs/components";
import { FriendCardBasePage } from "../friend-card-units/base-page";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { friendInfo } from "../friend-card-units/api-friend";
import { useShare } from "@/stores/useShare";

export default function Page() {
  const router = useRouter();
  const tid = router.params.id;

  useShare(() => {
    return {
      title: "好友组队，同享优惠",
      imageUrl:
        "https://oss.gaomei168.com/file-release/20250505/1368918047754481664_750_616.jpg",
      path: `/pages/mall3/friend-card-share-root/index?id=${tid}`,
    };
  });

  // prettier-ignore
  useDidShow(async () => {
    const token = Taro.getStorageSync("token");
    
    if (!token) {
      return Taro.navigateTo({ url: `/pages/mall/login/index` });
    }

    const info = await friendInfo({ id: tid }, { _loading: true });
    console.log("root page",info);

    // 发布人 - 未组队 - 去分享
    if (info.togetherTag == 1 && info.state == 10) {
      Taro.redirectTo({ url: `/pages/mall3/friend-card-create/index?id=${tid}` });
    }

    // 发布人 - 已组队 - 去支付
    else if (info.togetherTag == 1 && info.state == 20) {
      Taro.redirectTo({ url: `/pages/mall3/friend-card-success/index?id=${tid}` });
    }

    // 发布人 - 失败 - 失败
    // else if (info.togetherTag == 1 && info.state == 21) {}

    // 发布人 - 已支付 - 已结束
    else if (info.togetherTag == 1 && info.state == 30) {
      Taro.redirectTo({ url: `/pages/mall3/friend-card-expired/index?id=${tid}` });
    }

    // 接收人 - 未组队 - 去组队
    else if (info.state == 10) {
      Taro.redirectTo({ url: `/pages/mall3/friend-card-join/index?id=${tid}` });
    }

    // 接收人 - 已组队 - 等待支付
    else if (info.togetherTag == 2 && info.state == 20) {
      Taro.redirectTo({ url: `/pages/mall3/friend-card-join/index?id=${tid}` });
    }

    // 发布人 - 失败 - 失败
    // else if (info.togetherTag == 2 && info.state == 21) {}

    // 接收人 - 已支付 - 已完成
    else if (info.togetherTag == 2 && info.state == 30) {
      Taro.redirectTo({ url: `/pages/mall3/friend-card-expired/index?id=${tid}` });
    }

    // 其他人 - any - 已完成
    else {
      Taro.redirectTo({ url: `/pages/mall3/friend-card-expired/index?id=${tid}` });
    }


  });

  return (
    <FriendCardBasePage>
      <View className="text-(24 #999 center) py-100">加载中...</View>
    </FriendCardBasePage>
  );
}
