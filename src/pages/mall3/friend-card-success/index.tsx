import { Image, View } from "@tarojs/components";
import { FriendCardBasePage } from "../friend-card-units/base-page";
import { useMount, useSetState } from "ahooks";
import Taro, { useRouter } from "@tarojs/taro";
import { friendInfo, friendPay } from "../friend-card-units/api-friend";
import { img_avatar } from "@/utils/images";

export default function Page() {
  const router = useRouter();
  const tid = router.params.id;
  // const tid = "1368195377731620864";
  const [state, setState] = useSetState({
    info: null as any,
    loading: false,
  });

  useMount(async () => {
    const info = await friendInfo({ id: tid });
    setState({ info });
  });

  const onPay = async () => {
    if (state.loading) return;
    setState({ loading: true });
    const res = await friendPay(
      {
        orderId: tid,
        wxAppId: process.env.TARO_APP_ID,
      },
      { _loading: true }
    ).finally(() => {
      setState({ loading: false });
    });
    console.log(res);
    Taro.requestPayment({
      ...res,
      package: res.packgeStr,
      success: () => {
        Taro.showModal({
          title: "支付成功",
          showCancel: true,
          cancelText: "返回首页",
          confirmText: "查看订单",
          success: (res) => {
            if (res.cancel) {
              Taro.reLaunch({ url: `/pages/tabs/mall/index` });
            } else {
              Taro.redirectTo({ url: `/pages/mall2/orderList/index` });
            }
          },
        });
      },
    });
  };

  return (
    <FriendCardBasePage>
      <Image
        className="w-742 h-389 block mx-a"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250503/1368209715334934528_1484_778.png"
      />
      <Image
        className="w-360 h-360 block mx-a mt-30"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250503/1368229451296862208_720_718.png"
      />
      <View className="flex gap-80 items-center justify-center mt-20">
        <View className="pos-relative size-136">
          <Image
            className="size-full rounded-full bg-#eee"
            mode="aspectFill"
            src={state.info?.userList?.[0]?.photo || img_avatar}
          />
          <View className="pos-absolute -bottom-10 left-1/2 bg-#86A763 rounded-full py-8 px-10 ws-nowrap text-(22 #fff) -translate-x-1/2">
            发起人
          </View>
        </View>

        <View className="pos-relative size-136">
          <Image
            className="size-full rounded-full bg-#eee"
            mode="aspectFill"
            src={state.info?.userList?.[1]?.photo || img_avatar}
          />
        </View>
      </View>

      {state.info?.togetherTag == 2 && state.info?.state == 20 && (
        <View className="text-(25 #101010 center) py-15 mt-30">
          等待发起人完成支付即可
        </View>
      )}

      <View className="mt-80">
        {state.info?.togetherTag == 1 && state.info?.state == 20 && (
          <View
            className="w-630 h-90 rounded-full b-(1 solid #25262A) bg-#25262A text-(#F5BFCD 42/90 center) mx-a mt-20"
            onClick={onPay}
          >
            去支付
          </View>
        )}
        <View
          className="w-630 h-90 rounded-full b-(1 solid #25262A) bg-transparent text-(#25262A 42/90 center) mx-a mt-20"
          onClick={() => {
            Taro.reLaunch({ url: `/pages/tabs/mall/index` });
          }}
        >
          返回首页
        </View>
      </View>
    </FriendCardBasePage>
  );
}
