import { Image, View } from "@tarojs/components";
import { FriendCardBasePage } from "../friend-card-units/base-page";
import Taro from "@tarojs/taro";

export default function Page() {
  return (
    <FriendCardBasePage>
      <Image
        className="w-742 h-389 block mx-a"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250503/1368209715334934528_1484_778.png"
      />
      <Image
        className="w-360 h-360 block mx-a mt-40"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250503/1368241468707631104_718_718.png"
      />

      <Image
        className="block w-668 h-155 mt-80 mx-a"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250502/1367865224598056960_668_155.png"
      />

      <View className="mt-40">
        <View
          className="w-630 h-90 rounded-full b-(1 solid #25262A) bg-#25262A text-(#F5BFCD 42/90 center) mx-a mt-20"
          onClick={() => {
            Taro.navigateTo({ url: `/pages/mall2/orderList/index` });
          }}
        >
          查看我的订单
        </View>
        <View
          className="w-630 h-90 rounded-full b-(1 solid #25262A) bg-transparent text-(#25262A 42/90 center) mx-a mt-20"
          onClick={() => {
            Taro.reLaunch({ url: `/pages/tabs/mall/index` });
          }}
        >
          返回首页
        </View>
      </View>
    </FriendCardBasePage>
  );
}
