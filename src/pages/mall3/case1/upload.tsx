import { Image, View } from "@tarojs/components";
import Taro from "@tarojs/taro";

export const Upload = (props: {
  value?: string;
  onChange?: any;
  holder?: any;
  disabled?: boolean;
}) => {
  const upload = async () => {
    if (props.disabled) return;

    const res = await Taro.chooseMedia({
      count: 1,
      mediaType: ["image"],
      sizeType: ["compressed"],
    });

    const path = res.tempFiles?.[0]?.tempFilePath || "";
    if (!path) return;

    Taro.showLoading({ title: "上传中..." });

    try {
      const res2 = await Taro.uploadFile({
        url: process.env.TARO_APP_UPLOAD || "",
        filePath: path,
        name: "file",
        header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
      });

      let data: any = null;

      try {
        data = JSON.parse(res2.data);
      } catch (err) {}

      const url = data?.data?.path || "";
      props.onChange?.(url);
    } finally {
      Taro.hideLoading();
    }
  };

  return (
    <View className="h-220" onClick={upload}>
      {props.value ? (
        <Image
          className="size-full"
          mode="aspectFill"
          src={props.value || ""}
        />
      ) : (
        <View className="box-border size-full b-(1 dashed #B5B5B5) rounded-4 flex flex-col items-center justify-center">
          <View className="pos-relative size-44 mb-30">
            <View className="pos-absolute top-1/2 left-1/2 w-1 h-full bg-#A1A1A1 -translate-1/2"></View>
            <View className="pos-absolute top-1/2 left-1/2 w-1 h-full bg-#A1A1A1 -translate-1/2 rotate-90"></View>
          </View>
          <View className="text-(23 #A1A1A1) flex items-center">
            <View>{props.holder}</View>
            <View className="text-red ml-8 pos-relative -top-4">*</View>
          </View>
        </View>
      )}
    </View>
  );
};
