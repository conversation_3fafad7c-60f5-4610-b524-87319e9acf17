import { usePageStat } from "@/stores/usePageStat";
import { useShare } from "@/stores/useShare";
import { Image, Input, Text, Textarea, View } from "@tarojs/components";
import { Upload } from "./upload";
import { useMount, useSetState } from "ahooks";
import { UnoIcon } from "@/components/UnoIcon";
import Taro, { useRouter } from "@tarojs/taro";
import { makeApi } from "@/utils/request";
import { openConcat, showToast, str2arr } from "@/utils/tools";
import { getUserByToken } from "@/services/UserApi";

const photoData = [
  { key: "img1", holder: "上传正面照" },
  { key: "img2", holder: "上传左45°照" },
  { key: "img3", holder: "上传右45°照" },
];

const getCase = makeApi("get", `/api/appRecruitmentInfo/query_one`);
const getInfo = makeApi("get", `/api/appRecruitmentLog/query_last`);
const addInfo = makeApi("post", `/api/appRecruitmentLog/apply`);

const DEBUG = false;

export default function Page() {
  const router = useRouter();
  const [state, setState] = useSetState({
    id: DEBUG ? "1377301755912478720" : router.params.id || "",
    case: null as any,
    isPosted: false,

    name: "",
    phone: "",
    age: "",
    cont: "",
    img1: "",
    img2: "",
    img3: "",
  });

  useShare(() => {
    return {
      title: "案例招募",
      path: `/pages/mall3/case1/index?id=${state.id}`,
    };
  });

  usePageStat({
    id: state.id,
    title: "案例招募",
  });

  useMount(() => {
    init();
  });

  const init = () => {
    fetchCase();
    fetchInfo();
  };

  const inputProp = (key) => {
    return {
      disabled: state.isPosted,
      value: state[key],
      onInput: (e) => setState({ [key]: e.detail.value } as any),
    };
  };

  const fetchCase = async () => {
    const res = await getCase({ id: state.id });
    setState({ case: res });
    // console.log("case", res);
  };

  const fetchInfo = async () => {
    const res = await getInfo({ recruitmentId: state.id });

    if (!res?.id) {
      const user = await getUserByToken();
      const phone = user?.mobile || "";
      setState({ phone, isPosted: false });
      return;
    }

    const imgs = str2arr(res?.submitterImage);

    const data = {
      name: res?.submitterName || "",
      phone: res?.submitterMobile || "",
      age: res?.submitterAge || "",
      cont: res?.submitterContent || "",
      img1: imgs?.[0] || "",
      img2: imgs?.[1] || "",
      img3: imgs?.[2] || "",
    };

    setState({ ...data, isPosted: true });
    // console.log("info", res);
  };

  const submit = async () => {
    const obj = {
      name: "请输入姓名",
      phone: "请输入手机号",
      age: "请输入年龄",
      cont: "请输入需求",
      img1: "请上传照片-正面",
      img2: "请上传照片-左",
      img3: "请上传照片-右",
    };

    for (const key of Object.keys(obj)) {
      if (!state[key]) return showToast(obj[key]);
    }

    const data = {
      recruitmentId: state.id,
      submitterName: state.name,
      submitterMobile: state.phone,
      submitterAge: state.age,
      submitterContent: state.cont,
      submitterImage: [state.img1, state.img2, state.img3].join(","),
    };

    Taro.showLoading({ title: "提交中..." });
    await addInfo(data);
    Taro.hideLoading();
    const md = await Taro.showModal({
      title: "您已提交成功",
      showCancel: false,
    });
    if (md.confirm) {
      init();
    }
  };

  return (
    <View className="min-h-100vh overflow-hidden bg-#F2F2F2">
      <Image
        className="block w-full h-a"
        mode="widthFix"
        src={state.case?.image || ""}
      />

      <View
        className="w-480 h-70 mt-50 mb-100 mx-a bg-#25262A rounded-full text-(32 #F4D6CC center) flex items-center justify-center"
        onClick={() => {
          const cont = encodeURIComponent(state.case.content);
          Taro.navigateTo({ url: `/pages/mall/rich/index?html=${cont}` });
        }}
      >
        <View>点击查看详情</View>
        <UnoIcon className="i-icon-park-outline:point-out text-36 ml-10 -rotate-20" />
      </View>

      <View className="text-(46 #000000 center)">招募报名表</View>
      <View className="text-(20 #a4a4a4 center)">Case Recruitment</View>

      <View className="mt-44 mx-15 h-20 -mb-28 rounded-full bg-#EAEAEA b-(18 solid #E5E1DA)"></View>
      <View className="mx-38 bg-#fff min-h-500 p-50 pt-20">
        <View className="pt-30 pb-20 text-(27 #313130)">
          <Text>姓名</Text>
          <Text className="text-red">*</Text>
        </View>

        <Input
          className="b-(1 solid #898989) rounded-15 h-70 px-20"
          {...inputProp("name")}
        />

        <View className="pt-30 pb-20 text-(27 #313130)">
          <Text>手机号</Text>
          <Text className="text-red">*</Text>
        </View>

        <Input
          className="b-(1 solid #898989) rounded-15 h-70 px-20"
          type="number"
          {...inputProp("phone")}
        />

        <View className="pt-30 pb-20 text-(27 #313130)">
          <Text>年龄</Text>
          <Text className="text-red">*</Text>
        </View>

        <Input
          className="b-(1 solid #898989) rounded-15 h-70 px-20"
          type="number"
          {...inputProp("age")}
        />

        <View className="pt-30 pb-20 text-(27 #313130)">
          <Text>皮肤状态，期待改善的效果</Text>
          <Text className="text-red">*</Text>
        </View>

        <Textarea
          className="block box-border b-(1 solid #898989) rounded-15 w-full h-200 p-20"
          {...inputProp("cont")}
        />

        <View className="pt-30 pb-20 text-(27 #313130)">
          <Text>照片示例：正面、左右45°各1张</Text>
          <Text className="text-red">*</Text>
        </View>

        <View className="grid cols-3 gap-30">
          <Image
            className="w-a h-220 bg-#eee"
            mode="aspectFill"
            src="https://oss.gaomei168.com/file-release/20250528/1377336038762409984_495_645.png"
          />
          <Image
            className="w-a h-220 bg-#eee"
            mode="aspectFill"
            src="https://oss.gaomei168.com/file-release/20250528/1377336038871461888_417_543.png"
          />
          <Image
            className="w-a h-220 bg-#eee"
            mode="aspectFill"
            src="https://oss.gaomei168.com/file-release/20250528/1377336038963736576_635_824.png"
          />
        </View>

        <View className="grid cols-3 gap-30 mt-30">
          {photoData.map((n) => (
            <Upload
              key={n.key}
              value={state[n.key]}
              onChange={(e) => setState({ [n.key]: e } as any)}
              holder={n.holder}
              disabled={state.isPosted}
            />
          ))}
        </View>
      </View>

      <View className="pt-60 pb-200">
        {!state.isPosted && (
          <View
            className="w-480 h-70 mx-a bg-#25262A rounded-full text-(32 #F4D6CC center) flex items-center justify-center"
            onClick={submit}
          >
            <View>点击提交</View>
          </View>
        )}
        {state.isPosted && (
          <View className="px-50">
            <View className="text-(34 #111 center) fw-bold mb-20">
              感谢您的参与！
            </View>
            <View className="text-(26/40 #666 center)">
              我们已收到您的信息，会尽快对您提交的内容进行评估。我们的工作人员将在
              3个工作日内与您联系，请保持电话畅通。如有任何疑问，欢迎随时联系我们
              。
            </View>
          </View>
        )}
      </View>

      <View className="z-50 pos-fixed bottom-80 right-30">
        <View
          className="pos-relative size-80 bg-#AD7F6D rounded-full flex items-center justify-center mb-30"
          onClick={openConcat}
        >
          <UnoIcon className="i-icon-park-outline:comment size-38 text-#fff" />
        </View>
      </View>
    </View>
  );
}
