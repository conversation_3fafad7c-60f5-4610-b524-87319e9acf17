import { makeApi } from "@/utils/request";

export const getRule = makeApi("get", `/api/mallMarketProduct/query_rule`);
// prettier-ignore
export const friendCrt = makeApi("get", `/api/mallTogether/get_current_together`);
export const friendAdd = makeApi("post", `/api/mallTogether/open`);
export const friendJoin = makeApi("post", `/api/mallTogether/join`);
export const friendInfo = makeApi("get", `/api/mallTogether/query_one`);
export const friendPay = makeApi("post", `/api/mallTogether/wx/pre_buy`);
