import { CusPopup } from "@/components/Custom/PopUp";
import RichContent from "@/components/RichContent";
import { UnoIcon } from "@/components/UnoIcon";
import { Text, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { getRule } from "./api-friend";
import { FriendCardBubble } from "./friend-card-bubble";

export const FriendCardBasePage = (props: { children?: any }) => {
  const [state, setState] = useSetState({
    rule: null as any,
    ruleOpen: false,
  });

  useMount(() => {
    fetchRule();
  });

  const fetchRule = async () => {
    try {
      const rule = await getRule();
      setState({ rule });
    } catch (error) {
      console.error("获取规则失败", error);
    }
  };

  return (
    <View className="min-h-100vh box-border pb-100 bg-[linear-gradient(to_bottom,#E9CFCF,#FFFFFF_20%,#FFFFFF_30%,#E9CFCF)]">
      <View
        style={{
          paddingTop: 48,
          paddingRight: 375 - 281 + 0,
          paddingLeft: 0,
          paddingBottom: 4,
          height: 32,
        }}
        className="z-100 pos-sticky top-0"
      >
        <View className="h-full flex items-center justify-between px-30">
          <View
            className="size-50 rounded-full bg-#000/5 flex items-center justify-center"
            onClick={() =>
              Taro.navigateBack({
                fail: () => Taro.reLaunch({ url: "/pages/tabs/mall/index" }),
              })
            }
          >
            <UnoIcon className="i-icon-park-outline:left size-40 text-#fff" />
          </View>
          <View onClick={() => setState({ ruleOpen: true })}>
            <Text className="text-(#2F2F2F 22)">规则 &gt;</Text>
          </View>
        </View>
      </View>

      <View className="px-50 pt-10">
        {/* <View className="pos-fixed top-200 left-30"> */}
        <FriendCardBubble />
      </View>

      {props.children}

      <CusPopup
        open={state.ruleOpen}
        onClose={() => setState({ ruleOpen: false })}
        title={"朋友卡规则"}
      >
        <View className="px-60 pt-10 pb-100 max-h-60vh overflow-y-auto">
          {state.rule ? (
            <RichContent html={state.rule.togetherMallRuleContent} />
          ) : (
            <View className="py-30 text-center text-#999">加载中...</View>
          )}
        </View>
      </CusPopup>
    </View>
  );
};
