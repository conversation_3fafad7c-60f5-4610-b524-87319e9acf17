import { img_avatar } from "@/utils/images";
import { makeApi } from "@/utils/request";
import { Image, View } from "@tarojs/components";
import { useMount, useSetState, useUnmount } from "ahooks";
import { useRef } from "react";

const getList = makeApi("get", `/api/appMessage/together/query_list`);

// 定义消息切换的间隔时间（毫秒）
const MESSAGE_INTERVAL = 5000; // 5秒
// 定义动画持续时间（毫秒）
const ANIMATION_DURATION = 600; // 0.6秒，更快的动画效果
// 定义消息隐藏后的停顿时间（毫秒）
const PAUSE_DURATION = 5000; // 1秒，可以根据需要调整

export const FriendCardBubble = () => {
  const [state, setState] = useSetState({
    list: [] as any[],
    currentIndex: 0,
    visible: false,
    animating: false, // 添加动画状态标记
  });

  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useMount(() => {
    fetchList();
  });

  useUnmount(() => {
    // 组件卸载时清除定时器
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  });

  const fetchList = async () => {
    const res = await getList({});
    const list = res?.list || [];
    setState({ list });

    // 如果获取到了消息列表，开始显示消息
    if (list.length > 0) {
      startMessageCycle();
    }
  };

  // 开始消息循环显示
  const startMessageCycle = () => {
    showMessage();

    // 设置定时器，定期切换消息
    timerRef.current = setInterval(() => {
      hideMessage(() => {
        setState((prev) => ({
          currentIndex: (prev.currentIndex + 1) % prev.list.length,
        }));
        showMessage();
      });
    }, MESSAGE_INTERVAL);
  };

  // 显示消息（向上淡入）
  const showMessage = () => {
    // 先重置到初始位置（在下方）
    setState({
      visible: false,
      animating: false,
    });

    // 强制重绘，确保元素先回到初始位置
    setTimeout(() => {
      // 然后设置为显示状态，触发向上淡入动画
      setState({
        visible: true,
        animating: true,
      });
    }, 50); // 短暂延时确保状态更新
  };

  // 隐藏消息（向上淡出）
  const hideMessage = (callback?: () => void) => {
    // 设置为淡出状态，触发向上淡出动画
    setState({
      visible: false,
      animating: true, // 标记为动画中，这样会应用向上移动的样式
    });

    // 动画结束后重置状态
    setTimeout(() => {
      // 将元素重置到初始位置（下方）
      setState({ animating: false });

      // 停顿指定时间后再执行回调（显示下一个元素）
      setTimeout(() => {
        callback && callback();
      }, PAUSE_DURATION);
    }, ANIMATION_DURATION);
  };

  const currentMessage = state.list[state.currentIndex] || {};

  return (
    <View
      key={state.currentIndex}
      className="inline-block transition-all duration-600 ease-in-out"
      style={{
        opacity: state.visible ? 1 : 0,

        transform: state.visible
          ? "translateY(0)" // 显示状态：原位
          : state.animating
          ? "translateY(-10rpx)" // 动画中且不可见：向上淡出
          : "translateY(10rpx)", // 初始状态：在下方
        pointerEvents: state.visible ? "auto" : "none",
      }}
    >
      {state.list.length > 0 && (
        <View className="inline-flex items-center h-45 px-30 gap-10 bg-#fff/50 rounded-full">
          <Image
            className="size-32 rounded-full overflow-hidden bg-#eee"
            mode="aspectFill"
            src={currentMessage.avatar || img_avatar}
          />
          <View className="text-(22 #101010)">
            {currentMessage.content || ""}
          </View>
        </View>
      )}
    </View>
  );
};
