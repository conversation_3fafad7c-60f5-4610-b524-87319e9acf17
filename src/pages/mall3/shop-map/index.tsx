import { fetchShopsByLocation, showToast, str2arr } from "@/utils/tools";
import { Image, Map, ScrollView, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";

export default function Page() {
  const [state, setState] = useSetState({
    shops: [] as any[],
    mapOpts: {} as any,
    markers: [] as any[],
  });

  useMount(() => {
    fetchShop();
  });

  const fetchShop = async () => {
    let shops = await fetchShopsByLocation();
    shops = shops || [];

    const markers = shops?.map((item) => ({
      id: Number(item.id),
      width: 30,
      height: 30,
      latitude: item.latitude,
      longitude: item.longitude,
      iconPath:
        "https://oss.gaomei168.com/file-release/20250612/1382765516716175360_64_64.png",
      callout: {
        display: "ALWAYS",
        content: item.name,
        padding: 6,

        color: "#333",
        bgColor: "#fff",
        fontSize: 14,
        anchorX: 0,
        anchorY: 0,
        borderWidth: 1,
        borderRadius: 6,
        borderColor: "#eee",
      },
    }));

    const mapOpts = {
      latitude: shops?.[0]?.latitude,
      longitude: shops?.[0]?.longitude,
    };

    setState({ shops, markers, mapOpts });
  };

  return (
    <View className="h-100vh overflow-hidden flex flex-col">
      <View className="h-700 bg-#fff">
        <Map
          className="block size-full"
          onError={() => {}}
          showLocation={true}
          {...state.mapOpts}
          markers={state.markers}
        />
      </View>
      <View className="py-20 px-20 text-(26 #666)">
        {state.shops.length}家门店
      </View>
      <ScrollView className="flex-1 min-h-0" scrollY>
        <View className="px-20">
          <View className="flex flex-col gap-20">
            {state.shops.map((item) => (
              <View
                className="bg-#fff rounded-20 p-30"
                key={item.id}
                onClick={() => {
                  const mapOpts = {
                    latitude: item.latitude,
                    longitude: item.longitude,
                  };
                  setState({ mapOpts });
                  // Taro.navigateTo({
                  //   url: `/pages/mall2/shopInfo/index?id=${item.id}`,
                  // });
                }}
              >
                <View className="flex">
                  <Image
                    className="size-110 bg-#fafafa rounded-10 overflow-hidden"
                    mode="aspectFill"
                    src={item.logo}
                  ></Image>
                  <View className="flex-1 min-w-0 flex flex-col justify-between py-5 px-20">
                    <View className="flex items-center">
                      <View className="flex-1 min-w-0 text-(33 #000)">
                        {item.name}
                      </View>
                      <View className="w-3 h-27 bg-#eee"></View>
                      <Image
                        className="size-32 ml-30"
                        mode="aspectFit"
                        src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238077034488242176_size_4679.png"
                        onClick={(evt) => {
                          evt.stopPropagation();
                          Taro.makePhoneCall({
                            phoneNumber: item.contactPhone,
                          });
                        }}
                      ></Image>
                    </View>
                    <View className="flex items-center">
                      {str2arr(item.mainBusiness).map((item, idx) => (
                        <View
                          key={idx}
                          className="text-(24 #fff) bg-#ad7f6d rounded-10 px-15 py-5 mr-8"
                        >
                          {item}
                        </View>
                      ))}
                    </View>
                  </View>
                </View>

                <View
                  className="flex items-center pt-20"
                  onClick={async (evt) => {
                    evt.stopPropagation();
                    Taro.openLocation({
                      latitude: item.latitude,
                      longitude: item.longitude,
                      scale: 18,
                      name: item.name,
                      address: item.fullAddress,
                      fail: () => showToast("打开地址失败"),
                    });
                  }}
                >
                  <Image
                    className="size-26 shrink-0"
                    mode="aspectFit"
                    src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238077259869167616_size_2330.png"
                  ></Image>
                  <View className="text-(26 #666) px-10">
                    {item.fullAddress}
                  </View>
                  <Image
                    className="size-14 shrink-0"
                    mode="aspectFit"
                    src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238076800756457473_size_464.png"
                  ></Image>
                </View>

                <View className="flex items-center pt-10">
                  <Image
                    className="size-26 shrink-0 invisible"
                    mode="aspectFit"
                    src="https://vip.gaomei168.com/file-release/file/100/2024/0509/10/1238077259869167616_size_2330.png"
                  ></Image>
                  <View className="text-(26 #666) px-10">
                    距离当前位置：{item._dis}
                  </View>
                </View>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
