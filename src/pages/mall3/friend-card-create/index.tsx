import { img_avatar } from "@/utils/images";
import { Button, Image, Text, View } from "@tarojs/components";
import CountDown from "@/components/CountDown";
import { FriendCardBasePage } from "../friend-card-units/base-page";
import { useRouter } from "@tarojs/taro";
import { friendInfo } from "../friend-card-units/api-friend";
import { useMount, useSetState } from "ahooks";
import { useShare } from "@/stores/useShare";

export default function Page() {
  const router = useRouter();
  const tid = router.params.id;
  const [state, setState] = useSetState({
    info: null as any,
  });

  useShare(async () => {
    return {
      title: "好友组队，同享优惠",
      imageUrl:
        "https://oss.gaomei168.com/file-release/20250505/1368918047754481664_750_616.jpg",
      path: `/pages/mall3/friend-card-share-root/index?id=${tid}`,
    };
  });

  useMount(async () => {
    let info: any = await friendInfo({ id: tid });
    setState({ info });
  });

  return (
    <FriendCardBasePage>
      <View className="h-1086 overflow-hidden bg-[size:100%_100%] bg-[url(https://oss.gaomei168.com/file-release/20250502/1367890760900734976_1500_2172.png)]">
        <View className="text-(#25262A 38 center) fw-bold mt-400">
          再邀请<Text className="text-#CD160C mx-3">1</Text>位好友，即可组队成功
        </View>
        <View className="pos-relative w-600 h-88 mt-40 mx-a bg-#25262A rounded-full flex items-center justify-center">
          <Text className="text-42 text-#F4D0D8">立即邀请好友参与</Text>
          <Button
            openType="share"
            className="pos-absolute top-0 left-0 size-full opacity-0"
          />
        </View>
        <View className="flex justify-center items-center mt-30">
          <CountDown
            endTime={state.info?.endDate} // 示例：24小时后结束
            // onFinish={() => showToast("")}
            className="flex items-center"
            renderDigit={(value) => (
              <View className="bg-#898989 rounded-4 min-w-38 px-6 h-34 flex items-center justify-center">
                <Text className="text-(#fff 26/34 center)">{value}</Text>
              </View>
            )}
            renderSeparator={(separator) => (
              <View className="px-5">
                <Text className="text-(26 #111)">{separator}</Text>
              </View>
            )}
          />
          <View className="text-(28 #25262A) ml-10">后组队失效</View>
        </View>
        <View className="flex gap-80 items-center justify-center mt-20">
          <View className="pos-relative size-136">
            <Image
              className="size-full rounded-full bg-#eee"
              mode="aspectFill"
              src={state.info?.publisher?.photo || img_avatar}
            />
            <View className="pos-absolute -bottom-10 left-1/2 bg-#86A763 rounded-full py-8 px-10 ws-nowrap text-(22 #fff) -translate-x-1/2">
              发起人
            </View>
          </View>

          <View className="pos-relative size-136">
            <Image
              className="size-full rounded-full bg-#eee"
              mode="aspectFill"
              src="https://oss.gaomei168.com/file-release/20250502/1367890667069960192_264_264.png"
            />
            <Button
              openType="share"
              className="pos-absolute top-0 left-0 size-full opacity-0"
            />
          </View>
        </View>
        {/* <View className="flex items-center justify-center gap-60 mt-100">
          <Image
            className="size-140 rounded-full overflow-hidden"
            mode="aspectFill"
            src={state.info?.publisher?.photo || img_avatar}
          />
          <Image
            className="size-140"
            mode="scaleToFill"
            src="https://oss.gaomei168.com/file-release/20250502/1367890667069960192_264_264.png"
          />
        </View> */}
      </View>
      <Image
        className="block w-668 h-155 mt-10 mx-a"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250502/1367865224598056960_668_155.png"
      />
    </FriendCardBasePage>
  );
}
