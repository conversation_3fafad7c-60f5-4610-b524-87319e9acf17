// import { useShare } from "@/stores/useShare";
import { Image, Text, View } from "@tarojs/components";
import { FriendCardBasePage } from "../friend-card-units/base-page";
import Taro, { useRouter } from "@tarojs/taro";
import { friendAdd, friendCrt } from "../friend-card-units/api-friend";
import { useShare } from "@/stores/useShare";

// const marketProductId = `1366430712824832000`;

export default function Page() {
  const router = useRouter();
  const marketProductId = router.params.mid;

  useShare(() => {
    return {
      title: "好友组队，同享优惠",
      imageUrl:
        "https://oss.gaomei168.com/file-release/20250505/1368918047754481664_750_616.jpg",
      path: `/pages/mall3/friend-card-home/index?mid=${marketProductId}`,
    };
  });

  return (
    <FriendCardBasePage>
      <View>
        <Image
          className="block w-full h-1100"
          mode="aspectFit"
          src="https://oss.gaomei168.com/file-release/20250502/1367864928572469248_1500_2198.png"
        />

        <Image
          className="block w-668 h-155 mt-40 mx-a"
          mode="aspectFit"
          src="https://oss.gaomei168.com/file-release/20250502/1367865224598056960_668_155.png"
        />

        <View className="h-200"></View>
        <View
          className="z-20 pos-fixed bottom-80 left-75 w-600 h-88 mt-30 mx-a bg-#25262A rounded-full flex items-center justify-center"
          onClick={async () => {
            try {
              await Taro.requestSubscribeMessage({
                tmplIds: ["EX_Zhy3skBqOXjwFzCDtMadt9endFKQesJxco37kkxs"], // 活动结果（同行）
              } as any);
            } catch (err) {}

            let info = await friendCrt({ marketProductId }, { _loading: true });

            if (!info?.id) {
              info = await friendAdd({ marketProductId }, { _loading: true });
            }

            Taro.navigateTo({
              url: `/pages/mall3/friend-card-share-root/index?id=${info?.id}`,
            });
          }}
        >
          <Text className="text-42 text-#F4D0D8">立即参与活动</Text>
        </View>
      </View>
    </FriendCardBasePage>
  );
}
