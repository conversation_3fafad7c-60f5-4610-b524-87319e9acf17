import CountDown from "@/components/CountDown";
import { Image, Text, View } from "@tarojs/components";
import { FriendCardBasePage } from "../friend-card-units/base-page";
import { useMount, useSetState } from "ahooks";
import Taro, { useRouter } from "@tarojs/taro";
import { friendInfo } from "../friend-card-units/api-friend";
import { img_avatar } from "@/utils/images";

export default function Page() {
  const router = useRouter();
  const tid = router.params.id;
  const [state, setState] = useSetState({
    info: null as any,
  });

  useMount(async () => {
    const info = await friendInfo({ id: tid });
    setState({ info });

    if (info.state == 20) {
      Taro.navigateTo({
        url: `/pages/mall3/friend-card-success/index?id=${tid}`,
      });

      return;
    }
  });

  const onJoin = async () => {
    Taro.navigateTo({
      url: `/pages/mall3/friend-card-join-confirm/index?id=${tid}`,
    });
  };

  return (
    <FriendCardBasePage>
      <Image
        className="block w-full h-1100"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250502/1367864928572469248_1500_2198.png"
      />

      <View className="mt-100">
        <View className="flex items-center justify-center">
          <Image
            className="size-60 bg-#eee rounded-full overflow-hidden"
            src={state.info?.publisher?.photo || img_avatar}
          />
          <View className="px-10 text-(24 #414141)">
            {state.info?.publisher?.name}
          </View>
          <View className="text-(26 #181611) fw-bold">邀请您组队</View>
        </View>

        <View
          className="w-600 h-88 mt-20 mx-a bg-#25262A rounded-full flex items-center justify-center"
          onClick={onJoin}
        >
          <Text className="text-42 text-#F4D0D8">立即加入组队</Text>
        </View>
        <View className="flex justify-center items-center mt-30">
          <CountDown
            endTime={state.info?.endDate} // 示例：24小时后结束
            className="flex items-center"
            renderDigit={(value) => (
              <View className="bg-#898989 rounded-4 min-w-38 px-6 h-34 flex items-center justify-center">
                <Text className="text-(#fff 26/34 center)">{value}</Text>
              </View>
            )}
            renderSeparator={(separator) => (
              <View className="px-5">
                <Text className="text-(26 #111)">{separator}</Text>
              </View>
            )}
          />
          <View className="text-(28 #25262A) ml-10">后组队失效</View>
        </View>
      </View>
    </FriendCardBasePage>
  );
}
