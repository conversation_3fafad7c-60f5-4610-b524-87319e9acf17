import CountDown from "@/components/CountDown";
import { Image, Text, View } from "@tarojs/components";
import { FriendCardBasePage } from "../friend-card-units/base-page";
import Taro, { useRouter } from "@tarojs/taro";
import { friendInfo, friendJoin } from "../friend-card-units/api-friend";
import { useMount, useSetState } from "ahooks";
import { img_avatar } from "@/utils/images";
import { getUserByToken } from "@/services/UserApi";
import { AvatarNickModal } from "@/components/AvatarNickModal";
import { UnoIcon } from "@/components/UnoIcon";

export default function Page() {
  const router = useRouter();
  const tid = router.params.id;
  const [state, setState] = useSetState({
    info: null as any,
    user: null as any,

    open: false,
  });

  useMount(async () => {
    const info = await friendInfo({ id: tid });
    setState({ info });
  });

  useMount(async () => {
    const user = await getUserByToken();
    setState({ user });
  });

  const onJoin = async () => {
    try {
      await Taro.requestSubscribeMessage({
        tmplIds: ["EX_Zhy3skBqOXjwFzCDtMadt9endFKQesJxco37kkxs"], // 活动结果（同行）
      } as any);
    } catch (err) {}

    const res = await friendJoin({ togetherId: tid });
    Taro.navigateTo({
      url: `/pages/mall3/friend-card-success/index?id=${res?.id}`,
    });
  };

  return (
    <FriendCardBasePage>
      <Image
        className="w-742 h-389 block mx-a"
        mode="aspectFit"
        src="https://oss.gaomei168.com/file-release/20250503/1368209715334934528_1484_778.png"
      />

      <View className="flex justify-center pt-150 pb-0">
        <Image
          className="size-140 rounded-full"
          mode="aspectFill"
          src={state.user?.photo || img_avatar}
          onClick={() => setState({ open: true })}
        />
      </View>
      <View className="">
        <View
          className="w-670 h-94 b-(1 solid #C9ACAC) rounded-16 flex items-center box-border mx-a bg-#fff mt-60"
          onClick={() => setState({ open: true })}
        >
          <Image
            className="size-48 ml-60 mr-30"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250503/1368209714751926272_288_306.png"
          />
          <View className="text-(32 #121619) flex-1">{state.user?.name}</View>
          <UnoIcon className="i-icon-park-outline:edit text-(32 #999) mr-20" />
        </View>
        <View className="w-670 h-94 b-(1 solid #C9ACAC) rounded-16 flex items-center box-border mx-a bg-#fff mt-60">
          <Image
            className="size-48 ml-60 mr-30"
            mode="aspectFit"
            src="https://oss.gaomei168.com/file-release/20250503/1368209714756120576_328_324.png"
          />
          <View className="text-(32 #121619)">{state.user?.mobile}</View>
        </View>
      </View>

      <View className="mt-100">
        <View className="flex items-center justify-center">
          <Image
            className="size-60 bg-#eee rounded-full overflow-hidden"
            src={state.info?.publisher?.photo || img_avatar}
          />
          <View className="px-10 text-(24 #414141)">
            {state.info?.publisher?.name}
          </View>
          <View className="text-(26 #181611) fw-bold">邀请您组队</View>
        </View>

        <View
          className="w-600 h-88 mt-20 mx-a bg-#25262A rounded-full flex items-center justify-center"
          onClick={onJoin}
        >
          <Text className="text-42 text-#F4D0D8">确认</Text>
        </View>
        <View className="flex justify-center items-center mt-30">
          <CountDown
            endTime={state.info?.endDate} // 示例：24小时后结束
            onFinish={() => console.log("倒计时结束")}
            className="flex items-center"
            renderDigit={(value) => (
              <View className="bg-#898989 rounded-4 min-w-38 px-6 h-34 flex items-center justify-center">
                <Text className="text-(#fff 26/34 center)">{value}</Text>
              </View>
            )}
            renderSeparator={(separator) => (
              <View className="px-5">
                <Text className="text-(26 #111)">{separator}</Text>
              </View>
            )}
          />
          <View className="text-(28 #25262A) ml-10">后组队失效</View>
        </View>
      </View>

      <AvatarNickModal
        tag="friend-card"
        open={state.open}
        onClose={() => setState({ open: false })}
        onChange={(user) => {
          setState({ user });
        }}
      />
    </FriendCardBasePage>
  );
}
