export default defineAppConfig({
  pages: [
    "pages/tabs/mall/index",
    "pages/tabs/cate/index",
    "pages/tabs/user/index",
    "pages/tabs/note/index",

    "pages/mall2/cart/index",
    "pages/mall2/shop/index",

    "pages/mall2/info/index",
    "pages/mall2/shopInfo/index",
    "pages/mall2/doctorInfo/index",
    // "pages/mall2/pickDoctor/index",
    "pages/mall2/order/index",
    "pages/mall2/orderList/index",
    "pages/mall2/orderInfo/index",
    "pages/mall2/refund/index",
    "pages/mall2/orderComment/index",
    "pages/mall2/orderCommentList/index",
    "pages/mall2/due/index",
    "pages/mall2/dueList/index",
    "pages/mall2/dueRes/index",
    "pages/mall2/projectList/index",

    // 搜索
    "pages/mall2/search/index",
    "pages/mall2/goodsList/index",

    // 集卡
    "pages/mall2/cardCollect/index",
    "pages/mall2/cardCollect2/index",

    // 签到 推荐 积分商城
    "pages/mall2/sign/index",
    "pages/mall2/referrer/index",
    "pages/mall2/mmall/index",
    "pages/mall2/minfo/index",

    // 好医生
    "pages/mall2/goodDoctor/index",
    "pages/mall2/goodDoctor2/index",

    // 最新活动
    // "pages/mall2/noteroot/index",
    "pages/mall2/noteroot40/index",
    "pages/mall2/noteroot50/index",
    "pages/mall2/noteinfo/index",
    "pages/mall2/noteinfo2/index",
    // "pages/mall2/noteinfo30/index",
    "pages/mall2/noteinfo40/index",

    // 会员
    // "pages/mall2/vipinfo/index",

    // 意见邮箱
    "pages/mall2/mailbox/index",
    "pages/mall2/mailboxInfo/index",
    "pages/mall2/mailboxList/index",

    // 团购
    "pages/mall2/group/index",
    "pages/mall2/groupLog/index",
    "pages/mall2/groupInfo/index",
    "pages/mall2/groupPay/index",

    // 秒杀
    "pages/mall2/marketRootFlash/index",
    "pages/mall2/marketInfo/index",
    "pages/mall2/marketPay/index",

    "pages/mall2/brand/index",
  ],
  subPackages: [
    {
      root: "pages/mall3",
      pages: [
        // 朋友卡
        "friend-card-home/index",
        "friend-card-create/index",
        "friend-card-join/index",
        "friend-card-join-confirm/index",
        "friend-card-success/index",
        "friend-card-expired/index",
        "friend-card-share-root/index",

        // 案例招募
        "case1/index",

        // 门店地图
        "shop-map/index",
      ],
    },
    {
      root: "pages/mall",
      pages: [
        "login/index",
        "webview/index",
        "coupon/index",
        "couponRes/index",
        "agreement/index",
        "gmb/index",
        "gmbDraw/index",
        "wallet/index",
        "rich/index",

        "dueList2/index",
        "due2/index",

        "adminShopPick/index",
        "verifyOrder/index",
        "verifyOrderInfo/index",

        // 分销
        "fxhome/index",
        "fxrank/index",
        "fxlist/index",
        "fxposter/index",
        "fxlog/index",
        "fxdraw/index",
        "fxuser/index",
        "fxorder/index",
        "fxpay/index",
        "fxinfo/index",
        "fxinfo2/index",
      ],
    },

    {
      root: "pages/user",
      pages: [
        "profile/index",
        "orderlog1/index",
        "orderlog2/index",
        "orderLogComment/index",
        "coupon/index",

        "withdraw/index",
        "withdrawBank/index",

        "InvestorShop/index",
        "InvestorUser/index",
        "InvestorIncome/index",

        "presenter/index",

        "invite/index",
        "invite2/index",
        "butler/index",
        "newUser/index",
        "vip/index",
        "lv3/index",
        "user/index",
        "user2/index",
        "userRelation/index",
      ],
    },
  ],
  window: {
    backgroundTextStyle: "dark",
    navigationBarBackgroundColor: "#fff",
    navigationBarTitleText: "搞美医疗数字诊所",
    navigationBarTextStyle: "black",
  },
  tabBar: {
    custom: false,
    color: "#aaa",
    selectedColor: "#8E4E36",
    list: [
      {
        text: "首页",
        pagePath: "pages/tabs/mall/index",
        iconPath: "./assets/v3/B-1.png",
        selectedIconPath: "./assets/v3/A-1.png",
      },
      {
        text: "商城",
        pagePath: "pages/tabs/cate/index",
        iconPath: "./assets/v3/B-2.png",
        selectedIconPath: "./assets/v3/A-2.png",
      },
      {
        text: "搞美星球",
        pagePath: "pages/tabs/note/index",
        iconPath: "./assets/v3/B-3.png",
        selectedIconPath: "./assets/v3/A-3.png",
      },
      {
        text: "我的",
        pagePath: "pages/tabs/user/index",
        iconPath: "./assets/v3/B-4.png",
        selectedIconPath: "./assets/v3/A-4.png",
      },
    ],
  },
  requiredPrivateInfos: ["getLocation", "chooseAddress"],
  permission: {
    "scope.userLocation": {
      desc: "用于获取附近门店信息",
    },
  },
});
