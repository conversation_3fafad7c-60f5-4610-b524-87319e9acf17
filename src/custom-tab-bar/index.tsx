import { Image, View } from "@tarojs/components";
import Style from "./index.module.scss";
import clsx from "clsx";
import Taro from "@tarojs/taro";
import { useSetState } from "ahooks";
import { useEffect } from "react";

const tabs = [
  {
    key: "home",
    name: "首页",
    ico1: require("@/assets/v2/tab-home-1.png"),
    ico2: require("@/assets/v2/tab-home-2.png"),
    link: "/pages/tabs/mall/index",
  },
  {
    key: "cate",
    name: "商城",
    ico1: require("@/assets/v2/tab-mall-1.png"),
    ico2: require("@/assets/v2/tab-mall-2.png"),
    link: "/pages/tabs/cate/index",
  },
  {
    key: "mid",
    name: "咨询预约",
    ico1: require("@/assets/v2/tab-msg-1.png"),
    ico2: require("@/assets/v2/tab-msg-2.png"),
    link: "",
  },
  {
    key: "note",
    name: "搞美星球",
    ico1: require("@/assets/v2/tab-shop-1.png"),
    ico2: require("@/assets/v2/tab-shop-2.png"),
    link: "/pages/tabs/note/index",
  },
  {
    key: "user",
    name: "我的",
    ico1: require("@/assets/v2/tab-user-1.png"),
    ico2: require("@/assets/v2/tab-user-2.png"),
    link: "/pages/tabs/user/index",
  },
];

export default function CusTabbar() {
  const [state, setState] = useSetState({ active: "home" });

  useEffect(() => {
    const func = (key) => setState({ active: key });

    Taro.eventCenter.on("tab.switch", func);

    return () => {
      Taro.eventCenter.off("tab.switch", func);
    };
  }, []);

  return (
    <View className={Style.box}>
      <View className={Style.tabs}>
        {tabs.map((item) => {
          if (item.key == "mid") {
            return (
              <View
                className={clsx(Style.item, Style.mid)}
                key={item.key}
                onClick={() => {
                  Taro.navigateTo({ url: `/pages/mall2/due/index` });
                }}
              >
                <Image className={Style.ico} mode="aspectFit" src={item.ico1} />
                <View className={Style.txt}>{item.name}</View>
                {/* <Button openType="contact" className={Style.btn}></Button> */}
              </View>
            );
          }

          return (
            <View
              className={clsx(Style.item, {
                [Style.on]: state.active == item.key,
              })}
              key={item.key}
              onClick={() => {
                Taro.switchTab({ url: item.link });
              }}
            >
              <Image
                className={Style.ico}
                mode="aspectFit"
                src={state.active == item.key ? item.ico2 : item.ico1}
              />
              <View className={Style.txt}>{item.name}</View>
            </View>
          );
        })}
      </View>
      <View className={Style.round}></View>
      <View className={Style.safe}></View>
    </View>
  );
}
