.box {
  z-index: 999;
  position: relative;
  filter: drop-shadow(0 -5px 10px rgba(#000, 0.3));

  .round {
    z-index: 1;
    position: absolute;
    top: -34px;
    left: 50%;
    width: 180px;
    height: 170px;
    border-radius: 50%;
    background: #fff;
    transform: translate(-50%, 0);
  }
  .safe {
    height: env(safe-area-inset-bottom);
    background: #fff;
  }
}

.tabs {
  z-index: 2;
  position: relative;
  height: 164px;
  background: #fff;
  display: flex;
  filter: drop-shadow();
  border-radius: 40px 40px 0 0;
  box-sizing: border-box;

  .item {
    flex: 1;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: flex-end;
    padding-bottom: 20px;

    .ico {
      width: 60px;
      height: 60px;
    }
    .txt {
      margin-top: 5px;
      font-size: 32px;
      color: #1f1f1f;
    }

    &.mid {
      position: relative;
      padding-left: 40px;
      padding-right: 40px;

      .ico {
        width: 95px;
        height: 95px;
      }

      .txt {
        margin-top: 10px;
      }

      .btn {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
      }
    }

    &.on {
      .txt {
        color: #8c533d;
      }
    }
  }
}
