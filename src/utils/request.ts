import axios, { AxiosRequestConfig } from "axios";
import Taro from "@tarojs/taro";
// import Config from "./config";

const request = axios.create({
  baseURL: process.env.TARO_APP_API,
  timeout: 20 * 1000,
  headers: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
});

request.interceptors.request.use((req) => {
  const token = Taro.getStorageSync("token") || "";
  const loading = (req as any)._loading;

  loading &&
    Taro.showLoading({ title: loading === true ? "加载中..." : loading });

  req.params = { ...req.params, token };
  // req.headers.set('X-Tenant-ID', "")

  return req;
});

request.interceptors.response.use(async (res) => {
  const loading = (res.config as any)._loading;
  loading && Taro.hideLoading();

  if (res.data?.code === 0) {
    return res.data?.data;
  }

  if (res.data?.code === 100) {
    const user = Taro.getStorageSync("user");
    const noAuth = !!(res.config as any)?._noAuth;

    if (user) {
      const { token } = await createToken({
        userId: user.id,
        password: user.p,
      });

      Taro.setStorageSync("token", token);
      return request(res.config);
    } else if (noAuth) {
      throw res;
    } else {
      Taro.removeStorageSync("token");
      Taro.removeStorageSync("user");

      Taro.redirectTo({ url: `/pages/mall/login/index` });

      (res.config as any)._toast = true;
      throw res;
    }
  }

  throw res;
});

request.interceptors.response.use(null, (err) => {
  if (err?.code === "ERR_CANCELED") throw err;
  const _toast = err.config?._toast ?? true;

  const msg = err?.data?.message || err?.message || "发生错误，请重试";
  _toast && Taro.showToast({ title: msg, icon: "none", duration: 2000 });
  throw err;
});

export const makeApi =
  (method: string, url: string) =>
  (
    data: any = {},
    configs?: AxiosRequestConfig & {
      _loading?: string | boolean;
      _toast?: boolean;
      _noAuth?: boolean;
    }
  ): Promise<any> => {
    const options = { method, url, ...configs };

    if (method.toLowerCase() === "get") {
      options.params = { ...data, ...options.params };
    } else {
      options.data = { ...data, ...options.data };
    }

    return request(options);
  };

const createToken = makeApi("get", `/api/token/create_token`);

export const uploadFile = async (path) => {
  const res = await Taro.uploadFile({
    url: process.env.TARO_APP_UPLOAD || "",
    filePath: path,
    name: "file",
    header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
  });
  const data = JSON.parse(res.data);
  return data;
};

export default request;
