import CusApi from "@/services/CusApi";
import { showToast } from "@/utils/tools";
import Taro from "@tarojs/taro";

export const StateMap = [
  { id: 0, name: "待付款" },
  { id: 10, name: "已付款" },
  { id: 20, name: "已接单" },
  { id: 30, name: "待收货" },
  { id: 40, name: "已完成" },
  { id: 70, name: "退款确认中" },
  { id: 80, name: "已退款" },
  { id: 99, name: "交易关闭" },
];

export const OrderItem = [
  {
    id: 0,
    name: "全部",
    icon: require("@/assets/order-0.png"),
    params: {},
  },
  {
    id: 1,
    name: "待付款",
    icon: require("@/assets/order-1.png"),
    params: { orderState: 0 },
  },
  {
    id: 2,
    name: "可使用",
    icon: require("@/assets/order-2.png"),
    params: { orderState: "10,20,30" },
  },
  {
    id: 3,
    name: "退款/售后",
    icon: require("@/assets/order-3.png"),
    params: { orderState: "70" },
  },
  // { id: 3, name: "已完成", icon: require("@/assets/order-4.png") },
];

export const patchOrder = (data: any) => {
  return { ...data, ...data?.operate };
};

export const handlePay = async (data: any, cb: Function) => {
  const modal = await Taro.showModal({
    title: "确认支付？",
    content: `需要支付 ${data.payMoney} 元`,
  });

  if (!modal.confirm) return;

  Taro.showLoading({ title: "加载中" });
  // 0元购
  if (!data.payMoney) {
    await CusApi.payOrderZero({ orderId: data.id });
    Taro.hideLoading();
    await showToast("支付成功");
    cb();
  }

  // 支付
  else {
    const res = await CusApi.preOrder({
      wxAppId: process.env.TARO_APP_ID,
      orderId: data.id,
    });
    Taro.hideLoading();
    Taro.requestPayment({
      timeStamp: res.timeStamp,
      nonceStr: res.nonceStr,
      package: res.packgeStr,
      signType: res.signType,
      paySign: res.paySign,
      success: async () => {
        showToast("支付成功");
        cb();
      },
      fail: async () => {
        showToast("支付失败");
        cb();
      },
    });
  }
};

export const handleClose = async (data: any, cb: Function) => {
  const modal = await Taro.showModal({ title: "确定要取消这个订单吗?" });
  if (!modal.confirm) return;

  Taro.showLoading({ title: "加载中" });
  await CusApi.putOrderClose({ orderId: data.id });
  showToast("订单已取消");
  cb();
};

export const handleRefund = async (data: any) => {
  Taro.navigateTo({ url: `/pages/mall2/refund/index?id=${data.id}` });
};

export const handleRefundCancel = async (data: any, cb: Function) => {
  const modal = await Taro.showModal({ title: "确定放弃退款?" });
  if (!modal.confirm) return;
  Taro.showLoading({ title: "加载中" });
  await CusApi.putOrderRefundCancel({ orderId: data.id });
  showToast("操作成功");
  cb();
};

export const handleConfirm = async (data: any, cb: Function) => {
  const modal = await Taro.showModal({
    title: "确定已收货?",
    content: "操作后不可撤销",
  });

  if (!modal.confirm) return;
  Taro.showLoading({ title: "加载中" });
  await CusApi.putOrderConfirm({ orderId: data.id });
  showToast("操作成功");
  cb();
};

export const handleComment = async (data: any) => {
  Taro.navigateTo({ url: `/pages/mall2/orderComment/index?id=${data.id}` });
};
