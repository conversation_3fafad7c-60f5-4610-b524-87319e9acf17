import { useSetState } from "ahooks";
import axios from "axios";
import { useRef } from "react";

const CancelToken = axios.CancelToken;

const useFetchList = (props: {
  api: (...args: any[]) => Promise<any>;
  params?: any;
}) => {
  const ctr = useRef<any>(null);
  const [state, setState] = useSetState({
    list: [] as any[],
    loading: false,
    more: true,
    page: 1,
    size: 10,
  });

  const getList = async (page = 1) => {
    if (page !== 1 && (!state.more || state.loading)) return;

    if (page == 1) ctr.current?.cancel();

    ctr.current = CancelToken.source();
    setState({ loading: true });
    const params = { pageNum: page, pageSize: state.size, ...props.params };
    const res = await props
      .api(params, { cancelToken: ctr.current?.token })
      .finally(() => setState({ loading: false }));

    setState((prev) => {
      const plist = page == 1 ? [] : [...prev.list];
      const clist = res?.list || [];
      return {
        list: [...plist, ...clist],
        page,
        more: !!res?.hasNextPage,
      };
    });
  };

  const getNext = () => getList(state.page + 1);

  return [
    state,
    {
      getList,
      getNext,
    },
  ] as const;
};

export default useFetchList;
