import CusApi from "@/services/CusApi";
import Taro from "@tarojs/taro";
import dayjs from "dayjs";

export const sleep = (ms = 0) => {
  return new Promise((res) => setTimeout(res, ms));
};

export const showToast = async (msg: string, ms = 2000) => {
  Taro.showToast({ title: msg, duration: ms, icon: "none" });
  await sleep(ms);
};

export const formatDis = (n = 0) => {
  let s: any = n || 0;
  if (s < 1000) {
    s = s + "m";
  } else if (s > 1000 * 100) {
    s = ">100km";
  } else {
    s = (s / 1000).toFixed(1) + "km";
  }
  return s;
};

export const fetchShopsByLocation = async (p?: any) => {
  const pos = Taro.getStorageSync("pos");
  const res = await CusApi.getShops({
    longitude: pos?.longitude,
    latitude: pos?.latitude,
    ...p,
  });

  let list: any = res?.list || [];
  list = list.map((item) => ({ ...item, _dis: formatDis(item.distance) }));
  return list;
};

export const privatePhone = (n: string) => {
  if (!n) return "";
  return n.replace(/(\d{3})(\d{4})/, "$1**");
};

export const str2arr = (n = "") => {
  return (n || "").split(",").filter((n) => n);
};

export const openConcat = () => {
  const url = `https://work.weixin.qq.com/kfid/kfcc1793a8a8dbee924`;
  Taro.openCustomerServiceChat({
    extInfo: { url },
    corpId: "ww6ff0ba55cb7300f4",
  });
};

export const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => {
  return n ? dayjs(n).format(f) : "";
};

export const jumpAfterAdminShopChose = (url: string) => {
  const token = Taro.getStorageSync("token");
  if (!token) {
    return Taro.navigateTo({ url: `/pages/mall/login/index` });
  }
  Taro.navigateTo({
    url: `/pages/mall/adminShopPick/index?url=${encodeURIComponent(url)}`,
  });
};
