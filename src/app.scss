page {
  background: #f5f5f5;
  line-height: 1.3;
  color: #000;
  font-family: OPPOSans;

  // --nutui-color-primary: #d0ab6b;
  --nutui-brand-color: #d0ab6b;
  --nutui-brand-color-end: #d0ab6b;
  --nutui-popup-border-radius: 30px;
  --nutui-tabs-titles-background-color: #fff;

  --color-primary: #e8d9c5;
}

.nut-noticebar-page {
  background: rgba(#d0ab6b, 0.2) !important;
  color: #d0ab6b !important;
}

.anim-txt {
  animation: anim-txt 5s linear 0s infinite;
}

@keyframes anim-txt {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
