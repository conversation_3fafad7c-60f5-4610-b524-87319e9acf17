import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { decodeHTML } from "entities";
import "./index.scss";

(Taro as any).options.html.transformElement = (el) => {
  if (el.nodeName === "image") {
    el.setAttribute("mode", "widthFix");
  }
  return el;
};

(Taro as any).options.html.transformText = (txt, str) => {
  const tmp = str.content.replace(/\n/g, "");
  txt.textContent = decodeHTML(tmp);
  return txt;
};

export default function (props: { html: string }) {
  return (
    <View
      className="taro_html"
      dangerouslySetInnerHTML={{ __html: props.html }}
    ></View>
  );
}
