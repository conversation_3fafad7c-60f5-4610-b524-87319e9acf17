import { img_close } from "@/utils/images";
import { Image, View } from "@tarojs/components";
import clsx from "clsx";

export const CusPopup = (props: {
  open?: boolean;
  onClose?: any;
  children?: any;
  title?: any;
}) => {
  return (
    <>
      <View
        className={clsx(
          "pos-relative z-200 [&.hide_.pmask]:(invisible opacity-0) [&.hide_.pcont]:(invisible opacity-0 translate-y-1/1)",
          { hide: !props.open }
        )}
      >
        <View
          className="pmask z-90 pos-fixed top-0 left-0 size-full bg-#000/60 transition-all transition-duration-300"
          onClick={() => props.onClose?.()}
        ></View>
        <View className="pcont z-90 pos-fixed left-0 bottom-0 w-full transition-all transition-duration-300">
          <View
            className="bg-#fff"
            style={{
              borderRadius: "40rpx 40rpx",
              backgroundImage:
                "linear-gradient(0deg, rgba(226,199,171,0.7), rgba(255,255,255,0.7))",
            }}
          >
            <View className="flex items-center py-35 px-60">
              <View className="flex-1 text-(37 #11100F)">{props.title}</View>
              <Image
                className="size-36"
                onClick={() => props.onClose?.()}
                src={img_close}
                mode="aspectFit"
              />
            </View>
            {props.children}
          </View>
        </View>
      </View>
    </>
  );
};
