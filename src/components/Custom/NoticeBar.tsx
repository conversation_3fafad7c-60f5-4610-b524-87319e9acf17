import { UnoIcon } from "@/components/UnoIcon";
import { Text, View } from "@tarojs/components";
import { NoticeBar } from "@taroify/core";

export const CusNoticeBar = (props: { children?: any }) => {
  return (
    <NoticeBar scrollable delay={2000} speed={40} className="!bg-transparent">
      <NoticeBar.Icon>
        <View>
          <UnoIcon className="i-icon-park-outline:volume-notice block size-30 text-#AC7257 mr-10" />
        </View>
      </NoticeBar.Icon>
      <Text className="text-(22 #848484)">{props.children}</Text>
    </NoticeBar>
  );
};
