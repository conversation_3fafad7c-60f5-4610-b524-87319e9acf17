import CusApi from "@/services/CusApi";
import {
  Image,
  Swiper,
  SwiperItem,
  SwiperProps,
  View,
} from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";

export const CusSwiper = (props: {
  pos: number;
  className?: string;
  swiperProps?: SwiperProps;
  hideOnEmpty?: boolean;
}) => {
  const [state, setState] = useSetState({
    list: [] as any[],
    idx: 0,
    empty: false,
  });

  useMount(() => {
    fetchList();
  });

  const fetchList = async () => {
    const res = await CusApi.getAdv({ showPosition: props.pos });
    const list = res?.list || [];
    setState({ list, empty: !!list?.length });
  };

  const onClick = (item) => {
    const val: string = item.jumpKey || "";
    const val2 = encodeURIComponent(val);
    if (item.jumpType == 1) {
      if (val.startsWith("/pages/")) {
        Taro.navigateTo({ url: val });
      } else {
        Taro.navigateTo({ url: `/pages/mall/webview/index?url=${val2}` });
      }
    } else if (item.jumpType == 3) {
      Taro.navigateTo({ url: `/pages/mall/rich/index?html=${val2}` });
    } else if (item.jumpType == 4) {
      Taro.navigateTo({ url: `/pages/mall2/info/index?id=${val}` });
    }
  };

  if (props.hideOnEmpty && !state.empty) return null;

  return (
    <View
      className={clsx("pos-relative size-full bg-#fafafa", props.className)}
    >
      <Swiper
        className="size-full"
        autoplay
        circular
        interval={3000}
        current={state.idx}
        onChange={(e) => setState({ idx: e.detail.current })}
        {...props.swiperProps}
      >
        {state.list.map((item) => (
          <SwiperItem className="size-full" key={item.id}>
            <Image
              className="size-full"
              mode="aspectFill"
              src={item.image}
              onClick={() => onClick(item)}
            />
          </SwiperItem>
        ))}
      </Swiper>

      {state.list.length > 1 && (
        <View className="dots pos-absolute left-0 bottom-15 w-full flex items-center justify-center gap-6">
          {state.list.map((item, idx) => (
            <View
              key={item.id}
              className={clsx(
                "size-10 bg-#000/30 rounded-full",
                state.idx == idx && "uno-layer-z1-(bg-#AE7F6D)"
              )}
            />
          ))}
        </View>
      )}
    </View>
  );
};
