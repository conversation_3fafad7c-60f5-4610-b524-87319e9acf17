import { View } from "@tarojs/components";
import clsx from "clsx";

export const PopUp = (props: {
  open?: boolean;
  onClose?: () => any;
  children?: any;
  className?: string;
}) => {
  return (
    <View className={clsx("pos-relative z-200", props.className)} catchMove>
      <View
        className={clsx(
          "pos-fixed top-0 left-0 size-full bg-#000/60 invisible opacity-0 transition-all transition-duration-300",
          props.open && "uno-layer-z1-(visible opacity-100)"
        )}
        onClick={() => props.onClose?.()}
      />
      <View
        className={clsx(
          "pos-fixed left-0 bottom-0 w-full invisible opacity-0 translate-y-1/1 transition-all transition-duration-300",
          props.open && "uno-layer-z1-(visible opacity-100 translate-y-0)"
        )}
      >
        {props.children}
      </View>
    </View>
  );
};

export const PopUpTop = (props: {
  open?: boolean;
  onClose?: () => any;
  children?: any;
  className?: string;
}) => {
  return (
    <View className={clsx("pos-relative z-200", props.className)} catchMove>
      <View
        className={clsx(
          "pos-fixed top-0 left-0 size-full bg-#000/60 invisible opacity-0 transition-all transition-duration-300",
          props.open && "uno-layer-z1-(visible opacity-100)"
        )}
        onClick={() => props.onClose?.()}
      />
      <View
        className={clsx(
          "pos-fixed left-0 top-0 w-full invisible opacity-0 -translate-y-1/1 transition-all transition-duration-300",
          props.open && "uno-layer-z1-(visible opacity-100 translate-y-0)"
        )}
      >
        {props.children}
      </View>
    </View>
  );
};

export const Modal = (props: {
  open?: boolean;
  onClose?: () => any;
  children?: any;
  className?: string;
}) => {
  return (
    <View className={clsx("pos-relative z-200", props.className)} catchMove>
      <View
        className={clsx(
          "pos-fixed top-0 left-0 size-full bg-#000/60 invisible opacity-0 transition-all transition-duration-300",
          props.open && "uno-layer-z1-(visible opacity-100)"
        )}
        onClick={() => props.onClose?.()}
      />
      <View
        className={clsx(
          "pos-fixed top-1/2 left-1/2 invisible opacity-0 -translate-1/2 scale-80 transition-all transition-duration-300",
          props.open && "uno-layer-z1-(visible opacity-100 scale-100)"
        )}
      >
        {props.children}
      </View>
    </View>
  );
};
