import { Uploader } from "@taroify/core";
import Taro from "@tarojs/taro";
import { useSetState } from "ahooks";
import { useEffect } from "react";

export const CusUpload = (props: { max?: number; onChange?: any }) => {
  const [state, setState] = useSetState({
    list: [] as any[],
  });

  const max = props.max ?? 1;

  useEffect(() => {
    const val = state.list
      .filter((n) => n.status === "success")
      .map((n) => n.url)
      .join(",");
    props.onChange?.(val);
  }, [state.list]);

  return (
    <Uploader
      maxFiles={max}
      multiple
      value={state.list}
      onChange={(e) => setState({ list: e })}
      onUpload={() => {
        Taro.chooseMedia({
          count: max - state.list.length,
          mediaType: ["image"],
          sizeType: ["compressed"],
          success: async (res) => {
            const list = res.tempFiles.map((n, idx) => ({
              url: n.tempFilePath,
              status: "uploading",
              key: Date.now() + "-" + idx,
            }));

            setState({ list: [...state.list, ...list] });

            for (let item of list) {
              Taro.uploadFile({
                url: process.env.TARO_APP_UPLOAD || "",
                filePath: item.url,
                name: "file",
                header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
                success: (res2) => {
                  const url = JSON.parse(res2.data)?.data?.path || "";
                  if (!url) return;
                  setState((p) => ({
                    list: p.list.map((n) =>
                      n.key == item.key ? { ...n, url, status: "success" } : n
                    ),
                  }));
                },
              });
            }
          },
        });
      }}
    />
  );
};
