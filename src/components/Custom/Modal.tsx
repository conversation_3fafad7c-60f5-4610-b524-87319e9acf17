import { View } from "@tarojs/components";
import clsx from "clsx";

export const CusModal = (props: any) => {
  return (
    <View
      className={clsx(
        "[&.hide_.pmask]:(invisible opacity-0) [&.hide_.pcont]:(invisible opacity-0 scale-0)",
        { hide: !props.open }
      )}
    >
      <View
        className="pmask z-90 pos-fixed top-0 left-0 size-full bg-#000/60 transition-all transition-duration-400"
        onClick={() => props.onClose?.()}
      ></View>
      <View className="z-90 pos-fixed top-0 left-0 size-full flex items-center justify-center pointer-events-none">
        <View className="pcont pointer-events-auto transition-all transition-duration-400">
          {props.children}
        </View>
      </View>
    </View>
  );
};
