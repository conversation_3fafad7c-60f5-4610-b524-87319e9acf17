import { ScrollView, View } from "@tarojs/components";
import clsx from "clsx";
import { useId } from "react";

export const Tabs = (props: {
  scroll?: boolean;
  tabs?: any[];
  value?: any;
  onChange?: any;
}) => {
  const uid = "tab-" + useId();

  if (!props.scroll) {
    return (
      <View className="flex">
        {props.tabs?.map((item) => {
          const active = item.id == props.value;

          return (
            <View
              key={item.id}
              className={clsx(
                "pos-relative h-80 text-(28 #666) px-20",
                "flex-1 min-w-0 flex items-center justify-center",
                active && "uno-layer-z1-(text-#8E4E36)"
              )}
              onClick={() => props.onChange?.(item.id)}
            >
              <View
                className="pos-absolute -left-200 top-0"
                id={uid + item.id}
              ></View>
              <View className="line-clamp-1">{item.name}</View>
              <View
                className={clsx(
                  "invisible opacity-0 scale-x-0 pos-absolute bottom-12 left-1/2 -translate-x-1/2 w-30 h-4 bg-#8E4E36 transition-all duration-300",
                  active && "uno-layer-z1-(visible opacity-100 scale-x-100)"
                )}
              ></View>
            </View>
          );
        })}
      </View>
    );
  }

  return (
    <View className="overflow-hidden">
      <View className="-mb-30">
        <ScrollView
          scrollX
          scrollIntoView={uid + props.value}
          scrollWithAnimation
        >
          <View className="ws-nowrap fz-0">
            {props.tabs?.map((item) => {
              const active = item.id == props.value;

              return (
                <View
                  key={item.id}
                  className={clsx(
                    "pos-relative h-80 text-(28 #666) px-20",
                    "ws-normal inline-flex items-center justify-center max-w-200",
                    active && "uno-layer-z1-(text-#8E4E36)"
                  )}
                  onClick={() => props.onChange?.(item.id)}
                >
                  <View
                    className="pos-absolute -left-200 top-0"
                    id={uid + item.id}
                  ></View>
                  <View className="line-clamp-1">{item.name}</View>
                  <View
                    className={clsx(
                      "invisible opacity-0 scale-x-0 pos-absolute bottom-12 left-1/2 -translate-x-1/2 w-30 h-4 bg-#8E4E36 transition-all duration-300",
                      active && "uno-layer-z1-(visible opacity-100 scale-x-100)"
                    )}
                  ></View>
                </View>
              );
            })}
          </View>
          <View className="h-30"></View>
        </ScrollView>
      </View>
    </View>
  );
};
