import CusApi from "@/services/CusApi";
import { img_avatar } from "@/utils/images";
import { str2arr } from "@/utils/tools";
import { Image, ScrollView, View } from "@tarojs/components";
import { useSetState } from "ahooks";
import { UnoIcon } from "../UnoIcon";
import Taro, { useReachBottom } from "@tarojs/taro";
import { useEffect } from "react";

export const MasonryList = (props: { type?: any; cate?: any }) => {
  const [state, setState] = useSetState({
    list1: [] as any[],
    list2: [] as any[],
    h1: 0,
    h2: 0,

    page: 1,
    size: 20,
    more: true,
    total: 0,
    loading: false,
    reloading: false,
  });

  useEffect(() => {
    getList(1);
  }, [props.cate?.id]);

  useReachBottom(() => {});

  const getRadio = (url = "") => {
    const arr = url.split(/_|\./);
    const w = Number(arr[arr.length - 3]);
    const h = Number(arr[arr.length - 2]);
    const r = h / w || 482 / 362;
    return r;
  };

  const getList = async (page) => {
    if (page !== 1 && (state.loading || !state.more)) return;
    if (page == 1) {
      setState({ list1: [], list2: [], more: true, total: 0 });
    }

    setState({ loading: true });

    try {
      const recommendTag = props.cate?.id == "recommendTag" ? 1 : "";
      const res = await CusApi.getNote(
        {
          pageNum: page,
          pageSize: state.size,
          categoryId: recommendTag ? "" : props.cate?.id,
          recommendTag: recommendTag,
          type: props.type,
        }
        // { baseURL: `https://vip.gaomei168.com/shaping-api-release` }
      );

      let total = res?.total || 0;
      let more = res?.hasNextPage;
      let list = res?.list || [];
      list = list.map((item) => {
        let _imgs = str2arr(item.images);
        let _cover = _imgs?.[0] || item.videoCoverImage;
        let _radio = getRadio(_cover);
        _cover += "?x-oss-process=image/resize,w_750,limit_0";
        return { ...item, _imgs, _cover, _radio };
      });

      let list1 = page == 1 ? [] : [...state.list1];
      let list2 = page == 1 ? [] : [...state.list2];
      let h1 = page == 1 ? 0 : state.h1;
      let h2 = page == 1 ? 0 : state.h2;
      list.forEach((item) => {
        if (h1 <= h2) {
          list1.push(item);
          h1 += item._radio;
        } else {
          list2.push(item);
          h2 += item._radio;
        }
      });

      setState({ list1, list2, h1, h2, total, more, page });
    } finally {
      setState({ loading: false });
    }
  };

  const reload = () => getList(1);
  const getNext = () => getList(state.page + 1);

  return (
    <ScrollView
      scrollY
      className="min-h-0 flex-1"
      onScrollToLower={getNext}
      refresherEnabled
      refresherTriggered={state.reloading}
      refresherBackground="#f5f5f5"
      onRefresherRefresh={async () => {
        setState({ reloading: true });
        await reload().finally(() => setState({ reloading: false }));
      }}
    >
      <View>
        <View className="grid cols-2 p-10 gap-10">
          {[state.list1, state.list2].map((list, idx) => (
            <View className="flex flex-col gap-10" key={idx}>
              {list.map((item) => (
                <View
                  className="pos-relative bg-#fff rounded-10 overflow-hidden"
                  onClick={() => {
                    if (/^https?:\/\//.test(item.jumpKey)) {
                      const link = encodeURIComponent(item.jumpKey);
                      Taro.navigateTo({
                        url: `/pages/mall/webview/index?url=${link}`,
                      });
                      return;
                    }

                    if (item.model == 2) {
                      Taro.navigateTo({
                        url: `/pages/mall2/noteinfo2/index?id=${item.id}`,
                      });
                    } else {
                      Taro.navigateTo({
                        url: `/pages/mall2/noteinfo/index?id=${item.id}`,
                      });
                    }
                  }}
                >
                  {item.model == 2 && (
                    <View className="z-10 pos-absolute top-20 right-20 size-38 rounded-full overflow-hidden bg-#000/30 flex items-center justify-center">
                      <Image
                        className="size-30"
                        src="https://oss.gaomei168.com/file-release/20250404/1357679409321377792_512_512.png"
                      />
                    </View>
                  )}
                  <View
                    className="pos-relative"
                    style={{ paddingTop: item._radio * 100 + "%" }}
                  >
                    <Image
                      className="pos-absolute top-0 left-0 size-full"
                      mode="aspectFit"
                      src={item._cover}
                    />
                  </View>
                  <View className="p-20">
                    <View className="h-72 text-(26/36 #282828) fw-bold line-clamp-2">
                      {item.title}
                    </View>
                    <View className="flex items-center mt-20">
                      <Image
                        className="size-40 rounded-full overflow-hidden"
                        mode="aspectFill"
                        src={item.appUser?.photo || img_avatar}
                      />
                      <View className="flex-1 min-w-0 mx-10 text-(20 #666)">
                        <View className="line-clamp-1">
                          {item.appUser?.name}
                        </View>
                      </View>

                      {item.likesTag ? (
                        <UnoIcon className="i-icon-park-solid:like size-32 c-#8E4E36" />
                      ) : (
                        <UnoIcon className="i-icon-park-outline:like size-32 text-#666" />
                      )}

                      <View className="text-(24 #666) ml-8">
                        {item.likesNum}
                      </View>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          ))}
        </View>
        <View className="pt-20 pb-40 text-(24 #999 center)">
          {state.more ? "加载中..." : "没有更多数据了"}
        </View>
      </View>
    </ScrollView>
  );
};
