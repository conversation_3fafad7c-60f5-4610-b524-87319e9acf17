import { img_avatar } from "@/utils/images";
// import { Popup } from "@taroify/core";
import { Button, Image, Input, View } from "@tarojs/components";
import { UnoIcon } from "../UnoIcon";
import { useMount, useSetState } from "ahooks";
import { getUserByToken, updateUser } from "@/services/UserApi";
import Taro from "@tarojs/taro";
import { PopUp } from "../Custom/PopUp2";

export const AvatarNickModal = (props: {
  open?: boolean;
  onClose?: any;
  onChange?: any;
  tag?: string;
}) => {
  const [state, setState] = useSetState({
    photo: "",
    name: "",
  });

  useMount(() => {
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await getUserByToken();
    setState({ photo: user.photo, name: user.name });
  };

  const updateAvatar = async (e) => {
    e.stopPropagation();
    const path = e.detail.avatarUrl;
    Taro.showLoading({ title: "上传中..." });
    const res = await Taro.uploadFile({
      url: process.env.TARO_APP_UPLOAD || "",
      filePath: path,
      name: "file",
      header: { "X-Tenant-ID": process.env.TARO_APP_TENANT_ID },
    });
    Taro.hideLoading();
    const data = JSON.parse(res.data);
    const url = data?.data?.allFullPath || "";
    setState({ photo: url });
  };

  const onsubmit = async () => {
    const data = { photo: state.photo, name: state.name, tag: props.tag || "" };
    const user = await updateUser(data, { _loading: true });
    props.onClose?.();
    props.onChange?.(user);
  };

  return (
    // <Popup
    //   open
    //   placement="bottom"
    //   className="[--popup-background-color:transparent]"
    // >
    <PopUp open={props.open} onClose={props.onClose}>
      <View
        className="w-full bg-#fff"
        style={{ borderRadius: "20rpx 20rpx 0 0" }}
      >
        <View className="px-30">
          <View className="py-40 text-(32 #111) fw-bold">
            设置您的头像和昵称
          </View>
          <View className="pos-relative flex items-center py-30 gap-40 b-b-(1 solid #eee) text-(26 #333)">
            <View>头像</View>
            <Image
              className="size-70 rounded-full bg-#eee"
              mode="aspectFill"
              src={state.photo || img_avatar}
            />
            <View className="flex-1 text-(26 #999)">请选择</View>
            <UnoIcon className="i-icon-park-outline:right text-(30 #666)" />
            <Button
              className="pos-absolute top-0 left-0 size-full opacity-0"
              openType="chooseAvatar"
              onChooseAvatar={updateAvatar}
            ></Button>
          </View>
          <View className="flex items-center py-30 gap-40 b-b-(1 solid #eee) text-(26 #333)">
            <View>昵称</View>
            <Input
              className="flex-1"
              type="nickname"
              placeholder="请输入昵称"
              value={state.name}
              onInput={(e) => setState({ name: e.detail.value })}
            />
          </View>
          <View className="flex justify-center items-center pt-50 pb-80 gap-40">
            <View
              className="w-200 h-70 text-(30/70 #666 center) rounded-10 bg-#eee"
              onClick={props.onClose}
            >
              关闭
            </View>
            <View
              className="w-200 h-70 text-(30/70 #8E4E36 center) rounded-10 bg-#eee"
              onClick={onsubmit}
            >
              提交
            </View>
          </View>
        </View>
      </View>
    </PopUp>
  );
};
