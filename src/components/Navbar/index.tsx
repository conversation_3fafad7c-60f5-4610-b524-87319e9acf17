import { View } from "@tarojs/components";
import Style from "./index.module.scss";
import { useMount, useSetState } from "ahooks";
import Taro from "@tarojs/taro";

const Navbar = (props: { children: any }) => {
  const [state, setState] = useSetState({
    rect: {
      width: 87,
      height: 32,
      left: 281,
      top: 48,
      right: 368,
      bottom: 80,
    },
  });

  useMount(() => {
    const rect = Taro.getMenuButtonBoundingClientRect();
    setState({ rect });
  });

  return (
    <View className={Style.nav}>
      <View className={Style.head} style={{ height: state.rect.top }}></View>
      <View className={Style.body} style={{ height: state.rect.height }}>
        {props.children}
      </View>
    </View>
  );
};

export default Navbar;
