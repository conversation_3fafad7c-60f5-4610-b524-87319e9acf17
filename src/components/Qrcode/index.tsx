import { Canvas, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useEffect, useId } from "react";
import drawQrcode from "weapp-qrcode";

export const QrCode = (props: { code: string }) => {
  const id = useId();

  useEffect(() => {
    Taro.createSelectorQuery()
      .select("#qr" + id)
      .boundingClientRect()
      .exec((res) => {
        const w = res?.[0]?.width || 0;
        const h = res?.[0]?.height || 0;

        drawQrcode({
          width: w,
          height: h,
          canvasId: "qr" + id,
          text: props.code,
          // foreground: "#333333",
        });
      });
  }, [props.code]);

  return (
    <View className="size-full">
      <Canvas
        id={"qr" + id}
        canvasId={"qr" + id}
        className="size-full"
      ></Canvas>
    </View>
  );
};
