import { View, Text } from "@tarojs/components";
import { useEffect, useState } from "react";

interface CountDownProps {
  endTime: string | number; // 结束时间，可以是时间戳或日期字符串
  onFinish?: () => void; // 倒计时结束回调
  className?: string; // 外层容器样式

  // 自定义样式选项 - 完全由外部定义
  digitClassName?: string; // 数字容器样式
  digitTextClassName?: string; // 数字文本样式
  separatorClassName?: string; // 分隔符容器样式
  separatorTextClassName?: string; // 分隔符文本样式

  // 自定义分隔符
  separator?: string;

  // 是否显示小时
  showHours?: boolean;

  // 自定义渲染函数
  renderDigit?: (value: string) => React.ReactNode;
  renderSeparator?: (separator: string) => React.ReactNode;
}

export default function CountDown({
  endTime,
  onFinish,
  className = "",
  digitClassName = "",
  digitTextClassName = "",
  separatorClassName = "",
  separatorTextClassName = "",
  separator = ":",
  showHours = true,
  renderDigit: customRenderDigit,
  renderSeparator: customRenderSeparator,
}: CountDownProps) {
  const [time, setTime] = useState({
    hours: "00",
    minutes: "00",
    seconds: "00",
  });

  useEffect(() => {
    // 计算剩余时间
    const calculateTimeLeft = () => {
      const endTimeMs =
        typeof endTime === "string" ? new Date(endTime).getTime() : endTime;
      const now = new Date().getTime();
      const difference = endTimeMs - now;

      if (difference <= 0) {
        // 倒计时结束
        setTime({
          hours: "00",
          minutes: "00",
          seconds: "00",
        });

        if (onFinish) {
          onFinish();
        }

        clearInterval(timer);
        return;
      }

      // 计算时、分、秒
      const hours = Math.floor(difference / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      // 格式化时间，保证两位数显示
      setTime({
        hours: hours < 10 ? `0${hours}` : `${hours}`,
        minutes: minutes < 10 ? `0${minutes}` : `${minutes}`,
        seconds: seconds < 10 ? `0${seconds}` : `${seconds}`,
      });
    };

    // 初始计算一次
    calculateTimeLeft();

    // 设置定时器，每秒更新一次
    const timer = setInterval(calculateTimeLeft, 1000);

    // 组件卸载时清除定时器
    return () => clearInterval(timer);
  }, [endTime, onFinish]);

  // 默认渲染数字
  const defaultRenderDigit = (value: string) => (
    <View className={digitClassName}>
      <Text className={digitTextClassName}>{value}</Text>
    </View>
  );

  // 默认渲染分隔符
  const defaultRenderSeparator = () => (
    <View className={separatorClassName}>
      <Text className={separatorTextClassName}>{separator}</Text>
    </View>
  );

  // 使用自定义渲染函数或默认渲染函数
  const renderDigit = customRenderDigit || defaultRenderDigit;
  const renderSeparator = customRenderSeparator
    ? () => customRenderSeparator(separator)
    : defaultRenderSeparator;

  return (
    <View className={className}>
      <View className="flex">
        {showHours && (
          <>
            {renderDigit(time.hours)}
            {renderSeparator()}
          </>
        )}
        {renderDigit(time.minutes)}
        {renderSeparator()}
        {renderDigit(time.seconds)}
      </View>
    </View>
  );
}
