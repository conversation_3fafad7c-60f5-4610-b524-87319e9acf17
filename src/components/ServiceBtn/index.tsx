import { Image, View } from "@tarojs/components";
import Style from "./index.module.scss";
import Taro from "@tarojs/taro";

const ServiceBtn = () => {
  return (
    <View
      className={Style.btn}
      onClick={() => {
        Taro.openCustomerServiceChat({
          extInfo: {
            url: `https://work.weixin.qq.com/kfid/kfcc1793a8a8dbee924`,
          },
          corpId: "ww6ff0ba55cb7300f4",
          success: () => console.log("open service succ"),
        });
      }}
    >
      <Image
        className={Style.img}
        src="https://vip.gaomei168.com/file-release/file/100/2024/0131/15/1202276434215542785_size_148802.gif"
      ></Image>
      {/* <Button openType="contact" className={Style.hide}></Button> */}
    </View>
  );
};

export default ServiceBtn;
