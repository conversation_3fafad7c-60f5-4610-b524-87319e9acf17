import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useMount, useSetState } from "ahooks";
import { useEffect, useId, useRef } from "react";
import { UnoIcon } from "../UnoIcon";

const arr = [1, 2, 3, 4, 5];

export const Rate = (props: {
  value?: number;
  onChange?: (n: number) => any;
  size?: number;
  gap?: number;
  disabled?: boolean;
  color1?: string;
  color2?: string;
}) => {
  const uid = useId();
  const ref = useRef<any>(null);
  const [state, setState] = useSetState({ w: 0, l: 0, v: 3.5, sx: 0 });

  const size = props.size ?? 36;
  const gap = props.gap ?? 15;

  useMount(() => getRect());

  useEffect(() => {
    let v = props.value || 0;
    v = Math.round(v);
    v = Math.max(0, Math.min(5, v));
    setState({ v });
  }, [props.value]);

  const getRect = () => {
    Taro.createSelectorQuery()
      .select("#star" + uid)
      .boundingClientRect()
      .exec((res) => {
        const w = res?.[0]?.width;
        const l = res?.[0]?.left;
        setState({ w, l });
      });
  };

  const getVal = (e: any) => {
    const t = e?.touches?.[0] || e?.changedTouches?.[0];
    const x = t?.pageX || 0;
    let v = ((x - state.l) / state.w) * 5;
    v = Math.round(v);
    v = Math.max(0, Math.min(5, v));
    return v;
  };

  const onTouchStart = (e) => {
    if (props.disabled) return;
    getRect();
  };

  const onTouchMove = (e) => {
    if (props.disabled) return;
    const v = getVal(e);
    setState({ v });
  };

  const onTouchEnd = (e) => {
    if (props.disabled) return;
    const v = getVal(e);
    setState({ v });
    props.onChange?.(v);
  };

  return (
    <View
      ref={ref}
      id={"star" + uid}
      className="inline-grid cols-5 gap-15"
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
      style={{ gap: gap + "rpx" }}
    >
      {arr.map((n) => (
        <View
          className="pos-relative size-36"
          style={{ width: size + "rpx", height: size + "rpx" }}
        >
          <UnoIcon
            key={n}
            className="pos-absolute top-0 left-0 i-icon-park-solid:star size-full text-gray"
            style={{ color: props.color2 }}
          />
          {state.v >= n && (
            <UnoIcon
              key={n}
              className="pos-absolute top-0 left-0 i-icon-park-solid:star size-full text-#E9A868"
              style={{ color: props.color1 }}
            />
          )}
        </View>
      ))}
    </View>
  );
};
