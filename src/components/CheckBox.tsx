import { View } from "@tarojs/components";
import { UnoIcon } from "./UnoIcon";

export const CheckBox = (props: { className?: string; checked?: boolean }) => {
  return (
    <View className={props.className}>
      {props.checked ? (
        <UnoIcon className="i-icon-park-outline:check-one size-full color-inherit"></UnoIcon>
      ) : (
        <UnoIcon className="i-icon-park-outline:round size-full color-inherit"></UnoIcon>
      )}
    </View>
  );
};
