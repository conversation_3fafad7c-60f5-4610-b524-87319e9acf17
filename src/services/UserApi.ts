import { makeApi } from "@/utils/request";

export const getOpenId = makeApi("get", `/api/wxApi/wxs/query_openid`);

export const getUserByOpenid = makeApi(
  "get",
  `/api/appUser/wx/query_user_by_openid`
);

export const getUserByToken = makeApi("get", `/api/appUser/query_user`);

export const updateUser = makeApi("post", `/api/appUser/update_user`);

export const registUser = makeApi("post", "/api/appUser/wx/regist_by_weixin");

export const createToken = makeApi("get", `/api/token/create_token`);

export const wx_getOpenid = makeApi("get", `/api/wxApi/query_openid`);
