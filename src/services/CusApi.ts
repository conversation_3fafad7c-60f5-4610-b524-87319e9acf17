import { makeApi } from "@/utils/request";

const CusApi = {
  getUserInfo: makeApi("get", `/api/mallShopVipUser/query_vip_user`),
  putUserInfo: makeApi("post", `/api/mallShopVipUser/update_vip_user`),
  getDict: makeApi("get", `/api/sysDictionarydata/query_dictionarydata_list`),

  // 优惠券
  getCoupon: makeApi("get", `/api/appUserCoupon/query_list`),
  getMallCoupon: makeApi("get", `/api/mallCoupon/query_list`),
  obtainMallCoupon: makeApi("post", `/api/mallCoupon/obtain`),

  getAdv: makeApi("get", `/api/mallIndexAdv/query_list`),

  getShops: makeApi("get", `/api/mallShop/query_list`),
  getShopInfo: makeApi("get", `/api/mallShop/query_one`),

  getGoods: makeApi("get", `/api/v2/mallProduct/query_list`),
  getGoodsInfo: makeApi("get", `/api/v2/mallProduct/query_one`),
  addGoodsView: makeApi("post", `/api/mallProduct/view`),

  getGoodsSkuShop: makeApi("get", `/api/v2/mallProduct/query_available_shop`),
  getGoodsSkuCalc: makeApi("post", `/api/mallProductSpecification/check_price`),

  getCates: makeApi("get", `/api/mallProductCategory/query_list`),

  // 订单创建 & 支付
  getOrderCoupon: makeApi("post", `/api/mallOrder/check_preferential`),
  getOrderMoney: makeApi("post", `/api/mallOrder/check_order`),
  createOrder: makeApi("post", `/api/mallOrder/create_order`),
  payOrderZero: makeApi("post", `/api/mallOrder/zeropay`),
  payOrder: makeApi("post", `/api/mallOrder/balance_pay`),
  preOrder: makeApi("post", `/api/mallOrder/wx/pre_buy`),

  // 订单详情
  getOrderList: makeApi("get", `/api/mallOrder/query_list`),
  getOrderInfo: makeApi("get", `/api/mallOrder/query_one`),
  getOrderCount: makeApi("get", `/api/mallOrder/query_state_total`),

  // 订单操作
  putOrderClose: makeApi("post", `/api/mallOrder/close_order`),
  putOrderDel: makeApi("post", `/api/mallOrder/delete_order`),
  putOrderRefund: makeApi("post", `/api/mallOrder/apply_refund`),
  putOrderRefundCancel: makeApi("post", `/api/mallOrder/cancel_refund`),
  putOrderRate: makeApi("post", `/api/mallOrder/evaluation_order`),
  putOrderConfirm: makeApi("post", `/api/mallOrder/receive_order`),
  // getOrderRate: makeApi("get", `/api/mallOrder/query_evaluation_list`),
  getOrderComment: makeApi("get", `/api/mallProduct/query_evaluation_list`),

  // ===== 分销
  getCardNoList: makeApi("get", `/api/mallCardNo/query_list`),
  bindCardNo: makeApi("post", `/api/mallCardNo/bind_card`),
  getCardNo: makeApi("get", `/api/mallCardNo/query_one`),
  getAgentUser: makeApi("get", `/api/mallShopVipUser/agent_query_list`),
  getAgentLower: makeApi("get", `/api/sysOrganize/query_child_list`),
  useCardNo: makeApi("post", `/api/mallCardNo/use_card`),
  getAgentIncome: makeApi("get", `/api/mallCommissionLog/agent_query_count`),
  getDistIncome: makeApi(
    "get",
    `/api/mallCommissionLog/distribution_query_count`
  ),
  getIncomeList: makeApi("get", `/api/mallCommissionLog/query_list`),
  getIncomeListStat: makeApi("get", `/api/mallCommissionLog/query_list_count`),

  // 投资人
  getInvestorShop: makeApi("get", `/api/mallShop/investor_query_shop_list`),
  getInvestorStat: makeApi("get", `/api/mallShop/investor_query_count`),
  getInvestorOrder: makeApi("get", `/api/mallShop/query_shop_order_list`),
  getInvestorOrderStat: makeApi("get", `/api/mallShop/query_shop_order_count`),
  getInvestorUser: makeApi("get", `/api/mallShopInvestor/query_list`),

  getMyTreat: makeApi("get", `/api/mallOrderProject/query_my_treat_item_list`),
  addWithdraw: makeApi("post", `/api/accountWithdraw/add`),

  // 卖套餐卡
  buyCard1: makeApi("post", `/api/mallOrder/create_order_card_type`),
  getCard1: makeApi("get", `/api/mallCardType/query_sell_first`),
  buyCard2: makeApi("post", `/api/mallOrder/create_order_shop_vip`),
  getCard2: makeApi("get", `/api/mallShopVipLevel/query_sell_first`),
  getMyCard: makeApi("get", `/api/mallCardNo/query_my_list`),

  //会员任务
  getMember: makeApi("get", `/api/mallVipTask/query_my_task`),
  getComplete: makeApi("get", "/api/mallVipTaskLog/query_list"),

  //盲盒管理
  getQueryPrizeList: makeApi(
    "get",
    `/api/mallBlindBox/blind_box/query_prize_list`
  ),
  getMyList: makeApi("get", `/api/mallBlindBox/query_own_my_list`),

  // 盲盒相关
  getBlindBoxList: makeApi("get", `/api/mallBlindBox/query_list`),
  getBlindBoxInfo: makeApi("get", `/api/mallBlindBox/query_one`),
  getBlindBox: makeApi("post", `/api/mallBlindBox/receive`),
  openBlindBox: makeApi("post", `/api/mallBlindBox/use_blind_box`),
  getPrizeList: makeApi("get", `/api/mallBlindBox/turntable/query_prize_list`),
  getPrize: makeApi("post", `/api/mallBlindBox/turntable/lottery`),
  sendCardGift: makeApi("post", `/api/mallCardNo/gift`),
  getCardGift: makeApi("post", `/api/mallCardNo/receive`),
  getBlindBoxStat: makeApi("get", `/api/mallBlindBox/query_count`),

  // 余额积分统计提现相关
  getWalletStat: makeApi("get", `/api/appUserBalanceLog/query_count`),
  getWalletList: makeApi("get", `/api/appUserBalanceLog/query_list`),
  addWalletDraw: makeApi("post", `/api/accountWithdraw/add`),

  //查询我推荐的用户
  getRecommend: makeApi("get", `/api/appUser/query_my_recommend`),

  // doctor
  getDoctor: makeApi("get", `/api/mallDoctor/query_list`),
  getDoctorInfo: makeApi("get", `/api/mallDoctor/query_one`),

  getArtCate: makeApi("get", `/api/mallInformationCategory/query_list`),
  getArticleList: makeApi("get", `/api/mallInformationArticle/query_list`),
  getArticleInfo: makeApi("get", `/api/mallInformationArticle/query_one`),

  getDoctorPrice: makeApi("get", `/api/mallProductServicer/query_list`),

  // 购物车
  addCart: makeApi("post", `/api/mallShoppingCart/add`),
  delCart: makeApi("delete", `/api/mallShoppingCart/delete`),
  getCart: makeApi("get", `/api/mallShoppingCart/query_list`),
  putCart: makeApi("post", `/api/mallShoppingCart/update`),

  // 支付
  ckSave: makeApi("post", `/api/mallOrder/check_preferential`),
  ckTotal: makeApi("post", `/api/mallOrder/check_order`),
  // createOrder: makeApi("", ``),

  // 商品下门店医生
  getGoodsShop: makeApi("get", `/api/mallProductOutpatientFee/query_list`),

  getQrcode: makeApi("post", `/api/wxApi/wxs/create_qr_code`),

  // 集卡
  getJkStat: makeApi("get", `/api/mallCollectCardLog/query_total_list`),
  getJKlist: makeApi(
    "get",
    `/api/mallCollectCardType/turntable/query_prize_list`
  ),
  getJkStart: makeApi("post", `/api/mallCollectCardType/turntable/lottery`),
  getJkRank: makeApi("get", `/api/mallCollectCardLog/query_ranking`),
  getJkRule: makeApi("get", `/api/mallCollectCardType/query_rule`),
  doJKGive: makeApi("post", `/api/mallCollectCardLog/transfer`),
  doJKGet: makeApi("post", `/api/mallCollectCardLog/receive`),
  doJKShare: makeApi("post", `/api/appUserEvent/share/collect_activity`),

  // 签到
  getSignRule: makeApi("get", `/api/appUserSignConfig/query_rule`),
  getSignList: makeApi("get", `/api/appUserSignConfig/query_sign_panel`),
  getSignStart: makeApi("get", `api/appUserSignConfig/sign`),

  getMarketTypes: makeApi("get", `/api/mallMarketProductCategory/query_list`),
  getMarketProduct: makeApi("get", `/api/mallMarketProduct/query_list`),
  getMarketProductInfo: makeApi("get", `/api/mallMarketProduct/query_one`),
  getMarketProductRule: makeApi("get", `/api/mallMarketProduct/query_rule`),
  addMarketOrder: makeApi("post", `/api/mallMarketOrder/create_order`),
  getMarketQueueId: makeApi("post", `/api/mallMarketOrder/get_queue_id`),

  // 医生评论
  getDocComment: makeApi("get", `/api/mallDoctorComment/query_list`),
  addDocComment: makeApi("post", `/api/mallDoctorComment/add`),

  // 文章相关
  getNote: makeApi("get", `/api/appInformationArticle/web/query_list`),
  getNoteInfo: makeApi("get", `/api/appInformationArticle/web/query_one`),
  addNote: makeApi("post", `/api/appInformationArticle/add`),
  putNote: makeApi("post", `/api/appInformationArticle/modify`),
  delNote: makeApi("post", `/api/appInformationArticle/delete`),

  getNoteCates: makeApi("get", `/api/appInformationCategory/query_list`),

  getNComment: makeApi("get", `/api/appInformationArticleComment/query_list`),
  addNComment: makeApi("post", `/api/appInformationArticleComment/add`),

  addNoteAct: makeApi("post", `/api/appInformationArticleInteraction/add`),
  delNoteAct: makeApi("post", `/api/appInformationArticleInteraction/cancel`),

  // 预约
  getDueDoctor: makeApi("get", `/api/appUserReservation/select_doctor_list`),
  getDueProject: makeApi("get", `/api/appUserReservation/select_project_list`),
  getDueTimes: makeApi("get", `/api/appUserReservationDate/query_date_list`),
  postDue: makeApi("post", `/api/appUserReservation/apply`),
  getDueList: makeApi("get", `/api/appUserReservation/query_list`),
  getDueInfo: makeApi("get", `/api/appUserReservation/query_one`),
  cancelDue: makeApi("post", `/api/appUserReservation/cancel`),

  // 预约管理
  getAdminShops: makeApi("get", `/api/mallShop/query_manage_shop_list`),
  getAdminAuth: makeApi("get", `/api/appUserPanelPower/query_power`),
  getAdminDueList: makeApi("get", `/api/appUserReservation/shop/query_list`),
  addAdminDue: makeApi("post", `/api/appUserReservation/shop/apply`),

  // 反馈
  postFeed: makeApi("post", `/api/appFeedback/submit_feedback`),
  getFeed: makeApi("get", `/api/appFeedback/query_list`),
  getFeedInfo: makeApi("get", `api/appFeedback/query_one`),
  delFeed: makeApi("delete", `/api/appFeedback/delete`),

  // 团购
  getGroup: makeApi("get", `/api/mallGroup/query_list`),
  getGrouping: makeApi("get", `/api/mallGroup/product/query_list`),
  getGroupInfo: makeApi("get", `/api/mallGroup/query_one`),
  // getGroupTeam: makeApi("get", ``),
  checkGroupOrder: makeApi("post", `/api/mallMarketOrder/check_order`),
  createGroupOrder: makeApi("post", `/api/mallMarketOrder/create_order`),
  makeAdminGroup: makeApi("post", `/api/mallGroup/open_group`),

  // 核销
  getVerifyOrder: makeApi("get", `/api/mallOrder/shop/verify_order_query`),
  postVerifyOrder: makeApi("post", `/api/mallOrder/shop/verify_order_submit`),

  getServiceStat: makeApi("get", `/api/mallServiceOrder/query_state_total`),

  // vip
  getVipLv: makeApi("get", `/api/mallShopVipLevel/user/query_list`),

  pageStat: makeApi("post", `/api/appUserBrowseLog/submit`),
};

export default CusApi;
