import Taro, { useRouter, useShareAppMessage } from "@tarojs/taro";
import { img_share } from "@/utils/images";
import { useMount } from "ahooks";
import axios from "axios";

export const useShare = (fn?: any) => {
  const router = useRouter();

  useMount(() => {
    const code = router.params.promotionUserCode;
    if (code) {
      Taro.setStorageSync("promotionUserCode", code);
    }
  });

  useShareAppMessage(async (...args) => {
    const user = Taro.getStorageSync("user");
    const code = user?.serialNo || "";

    const res = await fn(...args);

    const base = {
      title: "搞美医疗数字诊所",
      imageUrl: img_share,
      path: `/pages/tabs/mall/index`,
    };

    const config = { ...base, ...res };
    config.path = axios.getUri({
      url: config.path,
      params: { promotionUserCode: code },
    });

    return config;
  });
};
