import CusApi from "@/services/CusApi";
import Taro, {
  useDidHide,
  useDidShow,
  useRouter,
  useUnload,
} from "@tarojs/taro";
import { useSetState } from "ahooks";
import dayjs from "dayjs";

export const usePageStat = (props: { path?: any; id: any; title: any }) => {
  const router = useRouter();
  const [state, setState] = useSetState({
    start: null as any,
  });

  useDidShow(() => {
    setState({ start: dayjs().format("YYYY-MM-DD HH:mm:ss") });
  });

  useDidHide(async () => {
    onLeave();
  });

  useUnload(() => {
    onLeave();
  });

  const onLeave = async () => {
    const token = Taro.getStorageSync("token");
    if (!token) return;

    await CusApi.pageStat({
      wxAppId: process.env.TARO_APP_ID,
      pageData: [
        {
          wxPagePath: router.path,
          keyId: props.id,
          title: props.title,
          entryDate: state.start,
          exitDate: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        },
      ],
    });
  };
};
