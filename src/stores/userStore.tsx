import Taro from "@tarojs/taro";
import { create } from "zustand";
import {
  persist,
  combine,
  StateStorage,
  createJSONStorage,
} from "zustand/middleware";

const storage: StateStorage = {
  getItem: (name: string) => Taro.getStorageSync(name),
  setItem: (name: string, value: string) => Taro.setStorageSync(name, value),
  removeItem: (name: string) => Taro.removeStorageSync(name),
};

const useUserStore = create(
  persist(
    combine(
      {
        token: "",
        auth: null as any,
        shop: null as any,
      },
      (set) => ({
        setToken: (token) => set({ token }),
        setShop: (shop: any) => set({ shop }),
        setAuth: (auth) => set({ auth }),
      })
    ),
    { name: "store", storage: createJSONStorage(() => storage) }
  )
);

export default useUserStore;
