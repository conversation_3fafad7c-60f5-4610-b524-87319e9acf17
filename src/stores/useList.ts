import { useSetState } from "ahooks";

export const useList = (props: {
  api: any;
  size?: number;
  params?: any;
  keep?: boolean;
  formater?: any;
}) => {
  const [state, setState] = useSetState({
    list: [] as any[],
    page: 1,
    size: 20,
    loading: false,
    more: true,
    empty: false,
    total: 0,

    params: {} as any,
  });

  const getList = async (page = 1) => {
    if (page !== 1 && (state.loading || !state.more)) return;
    if (page === 1) {
      const keep = props.keep ?? true;
      setState((p) => ({
        list: keep ? p.list : [],
        more: true,
        empty: false,
      }));
    }

    try {
      setState({ loading: true });

      const res = await props.api({
        pageNum: page,
        pageSize: props.size || state.size,
        ...props.params,
      });

      setState((prev) => {
        const formater = props.formater || ((p) => p);
        const clist = (res?.list || []).map((n) => formater(n));
        const plist = page === 1 ? [] : prev.list;

        return {
          list: [...plist, ...clist],
          page: res.pageNum,
          size: res.pageSize,
          more: res.hasNextPage,
          total: res?.total,
          empty: !res.total,
        };
      });
    } finally {
      setState({ loading: false });
    }
  };

  const reload = () => getList(1);
  const getNext = () => getList(state.page + 1);

  return [state, { getList, reload, getNext, setState }] as const;
};
