{"name": "mp-g<PERSON><PERSON>", "version": "1.0.0", "private": true, "description": "gao<PERSON>", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"build:weapp": "taro build --type weapp --mode dev", "build:prod": "taro build --type weapp --mode prod", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "test": "jest"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.22.15", "@taroify/core": "^0.8.0", "@tarojs/components": "4.0.9", "@tarojs/helper": "4.0.9", "@tarojs/plugin-framework-react": "4.0.9", "@tarojs/plugin-html": "4.0.9", "@tarojs/plugin-http": "4.0.9", "@tarojs/plugin-inject": "4.0.9", "@tarojs/plugin-platform-alipay": "4.0.9", "@tarojs/plugin-platform-h5": "4.0.9", "@tarojs/plugin-platform-jd": "4.0.9", "@tarojs/plugin-platform-qq": "4.0.9", "@tarojs/plugin-platform-swan": "4.0.9", "@tarojs/plugin-platform-tt": "4.0.9", "@tarojs/plugin-platform-weapp": "4.0.9", "@tarojs/react": "4.0.9", "@tarojs/runtime": "4.0.9", "@tarojs/shared": "4.0.9", "@tarojs/taro": "4.0.9", "abortcontroller-polyfill": "^1.7.5", "ahooks": "^3.7.8", "axios": "^1.5.0", "clsx": "^2.0.0", "dayjs": "^1.11.10", "entities": "^5.0.0", "js-base64": "^3.7.5", "react": "^18.2.0", "react-dom": "^18.2.0", "weapp-qrcode": "^1.0.0", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.22.15", "@babel/preset-react": "^7.26.3", "@iconify-json/icon-park-outline": "^1.2.1", "@iconify-json/icon-park-solid": "^1.2.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.11", "@tarojs/cli": "4.0.9", "@tarojs/taro-loader": "4.0.9", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "4.0.9", "@types/jest": "^29.5.4", "@types/node": "^18.17.14", "@types/react": "^18.2.21", "@types/webpack-env": "^1.18.1", "@typescript-eslint/eslint-plugin": "^6.6.0", "@typescript-eslint/parser": "^6.6.0", "@unocss/preset-icons": "^0.63.6", "@unocss/webpack": "0.58", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "4.0.9", "eslint": "^8.48.0", "eslint-config-taro": "4.0.9", "eslint-plugin-import": "^2.28.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.6.4", "jest-environment-jsdom": "^29.6.4", "postcss": "^8.4.29", "react-refresh": "^0.11.0", "stylelint": "^14.16.1", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.2.2", "unocss": "0.58", "unocss-preset-weapp": "^0.59.1", "webpack": "5.78.0"}}