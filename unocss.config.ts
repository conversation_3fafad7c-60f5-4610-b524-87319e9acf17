import { AnyARecord } from "dns";
import { defineConfig, presetIcons, transformerVariantGroup } from "unocss";
import presetWeapp from "unocss-preset-weapp";
import { transformerClass } from "unocss-preset-weapp/transformer";

const transformRules = {
  ".": "_dl_",
  "/": "_sl_",
  "\\": "_sr_",
  ":": "_cl_",
  "%": "_pes_",
  "!": "_el_",
  "#": "_wn_",
  "(": "_lbl_",
  ")": "_lbr_",
  "[": "_lfl_",
  "]": "_lfr_",
  $: "_do_",
  ",": "_lco_",
  "=": "_eqe_",
  "&": "_my_",
};

export default defineConfig({
  presets: [
    presetIcons(),
    presetWeapp({
      whRpx: false,
      transformRules,
    }) as any,
  ],
  transformers: [
    transformerVariantGroup(),
    transformerClass({ transformRules }) as any,
  ],
  postprocess: (css) => {
    css.entries.forEach((i) => {
      const remRE = /(-?[\.\d]+)rpx/g;
      const name = i[0];
      const value = i[1];

      if (typeof value === "string" && remRE.test(value)) {
        if (/^border(.*)width$/.test(name)) return;
        i[1] = value.replace(remRE, (_, p1) => `${p1 / 8}rpx`);
      }
    });
  },
  theme: {
    colors: {
      brand: "var(--color-primary)",
    },
    borderRadius: {
      DEFAULT: "10rpx",
    },

    animation: {
      keyframes: {
        l2r: "{0% { transform: translateX(0); } 100% { transform: translateX(-100%); }}",
      },
      durations: {
        l2r: "20s",
      },
      timingFns: {
        l2r: "linear",
      },
      counts: {
        l2r: "infinite",
      },
    },
  },
});
