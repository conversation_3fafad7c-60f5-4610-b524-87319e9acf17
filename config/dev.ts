import type { UserConfigExport } from "@tarojs/cli";

const openCompress = true;

export default {
  logger: {
    quiet: false,
    stats: true,
  },
  mini: {
    webpackChain: (chain, webpack) => {
      if (openCompress) {
        chain.merge({
          plugin: {
            install: {
              plugin: require("terser-webpack-plugin"),
              args: [
                {
                  terserOptions: {
                    compress: true, // 默认使用terser压缩
                    // mangle: false,
                    keep_classnames: true, // 不改变class名称
                    keep_fnames: true, // 不改变函数名称
                  },
                },
              ],
            },
          },
        });
      }
    },
  },
  h5: {},
} satisfies UserConfigExport;
