import { defineConfig, type UserConfigExport } from "@tarojs/cli";
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import devConfig from "./dev";
import prodConfig from "./prod";
import UnoCSS from "unocss/webpack";

process.env.BROWSERSLIST_IGNORE_OLD_DATA = "1";

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig(async (merge) => {
  const baseConfig: UserConfigExport = {
    alias: {
      "@tarojs/runtime": require.resolve("@tarojs/runtime"),
    },

    projectName: "mp-gaomei",
    date: "2023-9-7",
    // designWidth: 750,
    designWidth(input: any) {
      if (input?.file?.replace(/\\+/g, "/").indexOf("@taroify/core") > -1) {
        return 750;
      }
      // 全局使用 Taro 默认的 750 尺寸
      return 1080;
    },
    deviceRatio: {
      640: 2.34 / 2,
      750: 1,
      375: 2,
      828: 1.81 / 2,
      1080: 750 / 1080,
    },
    sourceRoot: "src",
    outputRoot: require("os").release().includes("microsoft")
      ? "/mnt/d/works/weapp/gaomei"
      : "dist",
    // outputRoot: "dist",
    plugins: [
      "@tarojs/plugin-html",
      "@tarojs/plugin-http",
      [
        "@tarojs/plugin-inject",
        {
          components: {
            Button: {
              "data-share": "'dataShare'",
            },

            // View: {
            //   "data-data": "'dataData'",
            // },

            ScrollView: {
              "scroll-into-view-offset": "",
            },

            Video: {
              /**
               * 临时解决taro bug
               * @fix https://github.com/NervJS/taro/issues/16903
               * @todo ldl remove
               */
              controls: "",
            },
          },
        },
      ],
    ],
    defineConstants: {},
    copy: {
      patterns: [],
      options: {},
    },
    framework: "react",
    compiler: {
      type: "webpack5",
      prebundle: { enable: false },
    },
    cache: {
      enable: false, // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
    },
    mini: {
      miniCssExtractPluginOption: {
        ignoreOrder: true,
      },
      postcss: {
        pxtransform: {
          enable: true,
          config: {},
        },
        url: {
          enable: true,
          config: {
            limit: 1024, // 设定转换尺寸上限
          },
        },
        cssModules: {
          enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: "module", // 转换模式，取值为 global/module
            generateScopedName: "[name]__[local]___[hash:base64:5]",
          },
        },
      },
      webpackChain(chain) {
        chain.resolve.plugin("tsconfig-paths").use(TsconfigPathsPlugin);
        chain.plugin("unocss").use(UnoCSS());
      },
    },
    h5: {
      publicPath: "/",
      staticDirectory: "static",
      output: {
        filename: "js/[name].[hash:8].js",
        chunkFilename: "js/[name].[chunkhash:8].js",
      },
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: "css/[name].[hash].css",
        chunkFilename: "css/[name].[chunkhash].css",
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {},
        },
        cssModules: {
          enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: "module", // 转换模式，取值为 global/module
            generateScopedName: "[name]__[local]___[hash:base64:5]",
          },
        },
      },
      webpackChain(chain) {
        chain.resolve.plugin("tsconfig-paths").use(TsconfigPathsPlugin);
      },
    },
    rn: {
      appName: "taroDemo",
      postcss: {
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        },
      },
    },
  };
  if (process.env.NODE_ENV === "development") {
    // 本地开发构建配置（不混淆压缩）
    return merge({}, baseConfig, devConfig);
  }
  // 生产构建配置（默认开启压缩混淆等）
  return merge({}, baseConfig, prodConfig);
});
